"""
优化效果监控和反馈机制

提供优化效果跟踪、统计分析和改进建议，形成闭环反馈系统。
"""

import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import json
from pathlib import Path

logger = logging.getLogger(__name__)


class OptimizationEffectivenessMonitor:
    """
    优化效果监控器
    
    跟踪和分析提示词优化的效果，提供数据驱动的改进建议。
    """
    
    def __init__(self, log_file: str = "optimization_log.json"):
        """
        初始化监控器
        
        Args:
            log_file: 日志文件路径
        """
        self.log_file = Path(log_file)
        self.optimization_history = []
        self.success_metrics = {}
        self._load_history()
    
    def _load_history(self):
        """加载历史记录"""
        if self.log_file.exists():
            try:
                with open(self.log_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.optimization_history = data.get('history', [])
                    self.success_metrics = data.get('metrics', {})
                logger.info(f"Loaded {len(self.optimization_history)} optimization records")
            except Exception as e:
                logger.warning(f"Failed to load optimization history: {e}")
    
    def _save_history(self):
        """保存历史记录"""
        try:
            # 确保目录存在
            self.log_file.parent.mkdir(parents=True, exist_ok=True)
            
            data = {
                'history': self.optimization_history,
                'metrics': self.success_metrics,
                'last_updated': datetime.now().isoformat()
            }
            with open(self.log_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"Failed to save optimization history: {e}")
    
    def track_optimization(self, original_prompt: str, optimized_prompt: str, 
                          optimization_type: str, result_quality: Optional[Dict] = None):
        """
        跟踪优化效果
        
        Args:
            original_prompt: 原始提示词
            optimized_prompt: 优化后提示词
            optimization_type: 优化类型
            result_quality: 结果质量评估
        """
        if result_quality is None:
            result_quality = {}
            
        optimization_record = {
            "timestamp": datetime.now().isoformat(),
            "original_prompt": original_prompt,
            "optimized_prompt": optimized_prompt,
            "optimization_type": optimization_type,
            "before_quality": result_quality.get("before", {}),
            "after_quality": result_quality.get("after", {}),
            "improvement": self._calculate_improvement(result_quality),
            "prompt_length_change": len(optimized_prompt) - len(original_prompt),
            "complexity_reduction": self._estimate_complexity_reduction(original_prompt, optimized_prompt)
        }
        
        self.optimization_history.append(optimization_record)
        self._update_success_metrics(optimization_record)
        self._save_history()
        
        logger.info(f"Optimization tracked: {optimization_type}, improvement: {optimization_record['improvement']:.2f}")
    
    def _calculate_improvement(self, result_quality: Dict) -> float:
        """计算改进程度"""
        before = result_quality.get("before", {})
        after = result_quality.get("after", {})
        
        if not before or not after:
            return 0.0
        
        before_score = before.get("overall", 0)
        after_score = after.get("overall", 0)
        
        return after_score - before_score
    
    def _estimate_complexity_reduction(self, original: str, optimized: str) -> float:
        """估算复杂度降低程度"""
        # 简单的复杂度估算：基于长度、模糊词汇等
        original_complexity = self._calculate_text_complexity(original)
        optimized_complexity = self._calculate_text_complexity(optimized)
        
        return original_complexity - optimized_complexity
    
    def _calculate_text_complexity(self, text: str) -> float:
        """计算文本复杂度"""
        # 基于多个因素的复杂度计算
        factors = {
            "length": len(text.split()) / 100,  # 长度因子
            "ambiguous_words": self._count_ambiguous_words(text),
            "technical_terms": self._count_technical_terms(text),
            "sentence_count": text.count('。') + text.count('？') + text.count('！')
        }
        
        return sum(factors.values()) / len(factors)
    
    def _count_ambiguous_words(self, text: str) -> float:
        """计算模糊词汇数量"""
        ambiguous_words = ["一些", "几个", "大概", "差不多", "随便", "什么的", "帮我", "搞一下"]
        count = sum(1 for word in ambiguous_words if word in text)
        return min(count / 5, 1.0)
    
    def _count_technical_terms(self, text: str) -> float:
        """计算技术术语数量"""
        technical_terms = ["API", "JSON", "CSS", "XPath", "DOM", "HTTP", "URL", "HTML"]
        count = sum(1 for term in technical_terms if term.lower() in text.lower())
        return min(count / 5, 1.0)
    
    def _update_success_metrics(self, record: Dict):
        """更新成功指标"""
        opt_type = record["optimization_type"]
        
        if opt_type not in self.success_metrics:
            self.success_metrics[opt_type] = {
                "count": 0,
                "total_improvement": 0,
                "success_rate": 0,
                "avg_complexity_reduction": 0
            }
        
        metrics = self.success_metrics[opt_type]
        metrics["count"] += 1
        metrics["total_improvement"] += record["improvement"]
        
        # 计算成功率（改进 > 0.5 算成功）
        successful_optimizations = sum(1 for r in self.optimization_history 
                                     if r["optimization_type"] == opt_type and r["improvement"] > 0.5)
        metrics["success_rate"] = successful_optimizations / metrics["count"]
        
        # 计算平均复杂度降低
        complexity_reductions = [r["complexity_reduction"] for r in self.optimization_history 
                               if r["optimization_type"] == opt_type]
        metrics["avg_complexity_reduction"] = sum(complexity_reductions) / len(complexity_reductions) if complexity_reductions else 0
    
    def get_optimization_insights(self) -> Dict:
        """获取优化洞察"""
        if not self.optimization_history:
            return {"message": "暂无优化数据"}
        
        recent_optimizations = self.optimization_history[-10:]  # 最近10次
        
        insights = {
            "total_optimizations": len(self.optimization_history),
            "recent_average_improvement": sum(r["improvement"] for r in recent_optimizations) / len(recent_optimizations),
            "most_effective_strategy": self._get_most_effective_strategy(),
            "optimization_frequency": self._calculate_optimization_frequency(),
            "success_metrics": self.success_metrics,
            "trends": self._analyze_trends(),
            "recommendations": self._generate_recommendations()
        }
        
        return insights
    
    def _get_most_effective_strategy(self) -> str:
        """获取最有效的优化策略"""
        if not self.success_metrics:
            return "insufficient_data"
        
        # 综合考虑成功率和改进程度
        best_strategy = max(self.success_metrics.items(), 
                          key=lambda x: x[1]["success_rate"] * (x[1]["total_improvement"] / x[1]["count"]))
        return best_strategy[0]
    
    def _calculate_optimization_frequency(self) -> float:
        """计算优化频率"""
        if len(self.optimization_history) < 2:
            return 0.0
        
        # 计算最近一周的优化频率
        one_week_ago = datetime.now() - timedelta(days=7)
        recent_week = [r for r in self.optimization_history 
                      if datetime.fromisoformat(r["timestamp"]) > one_week_ago]
        
        return len(recent_week) / 7  # 每天平均优化次数
    
    def _analyze_trends(self) -> Dict:
        """分析优化趋势"""
        if len(self.optimization_history) < 5:
            return {"message": "数据不足以分析趋势"}
        
        recent_10 = self.optimization_history[-10:]
        previous_10 = self.optimization_history[-20:-10] if len(self.optimization_history) >= 20 else []
        
        trends = {}
        
        if previous_10:
            recent_avg = sum(r["improvement"] for r in recent_10) / len(recent_10)
            previous_avg = sum(r["improvement"] for r in previous_10) / len(previous_10)
            trends["improvement_trend"] = "improving" if recent_avg > previous_avg else "declining"
            trends["improvement_change"] = recent_avg - previous_avg
        
        # 分析优化类型趋势
        recent_types = [r["optimization_type"] for r in recent_10]
        type_counts = {}
        for t in recent_types:
            type_counts[t] = type_counts.get(t, 0) + 1
        trends["popular_optimization_types"] = sorted(type_counts.items(), key=lambda x: x[1], reverse=True)
        
        return trends
    
    def _generate_recommendations(self) -> List[str]:
        """生成优化建议"""
        recommendations = []
        
        insights = self.get_optimization_insights()
        
        # 基于改进效果的建议
        if insights["recent_average_improvement"] < 0.3:
            recommendations.append("建议调整优化策略，当前改进效果不明显")
        
        # 基于频率的建议
        if insights["optimization_frequency"] > 2:
            recommendations.append("优化频率过高，建议提高触发阈值")
        elif insights["optimization_frequency"] < 0.1:
            recommendations.append("优化频率过低，建议降低触发阈值")
        
        # 基于最有效策略的建议
        most_effective = insights.get("most_effective_strategy")
        if most_effective and most_effective != "insufficient_data":
            recommendations.append(f"建议优先使用 {most_effective} 策略")
        
        # 基于趋势的建议
        trends = insights.get("trends", {})
        if trends.get("improvement_trend") == "declining":
            recommendations.append("优化效果呈下降趋势，建议检查优化策略")
        
        return recommendations
    
    def export_report(self, file_path: str = None) -> str:
        """导出优化报告"""
        if file_path is None:
            file_path = f"optimization_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        report = {
            "generated_at": datetime.now().isoformat(),
            "insights": self.get_optimization_insights(),
            "detailed_history": self.optimization_history[-50:],  # 最近50条记录
            "summary": {
                "total_optimizations": len(self.optimization_history),
                "success_rate": sum(1 for r in self.optimization_history if r["improvement"] > 0.5) / len(self.optimization_history) if self.optimization_history else 0,
                "average_improvement": sum(r["improvement"] for r in self.optimization_history) / len(self.optimization_history) if self.optimization_history else 0
            }
        }
        
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            logger.info(f"Optimization report exported to {file_path}")
            return file_path
        except Exception as e:
            logger.error(f"Failed to export report: {e}")
            return ""
