"""
Prompt optimization tools for iICrawlerMCP.

This module provides specialized tools for analyzing and optimizing user prompts
to improve task execution effectiveness.
"""

import logging
from langchain_core.tools import tool
from typing import List
from langchain_core.tools import BaseTool

logger = logging.getLogger(__name__)


@tool
def analyze_prompt_clarity(prompt: str) -> str:
    """
    分析提示词的清晰度和明确性。
    
    检查提示词是否包含模糊表述、缺失信息或不明确的要求。
    
    Args:
        prompt: 需要分析的提示词
        
    Returns:
        清晰度分析结果和改进建议
        
    Example:
        analyze_prompt_clarity("帮我爬取网站数据")
        analyze_prompt_clarity("提取商品信息并保存到文件")
    """
    try:
        logger.info(f"Analyzing prompt clarity: {prompt[:50]}...")
        
        # 分析清晰度的关键指标
        issues = []
        suggestions = []
        
        # 检查1: 是否过于简短
        if len(prompt.strip()) < 10:
            issues.append("提示词过于简短")
            suggestions.append("请提供更详细的任务描述")
        
        # 检查2: 是否包含模糊词汇
        vague_words = ["帮我", "搞一下", "弄个", "做个", "随便", "一些", "什么的"]
        found_vague = [word for word in vague_words if word in prompt]
        if found_vague:
            issues.append(f"包含模糊词汇: {', '.join(found_vague)}")
            suggestions.append("请使用更具体的动词和名词")
        
        # 检查3: 是否缺少关键信息
        if "爬取" in prompt or "抓取" in prompt:
            if not any(word in prompt for word in ["网站", "URL", "链接", "地址"]):
                issues.append("缺少目标网站信息")
                suggestions.append("请指定要爬取的网站URL或名称")
        
        # 检查4: 是否缺少输出要求
        if not any(word in prompt for word in ["保存", "输出", "格式", "文件", "返回"]):
            issues.append("缺少输出格式说明")
            suggestions.append("请说明期望的输出格式（如JSON、CSV、文本等）")
        
        # 生成分析结果
        if not issues:
            result = "✅ 提示词清晰度良好，表述明确具体。"
        else:
            result = f"⚠️ 发现 {len(issues)} 个清晰度问题：\n"
            result += "\n".join([f"• {issue}" for issue in issues])
            result += "\n\n💡 改进建议：\n"
            result += "\n".join([f"• {suggestion}" for suggestion in suggestions])
        
        logger.info("Prompt clarity analysis completed")
        return result
        
    except Exception as e:
        error_msg = f"Prompt clarity analysis failed: {str(e)}"
        logger.error(error_msg)
        return f"❌ {error_msg}"


@tool
def detect_prompt_contradictions(prompt: str) -> str:
    """
    检测提示词中的矛盾和冲突表述。
    
    识别提示词中相互矛盾的要求或不一致的指令。
    
    Args:
        prompt: 需要检测的提示词
        
    Returns:
        矛盾检测结果和解决建议
        
    Example:
        detect_prompt_contradictions("快速且详细地爬取所有数据")
        detect_prompt_contradictions("简单地完成这个复杂任务")
    """
    try:
        logger.info(f"Detecting prompt contradictions: {prompt[:50]}...")
        
        contradictions = []
        
        # 检测常见矛盾模式
        contradiction_patterns = [
            (["快速", "迅速", "立即"], ["详细", "完整", "全面"], "速度与完整性矛盾"),
            (["简单", "容易"], ["复杂", "困难", "高级"], "复杂度矛盾"),
            (["少量", "几个"], ["所有", "全部", "完整"], "数量范围矛盾"),
            (["精确", "准确"], ["大概", "差不多", "随便"], "精确度矛盾"),
            (["保存", "存储"], ["不保存", "临时"], "存储要求矛盾")
        ]
        
        for positive_words, negative_words, contradiction_type in contradiction_patterns:
            has_positive = any(word in prompt for word in positive_words)
            has_negative = any(word in prompt for word in negative_words)
            
            if has_positive and has_negative:
                found_positive = [word for word in positive_words if word in prompt]
                found_negative = [word for word in negative_words if word in prompt]
                contradictions.append({
                    "type": contradiction_type,
                    "positive": found_positive,
                    "negative": found_negative
                })
        
        # 生成检测结果
        if not contradictions:
            result = "✅ 未发现明显的矛盾表述，提示词逻辑一致。"
        else:
            result = f"⚠️ 发现 {len(contradictions)} 个潜在矛盾：\n"
            for i, contradiction in enumerate(contradictions, 1):
                result += f"\n{i}. {contradiction['type']}:\n"
                result += f"   • 正面词汇: {', '.join(contradiction['positive'])}\n"
                result += f"   • 负面词汇: {', '.join(contradiction['negative'])}\n"
            
            result += "\n💡 解决建议：\n"
            result += "• 明确优先级：确定哪个要求更重要\n"
            result += "• 重新表述：使用不冲突的词汇\n"
            result += "• 分步执行：将矛盾要求分解为不同步骤"
        
        logger.info("Prompt contradiction detection completed")
        return result
        
    except Exception as e:
        error_msg = f"Prompt contradiction detection failed: {str(e)}"
        logger.error(error_msg)
        return f"❌ {error_msg}"


@tool
def generate_clarifying_questions(prompt: str) -> str:
    """
    基于提示词生成澄清问题，帮助完善任务描述。
    
    分析提示词中的不明确之处，生成有针对性的问题。
    
    Args:
        prompt: 需要澄清的提示词
        
    Returns:
        澄清问题列表和说明
        
    Example:
        generate_clarifying_questions("爬取电商网站商品信息")
        generate_clarifying_questions("提取新闻数据并分析")
    """
    try:
        logger.info(f"Generating clarifying questions: {prompt[:50]}...")
        
        questions = []
        
        # 基于任务类型生成问题
        if "爬取" in prompt or "抓取" in prompt:
            if not any(word in prompt for word in ["网站", "URL", "链接"]):
                questions.append("🌐 请提供具体的目标网站URL或网站名称？")
            
            if "商品" in prompt:
                questions.append("🛍️ 需要提取哪些商品信息？（如价格、标题、图片、评价等）")
                questions.append("📊 期望获取多少个商品的数据？")
            
            if "新闻" in prompt:
                questions.append("📰 需要哪个时间段的新闻？（如最近一周、一个月等）")
                questions.append("🏷️ 有特定的新闻分类要求吗？（如科技、财经、体育等）")
        
        # 基于输出要求生成问题
        if not any(word in prompt for word in ["保存", "输出", "格式", "文件"]):
            questions.append("💾 希望以什么格式保存数据？（如JSON、CSV、Excel等）")
            questions.append("📁 数据保存到哪个位置？")
        
        # 基于数据处理生成问题
        if "分析" in prompt:
            questions.append("📈 需要进行什么类型的分析？（如统计分析、趋势分析等）")
            questions.append("📋 分析结果需要包含哪些指标？")
        
        # 基于性能要求生成问题
        if not any(word in prompt for word in ["速度", "时间", "频率"]):
            questions.append("⏱️ 对执行速度有特殊要求吗？")
            questions.append("🔄 是否需要定期执行此任务？")
        
        # 生成结果
        if not questions:
            result = "✅ 提示词信息较为完整，暂无需要澄清的问题。"
        else:
            result = f"❓ 为了更好地完成任务，请回答以下 {len(questions)} 个问题：\n\n"
            result += "\n".join([f"{i+1}. {q}" for i, q in enumerate(questions)])
            result += "\n\n💡 提示：回答这些问题将帮助生成更精确的任务执行方案。"
        
        logger.info("Clarifying questions generation completed")
        return result
        
    except Exception as e:
        error_msg = f"Clarifying questions generation failed: {str(e)}"
        logger.error(error_msg)
        return f"❌ {error_msg}"


@tool
def optimize_prompt_structure(prompt: str) -> str:
    """
    优化提示词的结构和表述，提高执行效果。
    
    重新组织提示词，使其更清晰、更具体、更易执行。
    
    Args:
        prompt: 需要优化的原始提示词
        
    Returns:
        优化后的提示词和改进说明
        
    Example:
        optimize_prompt_structure("帮我爬取淘宝商品数据")
        optimize_prompt_structure("提取网站信息并保存")
    """
    try:
        logger.info(f"Optimizing prompt structure: {prompt[:50]}...")
        
        # 分析原始提示词
        original = prompt.strip()
        
        # 提取关键信息
        action_words = []
        target_words = []
        output_words = []
        
        # 识别动作词
        actions = ["爬取", "抓取", "提取", "获取", "下载", "收集", "分析", "处理"]
        action_words = [word for word in actions if word in original]
        
        # 识别目标词
        targets = ["网站", "商品", "新闻", "数据", "信息", "内容", "图片", "文本"]
        target_words = [word for word in targets if word in original]
        
        # 识别输出词
        outputs = ["保存", "输出", "导出", "存储", "文件", "JSON", "CSV"]
        output_words = [word for word in outputs if word in original]
        
        # 构建优化后的提示词
        optimized_parts = []
        
        # 1. 明确任务目标
        if action_words and target_words:
            optimized_parts.append(f"**任务目标**: {action_words[0]}{target_words[0]}数据")
        else:
            optimized_parts.append("**任务目标**: 请明确指定要执行的具体操作")
        
        # 2. 添加具体要求
        if "网站" in original or "URL" in original:
            optimized_parts.append("**目标网站**: [请提供具体的网站URL]")
        
        if "商品" in original:
            optimized_parts.append("**数据字段**: 商品标题、价格、图片链接、商品详情")
        elif "新闻" in original:
            optimized_parts.append("**数据字段**: 新闻标题、发布时间、内容摘要、来源")
        else:
            optimized_parts.append("**数据字段**: [请指定需要提取的具体字段]")
        
        # 3. 输出格式
        if output_words:
            optimized_parts.append(f"**输出格式**: {output_words[0]}为结构化数据")
        else:
            optimized_parts.append("**输出格式**: JSON格式，便于后续处理")
        
        # 4. 执行要求
        optimized_parts.append("**执行要求**: 确保数据完整性和准确性")
        
        # 组合优化结果
        optimized_prompt = "\n".join(optimized_parts)
        
        result = f"📝 **原始提示词**:\n{original}\n\n"
        result += f"✨ **优化后提示词**:\n{optimized_prompt}\n\n"
        result += "🔧 **主要改进**:\n"
        result += "• 结构化表述，层次清晰\n"
        result += "• 明确任务目标和具体要求\n"
        result += "• 规范输出格式说明\n"
        result += "• 增加执行标准和质量要求"
        
        logger.info("Prompt structure optimization completed")
        return result
        
    except Exception as e:
        error_msg = f"Prompt structure optimization failed: {str(e)}"
        logger.error(error_msg)
        return f"❌ {error_msg}"


@tool
def llm_reflection_optimizer(prompt: str, task_context: str = "") -> str:
    """
    使用LLM反思机制优化提示词。

    基于最新的Meta-Prompting研究，让LLM自我反思和改进提示词。

    Args:
        prompt: 需要优化的提示词
        task_context: 任务上下文信息

    Returns:
        反思式优化结果和详细分析

    Example:
        llm_reflection_optimizer("帮我爬取网站数据", "电商数据采集")
    """
    try:
        from ..core.config import config
        from langchain_openai import ChatOpenAI

        logger.info(f"LLM reflection optimization: {prompt[:50]}...")

        llm = ChatOpenAI(**config.get_llm_config())

        reflection_prompt = f"""
        作为提示词优化专家，请分析以下提示词并进行深度反思：

        原始提示词: "{prompt}"
        任务上下文: "{task_context}"

        请按以下步骤进行反思和优化：

        1. **初步分析**: 这个提示词想要实现什么目标？
        2. **问题识别**: 当前提示词存在哪些具体问题？
        3. **改进策略**: 基于问题，应该如何改进？
        4. **优化实施**: 生成改进后的提示词
        5. **效果预测**: 预测优化后的效果

        请提供详细的分析过程和最终的优化结果。
        """

        result = llm.invoke(reflection_prompt)
        logger.info("LLM reflection optimization completed")
        return result.content

    except Exception as e:
        error_msg = f"LLM reflection optimization failed: {str(e)}"
        logger.error(error_msg)
        return f"❌ {error_msg}"


@tool
def llm_iterative_optimizer(prompt: str, max_iterations: int = 3) -> str:
    """
    使用迭代改进方法优化提示词。

    基于DSPy和TextGRAD的迭代优化思想，多轮改进提示词质量。

    Args:
        prompt: 需要优化的原始提示词
        max_iterations: 最大迭代次数

    Returns:
        迭代优化结果和改进历史

    Example:
        llm_iterative_optimizer("提取商品信息", 3)
    """
    try:
        from ..core.config import config
        from langchain_openai import ChatOpenAI
        import json

        logger.info(f"LLM iterative optimization: {prompt[:50]}...")

        llm = ChatOpenAI(**config.get_llm_config())
        current_prompt = prompt
        optimization_history = []

        for iteration in range(max_iterations):
            iteration_prompt = f"""
            这是第 {iteration + 1} 轮优化，请改进以下提示词：

            当前提示词: "{current_prompt}"

            优化历史: {optimization_history}

            请：
            1. 评估当前提示词的质量（1-10分）
            2. 识别具体的改进点
            3. 生成改进版本
            4. 解释改进的理由

            如果质量已达到9分以上，请说明"优化完成"。

            请以JSON格式返回：
            {{
                "quality_score": 分数,
                "improvement_points": ["改进点1", "改进点2"],
                "optimized_prompt": "改进后的提示词",
                "reasoning": "改进理由",
                "completed": true/false
            }}
            """

            result = llm.invoke(iteration_prompt)
            optimization_history.append(f"第{iteration+1}轮: {result.content}")

            # 解析结果并提取改进后的提示词
            try:
                parsed_result = json.loads(result.content)
                current_prompt = parsed_result.get("optimized_prompt", current_prompt)

                if parsed_result.get("completed", False):
                    break
            except:
                # 如果解析失败，继续下一轮
                pass

        final_result = f"经过{iteration+1}轮迭代优化的结果：\n{current_prompt}\n\n优化历史：\n" + "\n".join(optimization_history)

        logger.info("LLM iterative optimization completed")
        return final_result

    except Exception as e:
        error_msg = f"LLM iterative optimization failed: {str(e)}"
        logger.error(error_msg)
        return f"❌ {error_msg}"


@tool
def llm_quality_assessor(user_input: str, task_result: str = "") -> str:
    """
    使用LLM评估任务执行质量。

    从多个维度评估提示词和执行结果的质量，判断是否需要优化。

    Args:
        user_input: 用户输入的提示词
        task_result: 任务执行结果

    Returns:
        质量评估结果和优化建议

    Example:
        llm_quality_assessor("爬取商品数据", "成功提取了100个商品信息")
    """
    try:
        from ..core.config import config
        from langchain_openai import ChatOpenAI

        logger.info(f"LLM quality assessment: {user_input[:50]}...")

        llm = ChatOpenAI(**config.get_llm_config())

        assessment_prompt = f"""
        作为任务质量评估专家，请评估以下任务执行情况：

        用户输入: "{user_input}"
        执行结果: "{task_result}"

        请从以下维度评分（0-10分）：

        1. **完整性**: 任务是否完全完成？
        2. **准确性**: 结果是否准确无误？
        3. **清晰度**: 输出是否清晰易懂？
        4. **相关性**: 结果是否与用户需求相关？
        5. **可用性**: 结果是否可以直接使用？

        请以JSON格式返回评分：
        {{
            "completeness": 分数,
            "accuracy": 分数,
            "clarity": 分数,
            "relevance": 分数,
            "usability": 分数,
            "overall": 总体分数,
            "needs_optimization": true/false,
            "optimization_reason": "原因说明"
        }}
        """

        result = llm.invoke(assessment_prompt)
        logger.info("LLM quality assessment completed")
        return result.content

    except Exception as e:
        error_msg = f"LLM quality assessment failed: {str(e)}"
        logger.error(error_msg)
        return f"❌ {error_msg}"


@tool
def llm_expert_panel_optimizer(prompt: str, domain: str = "web_automation") -> str:
    """
    模拟专家小组讨论优化提示词。

    基于Meta-Prompting的多专家协作思想，模拟不同领域专家的讨论。

    Args:
        prompt: 需要优化的提示词
        domain: 任务领域

    Returns:
        专家小组讨论结果和优化建议

    Example:
        llm_expert_panel_optimizer("提取网站数据", "web_scraping")
    """
    try:
        from ..core.config import config
        from langchain_openai import ChatOpenAI

        logger.info(f"LLM expert panel optimization: {prompt[:50]}...")

        llm = ChatOpenAI(**config.get_llm_config())

        expert_panel_prompt = f"""
        现在召开提示词优化专家小组会议，讨论如何优化以下提示词：

        提示词: "{prompt}"
        领域: "{domain}"

        参会专家：
        1. 📝 语言学专家 - 关注语言表达的准确性和清晰度
        2. 🎯 任务专家 - 关注任务目标的明确性和可执行性
        3. 🤖 AI专家 - 关注LLM理解和执行的优化
        4. 👥 用户体验专家 - 关注用户友好性和易用性

        请模拟专家讨论过程：

        **第一轮发言** - 每位专家指出问题
        **第二轮发言** - 每位专家提出改进建议
        **第三轮发言** - 专家们协商统一方案
        **最终结论** - 输出优化后的提示词

        请详细展现讨论过程和最终结果。
        """

        result = llm.invoke(expert_panel_prompt)
        logger.info("LLM expert panel optimization completed")
        return result.content

    except Exception as e:
        error_msg = f"LLM expert panel optimization failed: {str(e)}"
        logger.error(error_msg)
        return f"❌ {error_msg}"


# 提示词优化工具列表
PROMPT_OPTIMIZATION_TOOLS = [
    # 保留原有规则驱动工具作为备用
    analyze_prompt_clarity,
    detect_prompt_contradictions,
    generate_clarifying_questions,
    optimize_prompt_structure,

    # 新增LLM驱动工具
    llm_reflection_optimizer,
    llm_iterative_optimizer,
    llm_quality_assessor,
    llm_expert_panel_optimizer
]


def get_prompt_optimization_tools() -> List[BaseTool]:
    """
    Get prompt optimization tools for PromptOptimizationAgent.

    Returns:
        List of prompt optimization tools including both rule-based and LLM-driven tools
    """
    return PROMPT_OPTIMIZATION_TOOLS.copy()
