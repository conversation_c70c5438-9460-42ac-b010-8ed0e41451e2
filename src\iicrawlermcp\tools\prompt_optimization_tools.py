"""
Prompt optimization tools for iICrawlerMCP.

This module provides specialized tools for analyzing and optimizing user prompts
to improve task execution effectiveness.
"""

import logging
from langchain_core.tools import tool
from typing import List
from langchain_core.tools import BaseTool

logger = logging.getLogger(__name__)


@tool
def analyze_prompt_clarity(prompt: str) -> str:
    """
    分析提示词的清晰度和明确性。
    
    检查提示词是否包含模糊表述、缺失信息或不明确的要求。
    
    Args:
        prompt: 需要分析的提示词
        
    Returns:
        清晰度分析结果和改进建议
        
    Example:
        analyze_prompt_clarity("帮我爬取网站数据")
        analyze_prompt_clarity("提取商品信息并保存到文件")
    """
    try:
        logger.info(f"Analyzing prompt clarity: {prompt[:50]}...")
        
        # 分析清晰度的关键指标
        issues = []
        suggestions = []
        
        # 检查1: 是否过于简短
        if len(prompt.strip()) < 10:
            issues.append("提示词过于简短")
            suggestions.append("请提供更详细的任务描述")
        
        # 检查2: 是否包含模糊词汇
        vague_words = ["帮我", "搞一下", "弄个", "做个", "随便", "一些", "什么的"]
        found_vague = [word for word in vague_words if word in prompt]
        if found_vague:
            issues.append(f"包含模糊词汇: {', '.join(found_vague)}")
            suggestions.append("请使用更具体的动词和名词")
        
        # 检查3: 是否缺少关键信息
        if "爬取" in prompt or "抓取" in prompt:
            if not any(word in prompt for word in ["网站", "URL", "链接", "地址"]):
                issues.append("缺少目标网站信息")
                suggestions.append("请指定要爬取的网站URL或名称")
        
        # 检查4: 是否缺少输出要求
        if not any(word in prompt for word in ["保存", "输出", "格式", "文件", "返回"]):
            issues.append("缺少输出格式说明")
            suggestions.append("请说明期望的输出格式（如JSON、CSV、文本等）")
        
        # 生成分析结果
        if not issues:
            result = "✅ 提示词清晰度良好，表述明确具体。"
        else:
            result = f"⚠️ 发现 {len(issues)} 个清晰度问题：\n"
            result += "\n".join([f"• {issue}" for issue in issues])
            result += "\n\n💡 改进建议：\n"
            result += "\n".join([f"• {suggestion}" for suggestion in suggestions])
        
        logger.info("Prompt clarity analysis completed")
        return result
        
    except Exception as e:
        error_msg = f"Prompt clarity analysis failed: {str(e)}"
        logger.error(error_msg)
        return f"❌ {error_msg}"


@tool
def detect_prompt_contradictions(prompt: str) -> str:
    """
    检测提示词中的矛盾和冲突表述。
    
    识别提示词中相互矛盾的要求或不一致的指令。
    
    Args:
        prompt: 需要检测的提示词
        
    Returns:
        矛盾检测结果和解决建议
        
    Example:
        detect_prompt_contradictions("快速且详细地爬取所有数据")
        detect_prompt_contradictions("简单地完成这个复杂任务")
    """
    try:
        logger.info(f"Detecting prompt contradictions: {prompt[:50]}...")
        
        contradictions = []
        
        # 检测常见矛盾模式
        contradiction_patterns = [
            (["快速", "迅速", "立即"], ["详细", "完整", "全面"], "速度与完整性矛盾"),
            (["简单", "容易"], ["复杂", "困难", "高级"], "复杂度矛盾"),
            (["少量", "几个"], ["所有", "全部", "完整"], "数量范围矛盾"),
            (["精确", "准确"], ["大概", "差不多", "随便"], "精确度矛盾"),
            (["保存", "存储"], ["不保存", "临时"], "存储要求矛盾")
        ]
        
        for positive_words, negative_words, contradiction_type in contradiction_patterns:
            has_positive = any(word in prompt for word in positive_words)
            has_negative = any(word in prompt for word in negative_words)
            
            if has_positive and has_negative:
                found_positive = [word for word in positive_words if word in prompt]
                found_negative = [word for word in negative_words if word in prompt]
                contradictions.append({
                    "type": contradiction_type,
                    "positive": found_positive,
                    "negative": found_negative
                })
        
        # 生成检测结果
        if not contradictions:
            result = "✅ 未发现明显的矛盾表述，提示词逻辑一致。"
        else:
            result = f"⚠️ 发现 {len(contradictions)} 个潜在矛盾：\n"
            for i, contradiction in enumerate(contradictions, 1):
                result += f"\n{i}. {contradiction['type']}:\n"
                result += f"   • 正面词汇: {', '.join(contradiction['positive'])}\n"
                result += f"   • 负面词汇: {', '.join(contradiction['negative'])}\n"
            
            result += "\n💡 解决建议：\n"
            result += "• 明确优先级：确定哪个要求更重要\n"
            result += "• 重新表述：使用不冲突的词汇\n"
            result += "• 分步执行：将矛盾要求分解为不同步骤"
        
        logger.info("Prompt contradiction detection completed")
        return result
        
    except Exception as e:
        error_msg = f"Prompt contradiction detection failed: {str(e)}"
        logger.error(error_msg)
        return f"❌ {error_msg}"


@tool
def generate_clarifying_questions(prompt: str) -> str:
    """
    基于提示词生成澄清问题，帮助完善任务描述。
    
    分析提示词中的不明确之处，生成有针对性的问题。
    
    Args:
        prompt: 需要澄清的提示词
        
    Returns:
        澄清问题列表和说明
        
    Example:
        generate_clarifying_questions("爬取电商网站商品信息")
        generate_clarifying_questions("提取新闻数据并分析")
    """
    try:
        logger.info(f"Generating clarifying questions: {prompt[:50]}...")
        
        questions = []
        
        # 基于任务类型生成问题
        if "爬取" in prompt or "抓取" in prompt:
            if not any(word in prompt for word in ["网站", "URL", "链接"]):
                questions.append("🌐 请提供具体的目标网站URL或网站名称？")
            
            if "商品" in prompt:
                questions.append("🛍️ 需要提取哪些商品信息？（如价格、标题、图片、评价等）")
                questions.append("📊 期望获取多少个商品的数据？")
            
            if "新闻" in prompt:
                questions.append("📰 需要哪个时间段的新闻？（如最近一周、一个月等）")
                questions.append("🏷️ 有特定的新闻分类要求吗？（如科技、财经、体育等）")
        
        # 基于输出要求生成问题
        if not any(word in prompt for word in ["保存", "输出", "格式", "文件"]):
            questions.append("💾 希望以什么格式保存数据？（如JSON、CSV、Excel等）")
            questions.append("📁 数据保存到哪个位置？")
        
        # 基于数据处理生成问题
        if "分析" in prompt:
            questions.append("📈 需要进行什么类型的分析？（如统计分析、趋势分析等）")
            questions.append("📋 分析结果需要包含哪些指标？")
        
        # 基于性能要求生成问题
        if not any(word in prompt for word in ["速度", "时间", "频率"]):
            questions.append("⏱️ 对执行速度有特殊要求吗？")
            questions.append("🔄 是否需要定期执行此任务？")
        
        # 生成结果
        if not questions:
            result = "✅ 提示词信息较为完整，暂无需要澄清的问题。"
        else:
            result = f"❓ 为了更好地完成任务，请回答以下 {len(questions)} 个问题：\n\n"
            result += "\n".join([f"{i+1}. {q}" for i, q in enumerate(questions)])
            result += "\n\n💡 提示：回答这些问题将帮助生成更精确的任务执行方案。"
        
        logger.info("Clarifying questions generation completed")
        return result
        
    except Exception as e:
        error_msg = f"Clarifying questions generation failed: {str(e)}"
        logger.error(error_msg)
        return f"❌ {error_msg}"


@tool
def optimize_prompt_structure(prompt: str) -> str:
    """
    优化提示词的结构和表述，提高执行效果。
    
    重新组织提示词，使其更清晰、更具体、更易执行。
    
    Args:
        prompt: 需要优化的原始提示词
        
    Returns:
        优化后的提示词和改进说明
        
    Example:
        optimize_prompt_structure("帮我爬取淘宝商品数据")
        optimize_prompt_structure("提取网站信息并保存")
    """
    try:
        logger.info(f"Optimizing prompt structure: {prompt[:50]}...")
        
        # 分析原始提示词
        original = prompt.strip()
        
        # 提取关键信息
        action_words = []
        target_words = []
        output_words = []
        
        # 识别动作词
        actions = ["爬取", "抓取", "提取", "获取", "下载", "收集", "分析", "处理"]
        action_words = [word for word in actions if word in original]
        
        # 识别目标词
        targets = ["网站", "商品", "新闻", "数据", "信息", "内容", "图片", "文本"]
        target_words = [word for word in targets if word in original]
        
        # 识别输出词
        outputs = ["保存", "输出", "导出", "存储", "文件", "JSON", "CSV"]
        output_words = [word for word in outputs if word in original]
        
        # 构建优化后的提示词
        optimized_parts = []
        
        # 1. 明确任务目标
        if action_words and target_words:
            optimized_parts.append(f"**任务目标**: {action_words[0]}{target_words[0]}数据")
        else:
            optimized_parts.append("**任务目标**: 请明确指定要执行的具体操作")
        
        # 2. 添加具体要求
        if "网站" in original or "URL" in original:
            optimized_parts.append("**目标网站**: [请提供具体的网站URL]")
        
        if "商品" in original:
            optimized_parts.append("**数据字段**: 商品标题、价格、图片链接、商品详情")
        elif "新闻" in original:
            optimized_parts.append("**数据字段**: 新闻标题、发布时间、内容摘要、来源")
        else:
            optimized_parts.append("**数据字段**: [请指定需要提取的具体字段]")
        
        # 3. 输出格式
        if output_words:
            optimized_parts.append(f"**输出格式**: {output_words[0]}为结构化数据")
        else:
            optimized_parts.append("**输出格式**: JSON格式，便于后续处理")
        
        # 4. 执行要求
        optimized_parts.append("**执行要求**: 确保数据完整性和准确性")
        
        # 组合优化结果
        optimized_prompt = "\n".join(optimized_parts)
        
        result = f"📝 **原始提示词**:\n{original}\n\n"
        result += f"✨ **优化后提示词**:\n{optimized_prompt}\n\n"
        result += "🔧 **主要改进**:\n"
        result += "• 结构化表述，层次清晰\n"
        result += "• 明确任务目标和具体要求\n"
        result += "• 规范输出格式说明\n"
        result += "• 增加执行标准和质量要求"
        
        logger.info("Prompt structure optimization completed")
        return result
        
    except Exception as e:
        error_msg = f"Prompt structure optimization failed: {str(e)}"
        logger.error(error_msg)
        return f"❌ {error_msg}"


# 提示词优化工具列表
PROMPT_OPTIMIZATION_TOOLS = [
    analyze_prompt_clarity,
    detect_prompt_contradictions,
    generate_clarifying_questions,
    optimize_prompt_structure
]


def get_prompt_optimization_tools() -> List[BaseTool]:
    """
    Get prompt optimization tools for PromptOptimizationAgent.
    
    Returns:
        List of prompt optimization tools
    """
    return PROMPT_OPTIMIZATION_TOOLS.copy()
