"""
Browser module for iICrawlerMCP.

This module provides a Browser class that wraps Playwright functionality
for web automation and screenshot capture.
"""

import logging
import os
from typing import Optional

from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, BrowserContext
from playwright.sync_api import sync_playwright, <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON> as SyncP<PERSON>, BrowserContext as SyncBrowserContext

logger = logging.getLogger(__name__)

# Global browser instance for reuse
_global_browser = None


class Browser:
    """
    Browser wrapper class using Playwright.
    
    This class provides a simplified interface for browser automation,
    including navigation, screenshot capture, and element interaction.
    """
    
    def __init__(self, headless: bool = True, browser_type: str = "chromium"):
        """
        Initialize the Browser.
        
        Args:
            headless: Whether to run browser in headless mode
            browser_type: Type of browser to use ('chromium', 'firefox', 'webkit')
        """
        self.headless = headless
        self.browser_type = browser_type
        self._playwright = None
        self._browser = None
        self._context = None
        self._page = None
        self._is_initialized = False
        
        logger.info(f"Browser initialized with {browser_type}, headless={headless}")
    
    def __enter__(self):
        """Context manager entry."""
        self._initialize()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.close()
    
    def _initialize(self):
        """Initialize Playwright and browser."""
        if self._is_initialized:
            return
        
        try:
            self._playwright = sync_playwright().start()
            
            # Get browser based on type
            if self.browser_type == "firefox":
                self._browser = self._playwright.firefox.launch(headless=self.headless)
            elif self.browser_type == "webkit":
                self._browser = self._playwright.webkit.launch(headless=self.headless)
            else:  # default to chromium
                self._browser = self._playwright.chromium.launch(headless=self.headless)
            
            # Create context and page
            self._context = self._browser.new_context()
            self._page = self._context.new_page()
            
            self._is_initialized = True
            logger.info("Browser initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize browser: {e}")
            raise Exception(f"Browser initialization failed: {e}")

    def _get_locator(self, selector: str):
        """
        Get a Playwright locator for the given XPath selector.

        Args:
            selector: XPath selector for the element (only XPath is supported)

        Returns:
            Playwright locator object
        """
        # Normalize XPath format
        if selector.startswith('xpath/'):
            # Remove 'xpath/' prefix if present
            xpath = selector.replace('xpath/', '', 1)
        elif selector.startswith('//') or selector.startswith('/html'):
            # Standard XPath format
            xpath = selector
        elif selector.startswith('html/'):
            # DOM tool format - convert to standard XPath
            xpath = '/' + selector
        else:
            # Assume it's already a valid XPath or element path
            # If it doesn't start with /, add it
            xpath = selector if selector.startswith('/') else '/' + selector

        return self._page.locator(f"xpath={xpath}")

    def navigate(self, url: str) -> dict:
        """
        Navigate to a URL.
        
        Args:
            url: The URL to navigate to
            
        Returns:
            A dictionary containing navigation result information
            
        Raises:
            Exception: If navigation fails
        """
        if not self._is_initialized:
            self._initialize()
        
        try:
            logger.info(f"Navigating to: {url}")
            response = self._page.goto(url, wait_until="domcontentloaded", timeout=30000)
            
            result = {
                'url': self._page.url,
                'title': self._page.title(),
                'status': response.status if response else None,
                'success': True
            }
            
            logger.info(f"Navigation successful: {result['title']}")
            return result
            
        except Exception as e:
            logger.error(f"Navigation failed: {e}")
            raise Exception(f"Navigation to {url} failed: {e}")
    
    def screenshot(self, path: str = None, full_page: bool = False, 
                  element_selector: str = None) -> str:
        """
        Take a screenshot of the current page or element.
        
        Args:
            path: Path to save the screenshot (optional)
            full_page: Whether to capture the full page
            element_selector: CSS selector for specific element to capture
            
        Returns:
            Path to the saved screenshot
            
        Raises:
            Exception: If screenshot capture fails
        """
        if not self._is_initialized:
            raise Exception("Browser not initialized. Navigate to a page first.")
        
        try:
            # Generate default path if not provided
            if not path:
                from .config import config

                # Get screenshots directory and ensure it exists
                screenshots_dir = config.get_screenshots_dir()
                screenshots_dir.mkdir(parents=True, exist_ok=True)

                timestamp = __import__('datetime').datetime.now().strftime("%Y%m%d_%H%M%S")

                # Get current URL and create a safe filename
                try:
                    current_url = self._page.url
                    # Extract domain and path info from URL
                    from urllib.parse import urlparse
                    parsed_url = urlparse(current_url)
                    domain = parsed_url.netloc.replace('www.', '')
                    path_part = parsed_url.path.strip('/')

                    # Create safe filename by replacing invalid characters
                    safe_domain = ''.join(c if c.isalnum() or c in '-_' else '_' for c in domain)
                    safe_path = ''.join(c if c.isalnum() or c in '-_' else '_' for c in path_part)

                    # Construct filename with URL info
                    if safe_path and len(safe_path) > 0:
                        url_part = f"{safe_domain}_{safe_path}"
                    else:
                        url_part = safe_domain

                    # Limit URL part length to avoid overly long filenames
                    if len(url_part) > 50:
                        url_part = url_part[:50]

                    path = str(screenshots_dir / f"{url_part}_{timestamp}.png")
                except Exception:
                    # Fallback to original naming if URL processing fails
                    path = str(screenshots_dir / f"screenshot_{timestamp}.png")
            
            # Take screenshot
            if element_selector:
                element = self._page.locator(element_selector)
                element.screenshot(path=path)
                logger.info(f"Element screenshot saved: {path}")
            else:
                # For full page screenshots, scroll to trigger lazy loading
                if full_page:
                    self._scroll_to_load_content()

                self._page.screenshot(path=path, full_page=full_page)
                logger.info(f"Page screenshot saved: {path}")
            
            return path
            
        except Exception as e:
            logger.error(f"Screenshot failed: {e}")
            raise Exception(f"Screenshot capture failed: {e}")

    def _scroll_to_load_content(self) -> None:
        """
        Scroll through the page to trigger lazy loading of images and content.
        This is essential for proper full-page screenshots.
        """
        try:
            # JavaScript to scroll through the page and trigger lazy loading
            scroll_script = """
            () => {
                return new Promise((resolve) => {
                    let totalHeight = 0;
                    const distance = window.innerHeight;
                    const timer = setInterval(() => {
                        const scrollHeight = document.body.scrollHeight;
                        window.scrollBy(0, distance);
                        totalHeight += distance;

                        if(totalHeight >= scrollHeight){
                            // Scroll back to top
                            window.scrollTo(0, 0);
                            clearInterval(timer);
                            // Wait a bit for any final loading
                            setTimeout(resolve, 500);
                        }
                    }, 100);
                });
            }
            """

            # Execute the scroll script
            self._page.evaluate(scroll_script)
            logger.info("Page scrolled to trigger lazy loading")

        except Exception as e:
            logger.warning(f"Failed to scroll page for lazy loading: {e}")
            # Continue with screenshot even if scrolling fails

    def get_page_info(self) -> dict:
        """
        Get information about the current page.
        
        Returns:
            A dictionary containing page information
            
        Raises:
            Exception: If page info retrieval fails
        """
        if not self._is_initialized:
            raise Exception("Browser not initialized. Navigate to a page first.")
        
        from .browser_thread import run_browser_call

        def _fn():
            return {
                'title': self._page.title(),
                'url': self._page.url,
                'viewport': self._page.viewport_size
            }

        try:
            page_info = run_browser_call(_fn)
            
            logger.info("Page info retrieved successfully")
            return page_info
        except Exception as e:
            logger.error(f"Failed to get page info: {e}")
            raise Exception(f"Page info retrieval failed: {e}")
    
    def snapshot(self) -> dict:
        """
        Capture accessibility snapshot of the current page.

        Returns:
            A dictionary containing the accessibility tree and page information.

        Raises:
            Exception: If snapshot capture fails.
        """
        if not self._is_initialized:
            raise Exception("Browser not initialized. Navigate to a page first.")

        from .browser_thread import run_browser_call

        def _fn():
            accessibility_tree = self._page.accessibility.snapshot()
            return {
                'title': self._page.title(),
                'url': self._page.url,
                'viewport': self._page.viewport_size,
                'accessibility_tree': accessibility_tree
            }

        try:
            page_info = run_browser_call(_fn)

            logger.info("Page snapshot captured successfully")
            return page_info
        except Exception as e:
            logger.error(f"Failed to capture snapshot: {e}")
            raise Exception(f"Snapshot failed: {e}")
    
    def click(self, selector: str, click_type: str = "single") -> dict:
        """
        Click on an element.
        
        Args:
            selector: CSS selector or XPath for the element
            click_type: Type of click ('single', 'double')
            
        Returns:
            A dictionary containing click result information
            
        Raises:
            Exception: If click fails
        """
        if not self._is_initialized:
            raise Exception("Browser not initialized. Navigate to a page first.")
        
        try:
            element = self._get_locator(selector)

            if click_type == "double":
                element.dblclick()
                logger.info(f"Double-clicked element: {selector}")
            else:
                element.click()
                logger.info(f"Clicked element: {selector}")

            return {
                'selector': selector,
                'click_type': click_type,
                'success': True
            }

        except Exception as e:
            logger.error(f"Click failed on {selector}: {e}")
            raise Exception(f"Click on {selector} failed: {e}")
    
    def type_text(self, selector: str, text: str, clear_first: bool = True) -> dict:
        """
        Type text into an element.
        
        Args:
            selector: CSS selector or XPath for the element
            text: Text to type
            clear_first: Whether to clear existing text first
            
        Returns:
            A dictionary containing typing result information
            
        Raises:
            Exception: If typing fails
        """
        if not self._is_initialized:
            raise Exception("Browser not initialized. Navigate to a page first.")
        
        try:
            element = self._get_locator(selector)

            if clear_first:
                element.clear()

            element.type(text)
            logger.info(f"Typed text into {selector}: {text}")

            return {
                'selector': selector,
                'text': text,
                'success': True
            }

        except Exception as e:
            logger.error(f"Type failed on {selector}: {e}")
            raise Exception(f"Type text on {selector} failed: {e}")
    
    def wait_for_element(self, selector: str, timeout: int = 30000) -> dict:
        """
        Wait for an element to appear.
        
        Args:
            selector: CSS selector or XPath for the element
            timeout: Timeout in milliseconds
            
        Returns:
            A dictionary containing wait result information
            
        Raises:
            Exception: If wait fails
        """
        if not self._is_initialized:
            raise Exception("Browser not initialized. Navigate to a page first.")
        
        try:
            element = self._get_locator(selector)
            element.wait_for(timeout=timeout)

            logger.info(f"Element appeared: {selector}")
            return {
                'selector': selector,
                'success': True
            }

        except Exception as e:
            logger.error(f"Wait failed for {selector}: {e}")
            raise Exception(f"Wait for {selector} failed: {e}")
    
    def evaluate_script(self, script: str, *args) -> any:
        """
        Execute JavaScript in the page context.
        
        Args:
            script: JavaScript code to execute
            *args: Arguments to pass to the script
            
        Returns:
            Result of the script execution
            
        Raises:
            Exception: If script execution fails
        """
        if not self._is_initialized:
            raise Exception("Browser not initialized. Navigate to a page first.")
        
        from .browser_thread import run_browser_call

        def _fn():
            return self._page.evaluate(script, *args)

        try:
            result = run_browser_call(_fn)
            logger.info("JavaScript executed successfully")
            return result
            
        except Exception as e:
            logger.error(f"Script execution failed: {e}")
            raise Exception(f"Script execution failed: {e}")
    
    def hover(self, selector: str) -> dict:
        """
        Hover over an element.
        
        Args:
            selector: CSS selector or XPath for the element
            
        Returns:
            A dictionary containing hover result information
            
        Raises:
            Exception: If hover fails
        """
        if not self._is_initialized:
            raise Exception("Browser not initialized. Navigate to a page first.")
        
        try:
            element = self._get_locator(selector)
            element.hover()

            logger.info(f"Hovered over element: {selector}")
            return {
                'selector': selector,
                'success': True
            }

        except Exception as e:
            logger.error(f"Hover failed on {selector}: {e}")
            raise Exception(f"Hover on {selector} failed: {e}")
    
    def press_key(self, key: str) -> dict:
        """
        Press a keyboard key.
        
        Args:
            key: Key to press (e.g., 'Enter', 'Tab', 'Escape')
            
        Returns:
            A dictionary containing key press result information
            
        Raises:
            Exception: If key press fails
        """
        if not self._is_initialized:
            raise Exception("Browser not initialized. Navigate to a page first.")
        
        try:
            self._page.keyboard.press(key)
            
            logger.info(f"Pressed key: {key}")
            return {
                'key': key,
                'success': True
            }
            
        except Exception as e:
            logger.error(f"Key press failed for {key}: {e}")
            raise Exception(f"Key press {key} failed: {e}")
    
    def select_option(self, selector: str, value: str = None, label: str = None, 
                     index: int = None) -> dict:
        """
        Select an option from a dropdown.
        
        Args:
            selector: CSS selector or XPath for the select element
            value: Option value to select
            label: Option label to select
            index: Option index to select
            
        Returns:
            A dictionary containing selection result information
            
        Raises:
            Exception: If selection fails
        """
        if not self._is_initialized:
            raise Exception("Browser not initialized. Navigate to a page first.")
        
        try:
            element = self._get_locator(selector)

            if value is not None:
                element.select_option(value=value)
                logger.info(f"Selected option by value: {value}")
            elif label is not None:
                element.select_option(label=label)
                logger.info(f"Selected option by label: {label}")
            elif index is not None:
                element.select_option(index=index)
                logger.info(f"Selected option by index: {index}")
            else:
                raise ValueError("Must provide value, label, or index")
            
            return {
                'selector': selector,
                'value': value,
                'label': label,
                'index': index,
                'success': True
            }
            
        except Exception as e:
            logger.error(f"Select option failed on {selector}: {e}")
            raise Exception(f"Select option on {selector} failed: {e}")
    
    def close(self):
        """Close the browser and clean up resources."""
        try:
            if self._page:
                self._page.close()
            if self._context:
                self._context.close()
            if self._browser:
                self._browser.close()
            if self._playwright:
                self._playwright.stop()
            
            self._is_initialized = False
            logger.info("Browser closed successfully")
            
        except Exception as e:
            logger.error(f"Error closing browser: {e}")


def get_global_browser() -> Browser:
    """
    Get or create a global browser instance.

    Returns:
        Global Browser instance
    """
    global _global_browser
    if _global_browser is None:
        from .config import config
        _global_browser = Browser(headless=config.HEADLESS)
    return _global_browser


def close_global_browser():
    """Close the global browser instance."""
    global _global_browser
    if _global_browser:
        try:
            # defer heavy cleanup to the dedicated browser thread (avoids thread mismatch)
            from .browser_thread import run_browser_call  # local import to prevent cycle

            run_browser_call(_global_browser.close)
        finally:
            _global_browser = None
