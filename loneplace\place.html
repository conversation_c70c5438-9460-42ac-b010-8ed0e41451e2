<!DOCTYPE html>
<html>
    <head>
        <meta charSet="utf-8"/>
        <meta name="viewport" content="width=device-width, initial-scale=1"/>
        <meta property="fb:app_id" content="111537044496"/>
        <meta property="og:site_name" content="Lonely Planet"/>
        <meta property="og:type" content="website"/>
        <meta name="twitter:card" content="summary_large_image"/>
        <meta name="twitter:site" content="@lonelyplanet"/>
        <meta name="twitter:site:id" content="15066760"/>
        <title>Places - Lonely Planet</title>
        <link rel="canonical" href="https://www.lonelyplanet.com/places"/>
        <meta name="title" content="Places - Lonely Planet"/>
        <meta name="description" content="Where to go, best places to stay, travel tips and and best holiday destinations - inspiration from the experts at Lonely Planet."/>
        <meta property="og:title" content="Places - Lonely Planet"/>
        <meta property="og:url" content="https://www.lonelyplanet.com/places"/>
        <meta property="og:description" content="Where to go, best places to stay, travel tips and and best holiday destinations - inspiration from the experts at Lonely Planet."/>
        <meta name="twitter:title" content="Places - Lonely Planet"/>
        <meta name="twitter:description" content="Where to go, best places to stay, travel tips and and best holiday destinations - inspiration from the experts at Lonely Planet."/>
        <meta name="next-head-count" content="17"/>
        <link rel="dns-prefetch" href="https://data.lonelyplanet.com"/>
        <link rel="dns-prefetch" href="https://assets.lonelyplanet.com"/>
        <link rel="dns-prefetch" href="https://lp-cms-production.imgix.net"/>
        <meta name="theme-color" content="#156ff5"/>
        <link rel="preconnect" href="https://cdn.cookielaw.org/scripttemplates/otSDKStub.js" crossorigin="anonymous"/>
        <link rel="preconnect" href="https://cohesion.lonelyplanet.com/"/>
        <link rel="preconnect" href="https://ingest.make.rvapps.io/"/>
        <link rel="preload" as="script" href="https://cohesion.lonelyplanet.com/cohesion/cohesion-latest.min.js"/>
        <script>
            window.NREUM = window.NREUM || {};
            NREUM.init = {
                distributed_tracing: {
                    enabled: true
                },
                privacy: {
                    cookies_enabled: true
                },
                ajax: {
                    deny_list: ["bam.nr-data.net"]
                }
            };
            NREUM.loader_config = {
                accountID: "6491642",
                trustKey: "78034",
                agentID: "*********",
                licenseKey: "NRBR-adda68b2e55f6ec6616",
                applicationID: "*********"
            };
            NREUM.info = {
                beacon: "bam.nr-data.net",
                errorBeacon: "bam.nr-data.net",
                licenseKey: "NRBR-adda68b2e55f6ec6616",
                applicationID: "*********",
                sa: 1
            };
        </script>
        <script src="https://js-agent.newrelic.com/nr-loader-full-1.285.0.min.js" async=""></script>
        <script id="cohesion-script" type="text/javascript" data-nscript="beforeInteractive">
            !function(co, h, e, s, i, o, n) {
                var d = "documentElement";
                var a = "className";
                h[d][a] += " preampjs fusejs";
                n.k = e;
                co._Cohesion = n;
                co._Preamp = {
                    k: s,
                    start: new Date
                };
                co._Fuse = {
                    k: i
                };
                co._Tagular = {
                    k: o
                };
                [e, s, i, o].map(function(x) {
                    co[x] = co[x] || function() {
                        (co[x].q = co[x].q || []).push([].slice.call(arguments))
                    }
                });
                h.addEventListener("DOMContentLoaded", function() {
                    co.setTimeout(function() {
                        var u = h[d][a];
                        h[d][a] = u.replace(/ ?preampjs| ?fusejs/g, "")
                    }, 3e3);
                    co._Preamp.docReady = co._Fuse.docReady = !0
                });
            }(window, document, "cohesion", "preamp", "fuse", "tagular", {
                cookieDomain: "lonelyplanet.com",
                domainAllowlist: ["lonelyplanet.com", "shop.lonelyplanet.com"],
                tagular: {
                    writeKey: "wk_1lg1SLc1gApxa9x71IwXDttlgDh",
                    sourceKey: "src_1lg1SLNP84Uk7Ocf2fMZGpo1k10",
                    authCookie: "lp_asc",
                    taggy: {
                        enabled: true
                    },
                    iframeAllowList: ['https://request.elsewhere.io'],
                    useBeacon: true,
                    apiHost: "https://beam.lonelyplanet.com/v2/t"
                },
                preamp: {
                    enabled: false
                },
                monarch: {
                    token: '9c67fe1d-f538-400a-908b-4dce6371a4f7',
                    sourceId: 'cbb6e396-b461-452c-bf13-7c6b19b13f8d',
                    endpoint: 'https://monarch.lonelyplanet.com'
                },
                consent: {
                    onetrust: {
                        enabled: true,
                        optIn: true,
                        timeout: 2500,
                    },
                }
            })
        </script>
        <link rel="preload" href="/_next/static/css/b5ca87bbac731b47.css" as="style"/>
        <link rel="stylesheet" href="/_next/static/css/b5ca87bbac731b47.css" data-n-g=""/>
        <link rel="preload" href="/_next/static/css/82f16536efbdcc7e.css" as="style"/>
        <link rel="stylesheet" href="/_next/static/css/82f16536efbdcc7e.css" data-n-p=""/>
        <noscript data-n-css=""></noscript>
        <script defer="" nomodule="" src="/_next/static/chunks/polyfills-42372ed130431b0a.js"></script>
        <script src="/_next/static/chunks/webpack-a25d1fef6411b12e.js" defer=""></script>
        <script src="/_next/static/chunks/framework-c6ee5607585ef091.js" defer=""></script>
        <script src="/_next/static/chunks/main-84d9b8bd41445b57.js" defer=""></script>
        <script src="/_next/static/chunks/pages/_app-88cce2fe25aa7ff2.js" defer=""></script>
        <script src="/_next/static/chunks/403-b19f0cb1eddedf59.js" defer=""></script>
        <script src="/_next/static/chunks/pages/places/%5B%5B...slug%5D%5D-3ae985e022c5893c.js" defer=""></script>
        <script src="/_next/static/895dd9511351f106f935bf8baadf4d8ccf502075/_buildManifest.js" defer=""></script>
        <script src="/_next/static/895dd9511351f106f935bf8baadf4d8ccf502075/_ssgManifest.js" defer=""></script>
        <style id="__jsx-21109142f51e79b3">
            .lp_consent .show-cookie-settings.jsx-21109142f51e79b3 {
                display: block
            }

            .grid-layout.jsx-21109142f51e79b3 {
                grid-template-areas: "l c tr""l c br"
            }

            .fn-0.jsx-21109142f51e79b3 {
                grid-area: l
            }

            .fn-1.jsx-21109142f51e79b3 {
                grid-area: c
            }

            .fn-2.jsx-21109142f51e79b3 {
                grid-area: tr
            }

            .fn-3.jsx-21109142f51e79b3 {
                grid-area: br
            }
        </style>
    </head>
    <body>
        <div id="__next">
            <div class="relative">
                <header class="relative z-50 border-b border-black-200 bg-black-100">
                    <nav class="container flex items-center justify-between h-16 px-4 mx-auto lg:justify-end gap-x-4 lg:gap-x-6 xl:gap-x-12" aria-label="Global Navigation">
                        <div class="lg:hidden">
                            <div class="relative group lg:px-8 lg:py-2">
                                <button type="button" class="flex items-center justify-center p-2 transition-colors rounded-full bg-black-100 hover:bg-blue hover:text-white lg:bg-transparent lg:p-0 lg:hover:bg-transparent lg:hover:text-blue" aria-label="Search">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" class="inline-flex" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                        <path d="M15.3542 14.646L11.9312 11.224C13.028 9.94433 13.5865 8.28959 13.4896 6.60701C13.3926 4.92443 12.6476 3.34483 11.411 2.19971C10.1744 1.05458 8.54232 0.432967 6.85726 0.465313C5.1722 0.49766 3.56517 1.18145 2.37343 2.37319C1.1817 3.56493 0.497904 5.17196 0.465557 6.85702C0.433211 8.54208 1.05483 10.1742 2.19995 11.4108C3.34507 12.6474 4.92467 13.3923 6.60725 13.4893C8.28983 13.5863 9.94457 13.0278 11.2242 11.931L14.6462 15.354C14.7401 15.4478 14.8674 15.5006 15.0002 15.5006C15.133 15.5006 15.2603 15.4478 15.3542 15.354C15.4481 15.2601 15.5008 15.1327 15.5008 15C15.5008 14.8672 15.4481 14.7398 15.3542 14.646ZM1.5002 6.99996C1.5002 5.91216 1.82277 4.84879 2.42712 3.94432C3.03147 3.03985 3.89045 2.3349 4.89544 1.91862C5.90044 1.50234 7.0063 1.39342 8.0732 1.60564C9.14009 1.81786 10.1201 2.34168 10.8893 3.11087C11.6585 3.88006 12.1823 4.86007 12.3945 5.92696C12.6067 6.99386 12.4978 8.09972 12.0815 9.10472C11.6653 10.1097 10.9603 10.9687 10.0558 11.573C9.15137 12.1774 8.088 12.5 7.0002 12.5C5.542 12.4984 4.14398 11.9184 3.11287 10.8873C2.08176 9.85618 1.50179 8.45816 1.5002 6.99996Z"></path>
                                    </svg>
                                    <span class="sr-only">Search</span>
                                </button>
                            </div>
                        </div>
                        <div class="flex justify-center flex-1 lg:flex-none lg:justify-start">
                            <a href="https://www.lonelyplanet.com" aria-label="Lonely Planet homepage" class="flex items-center">
                                <span class="sr-only">Lonely Planet</span>
                                <svg xmlns="http://www.w3.org/2000/svg" width="2em" height="1em" class="text-5xl" viewBox="0 0 711.06 98.68" fill="currentColor" aria-hidden="true">
                                    <path d="M219.05 75.21c-10.54 0-16.72-6.28-16.72-16.93V0h15.76v57.32c0 3 1.28 4.48 4.16 4.48h3.83v13.41ZM500.31 75.21c-10.54 0-16.72-6.28-16.72-16.93V0h15.76v57.32c0 3 1.28 4.48 4.16 4.48h3.83v13.41Z"></path>
                                    <path d="M278.13 21.77h-15.76v29c0 7.45-3.83 10.65-8.94 10.65s-9-3.2-9-10.65v-29h-15.71v29.94c0 14.69 11 23.53 23.08 23.53 3.86 0 9.54-2 10.7-5.71v2.74c0 8.52-6 12.67-12.68 12.67a16.23 16.23 0 0 1-13.95-7.13L225.33 87c6.39 9.37 16.61 11.71 24.07 11.71 17.36 0 28.75-10.22 28.75-27.37V21.77ZM27 47.52c0-17.14 12.25-29.07 28.86-29.07s29 11.93 29 29.07-12.28 29.08-29 29.08S27 64.67 27 47.52Zm41.75 0c0-9.79-5.54-15.12-12.89-15.12s-12.67 5.33-12.67 15.12 5.43 15.23 12.67 15.23 12.89-5.43 12.89-15.23ZM197.26 51.46h-37.6c1.18 8.42 6 11.82 12.89 11.82 5.43 0 9.58-1.81 12.57-6.28L196 65.84c-5.35 7.24-14 10.76-23.45 10.76-17.15 0-28.86-11.61-28.86-29.08 0-17.14 11.18-29.07 27.26-29.07 15.66 0 26.41 11.82 26.41 29.07 0 1.17 0 2.77-.1 3.94ZM160 41h21.6c-1.38-7.14-5.22-10.12-10.65-10.12-6 0-9.69 3.19-11 10.12ZM678.21 51.46h-37.59c1.17 8.42 6 11.82 12.89 11.82 5.43 0 9.58-1.81 12.56-6.28l10.86 8.84C671.61 73.08 663 76.6 653.51 76.6c-17.15 0-28.86-11.61-28.86-29.08 0-17.14 11.18-29.07 27.26-29.07 15.65 0 26.41 11.82 26.41 29.07 0 1.17 0 2.77-.11 3.94ZM640.94 41h21.62c-1.39-7.14-5.22-10.12-10.65-10.12-5.97.03-9.69 3.22-10.97 10.12ZM508.35 47.56c0 16.45 10.93 29.16 25.13 29.16 7.8 0 12.46-3.53 15.77-8.55v7H565V47.56c0-23-17.31-29.16-28-29.16-18.27 0-28.65 12.71-28.65 29.16Zm16.12.1c0-9.82 5.13-15.27 12.5-15.27s12.5 5.45 12.5 15.27-5.13 15.17-12.47 15.17-12.5-5.34-12.5-15.17ZM711.06 34H699v20.13q0 7.66 6.06 7.67h6v13.41h-8c-12.46 0-19.81-7.66-19.81-20.44V3l15.75.17v18.6h12ZM16.72 75.25C6.18 75.25 0 69 0 58.31V0h15.76v57.36c0 3 1.28 4.47 4.15 4.47h3.84v13.42ZM449.57 18.43c-10.69 0-28 6.2-28 29.16v45.82h15.7v-25.2c3.31 5 8 8.54 15.78 8.54 14.2 0 25.13-12.71 25.13-29.16s-10.4-29.16-28.61-29.16Zm0 44.44c-7.37 0-12.5-5.34-12.5-15.17s5.13-15.27 12.5-15.27S462 37.87 462 47.7s-5.12 15.17-12.49 15.17ZM139.11 42v33.21h-15.76V42.94c0-7.45-3.84-10.64-8.95-10.64s-8.94 3.19-8.94 10.64v32.27H89.69V42c0-14.7 10.65-23.54 24.71-23.54s24.71 8.83 24.71 23.54ZM620.56 42v33.21H604.8V42.94c0-7.45-3.84-10.64-8.95-10.64s-8.94 3.19-8.94 10.64v32.27h-15.76V42c0-14.7 10.64-23.54 24.7-23.54s24.71 8.83 24.71 23.54Z"></path>
                                    <path d="M350.12 1.05a45.71 45.71 0 1 0 17.48 3.48 45.66 45.66 0 0 0-17.48-3.48Zm1.95 87.45a42.38 42.38 0 0 1-9.67-.69.32.32 0 0 1-.2-.12.42.42 0 0 1-.08-.21.35.35 0 0 1 .08-.22.32.32 0 0 1 .2-.12c2.69-.54 7.94-1.91 12-4.91 3.86-2.86 6.85-7.51 4.62-13.18C353.81 55.8 338.28 59 336 59.6h-.11a.2.2 0 0 1-.09-.05.24.24 0 0 1-.06-.08.36.36 0 0 1 0-.1l.57-9.63c1-9-.88-12.18-.88-12.18-4.6-9.15-14.77-7.19-14.77-7.19a11.72 11.72 0 0 0-4.75 2 16.94 16.94 0 0 0-6.54 9.63.35.35 0 0 1-.14.2.34.34 0 0 1-.24.06.35.35 0 0 1-.29-.35 28.2 28.2 0 0 1 1.36-7 41.94 41.94 0 0 1 48.82-29.23.42.42 0 0 1 .24.14.46.46 0 0 1 .11.27.41.41 0 0 1-.09.26.41.41 0 0 1-.23.16 15 15 0 0 0-6.57 3.61s-3.86 3-4.31 10.49a23.17 23.17 0 0 0 .44 5.93l3.83 19.31a11 11 0 0 0 4.33 6.83 8.52 8.52 0 0 0 6.27 1.61s8.47-.39 7.1-8.8l-1-5a.54.54 0 0 1 0-.18.69.69 0 0 1 .07-.15.46.46 0 0 1 .12-.12.52.52 0 0 1 .16-.06c2.39-.38 13.63-2.94 14.24-17.37a.45.45 0 0 1 .09-.25.48.48 0 0 1 .22-.15.46.46 0 0 1 .26 0 .51.51 0 0 1 .21.15 41.88 41.88 0 0 1-32.3 66.14Z" fill="#0066CC"></path>
                                </svg>
                            </a>
                        </div>
                        <div class="hidden lg:flex lg:self-stretch lg:flex-1">
                            <div class="flex items-center justify-center flex-1">
                                <div class="relative flex group after:content-[&#x27;&#x27;] after:absolute after:top-0 after:left-0 after:right-0 after:h-full after:bg-transparent after:pointer-events-none after:z-[1] hover:after:h-[100vh] hover:after:pointer-events-auto" role="none">
                                    <button type="button" class="inline-flex items-center h-full px-8 py-2 group focus:outline-none hover:text-blue hover:underline" aria-expanded="false" aria-controls="submenu-Destinations" aria-haspopup="true">
                                        <a href="https://www.lonelyplanet.com/places" class="uppercase hover:text-blue focus:outline-none group-hover:text-blue group-hover:underline relative z-[2]" tabindex="-1">Destinations</a>
                                    </button>
                                    <div id="submenu-Destinations" class="fixed z-50 invisible opacity-0 group-hover:visible group-hover:opacity-100 bg-white shadow-lg transition-all duration-200 left-1/2 transform -translate-x-1/2 max-w-[1392px] w-[calc(100%-32px)] top-[8rem]" role="menu" aria-label="Destinations submenu">
                                        <div class="w-full">
                                            <div class="grid grid-cols-12 min-h-[400px]">
                                                <div class="flex flex-col h-full col-span-2 bg-black-100">
                                                    <nav class="px-6 pt-8" role="navigation" aria-label="Destinations submenu navigation">
                                                        <div class="space-y-1">
                                                            <button type="button" class="w-full text-left relative inline-block py-2 px-4 my-1 transition-transform duration-200 bg-blue-900 rotate-[-1.5deg] text-white" data-section-index="0" data-menu-title="Destinations" role="menuitem" aria-controls="section-Destinations-0">
                                                                <div class="text-sm uppercase text-white">Trending</div>
                                                            </button>
                                                            <button type="button" class="w-full text-left relative inline-block py-2 px-4 my-1 transition-transform duration-200 " data-section-index="1" data-menu-title="Destinations" role="menuitem" aria-controls="section-Destinations-1">
                                                                <div class="text-sm uppercase hover:text-blue cursor-pointer">Countries</div>
                                                            </button>
                                                            <button type="button" class="w-full text-left relative inline-block py-2 px-4 my-1 transition-transform duration-200 " data-section-index="2" data-menu-title="Destinations" role="menuitem" aria-controls="section-Destinations-2">
                                                                <div class="text-sm uppercase hover:text-blue cursor-pointer">Regions</div>
                                                            </button>
                                                            <button type="button" class="w-full text-left relative inline-block py-2 px-4 my-1 transition-transform duration-200 " data-section-index="3" data-menu-title="Destinations" role="menuitem" aria-controls="section-Destinations-3">
                                                                <div class="text-sm uppercase hover:text-blue cursor-pointer">Cities</div>
                                                            </button>
                                                            <button type="button" class="w-full text-left relative inline-block py-2 px-4 my-1 transition-transform duration-200 " data-section-index="4" data-menu-title="Destinations" role="menuitem" aria-controls="section-Destinations-4">
                                                                <div class="text-sm uppercase hover:text-blue cursor-pointer">Continents</div>
                                                            </button>
                                                        </div>
                                                    </nav>
                                                    <div class="px-6 pt-6 pb-8 mt-auto">
                                                        <a href="https://www.lonelyplanet.com/places" class="text-sm uppercase hover:text-blue" role="menuitem">view all destinations</a>
                                                    </div>
                                                </div>
                                                <div class="col-span-7 p-6">
                                                    <div class="grid grid-cols-1 gap-y-6 uppercase">
                                                        <div class="submenu-section" data-section-index="0" id="section-Destinations-0" style="display:block" role="region" aria-label="Trending">
                                                            <div class="grid grid-cols-3 gap-4">
                                                                <a href="https://www.lonelyplanet.com/destinations/greece/athens" class="block pb-2 mb-2 border-b border-black-200 hover:text-blue hover:border-blue group/arrow" role="menuitem">
                                                                    <img alt="Athens" loading="lazy" width="160" height="160" decoding="async" data-nimg="1" class="max-w-full object-cover w-full h-40 mb-2" style="color:transparent" sizes="(max-width: 768px) 100vw, 160px" srcSet="https://lp-cms-production.imgix.net/2020-11/GettyRF_663376932.jpg?w=640&amp;auto=format&amp;q=75 640w, https://lp-cms-production.imgix.net/2020-11/GettyRF_663376932.jpg?w=750&amp;auto=format&amp;q=75 750w, https://lp-cms-production.imgix.net/2020-11/GettyRF_663376932.jpg?w=828&amp;auto=format&amp;q=75 828w, https://lp-cms-production.imgix.net/2020-11/GettyRF_663376932.jpg?w=1024&amp;auto=format&amp;q=75 1024w, https://lp-cms-production.imgix.net/2020-11/GettyRF_663376932.jpg?w=1080&amp;auto=format&amp;q=75 1080w, https://lp-cms-production.imgix.net/2020-11/GettyRF_663376932.jpg?w=1200&amp;auto=format&amp;q=75 1200w, https://lp-cms-production.imgix.net/2020-11/GettyRF_663376932.jpg?w=1280&amp;auto=format&amp;q=75 1280w, https://lp-cms-production.imgix.net/2020-11/GettyRF_663376932.jpg?w=1920&amp;auto=format&amp;q=75 1920w, https://lp-cms-production.imgix.net/2020-11/GettyRF_663376932.jpg?w=2048&amp;auto=format&amp;q=75 2048w, https://lp-cms-production.imgix.net/2020-11/GettyRF_663376932.jpg?w=3840&amp;auto=format&amp;q=75 3840w" src="https://lp-cms-production.imgix.net/2020-11/GettyRF_663376932.jpg?w=3840&amp;auto=format&amp;q=75"/>
                                                                    <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                        Athens
                                                                        <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                            <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                        </svg>
                                                                    </span>
                                                                </a>
                                                                <a href="https://www.lonelyplanet.com/destinations/malta" class="block pb-2 mb-2 border-b border-black-200 hover:text-blue hover:border-blue group/arrow" role="menuitem">
                                                                    <img alt="Malta" loading="lazy" width="160" height="160" decoding="async" data-nimg="1" class="max-w-full object-cover w-full h-40 mb-2" style="color:transparent" sizes="(max-width: 768px) 100vw, 160px" srcSet="https://lp-cms-production.imgix.net/2022-11/Malta-iStock-1296277157-RFE.jpeg?w=640&amp;auto=format&amp;q=75 640w, https://lp-cms-production.imgix.net/2022-11/Malta-iStock-1296277157-RFE.jpeg?w=750&amp;auto=format&amp;q=75 750w, https://lp-cms-production.imgix.net/2022-11/Malta-iStock-1296277157-RFE.jpeg?w=828&amp;auto=format&amp;q=75 828w, https://lp-cms-production.imgix.net/2022-11/Malta-iStock-1296277157-RFE.jpeg?w=1024&amp;auto=format&amp;q=75 1024w, https://lp-cms-production.imgix.net/2022-11/Malta-iStock-1296277157-RFE.jpeg?w=1080&amp;auto=format&amp;q=75 1080w, https://lp-cms-production.imgix.net/2022-11/Malta-iStock-1296277157-RFE.jpeg?w=1200&amp;auto=format&amp;q=75 1200w, https://lp-cms-production.imgix.net/2022-11/Malta-iStock-1296277157-RFE.jpeg?w=1280&amp;auto=format&amp;q=75 1280w, https://lp-cms-production.imgix.net/2022-11/Malta-iStock-1296277157-RFE.jpeg?w=1920&amp;auto=format&amp;q=75 1920w, https://lp-cms-production.imgix.net/2022-11/Malta-iStock-1296277157-RFE.jpeg?w=2048&amp;auto=format&amp;q=75 2048w, https://lp-cms-production.imgix.net/2022-11/Malta-iStock-1296277157-RFE.jpeg?w=3840&amp;auto=format&amp;q=75 3840w" src="https://lp-cms-production.imgix.net/2022-11/Malta-iStock-1296277157-RFE.jpeg?w=3840&amp;auto=format&amp;q=75"/>
                                                                    <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                        Malta
                                                                        <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                            <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                        </svg>
                                                                    </span>
                                                                </a>
                                                                <a href="https://www.lonelyplanet.com/destinations/puerto-rico" class="block pb-2 mb-2 border-b border-black-200 hover:text-blue hover:border-blue group/arrow" role="menuitem">
                                                                    <img alt="Puerto Rico" loading="lazy" width="160" height="160" decoding="async" data-nimg="1" class="max-w-full object-cover w-full h-40 mb-2" style="color:transparent" sizes="(max-width: 768px) 100vw, 160px" srcSet="https://lp-cms-production.imgix.net/2025-04/shutterstockRF234381985.jpg?w=640&amp;auto=format&amp;q=75 640w, https://lp-cms-production.imgix.net/2025-04/shutterstockRF234381985.jpg?w=750&amp;auto=format&amp;q=75 750w, https://lp-cms-production.imgix.net/2025-04/shutterstockRF234381985.jpg?w=828&amp;auto=format&amp;q=75 828w, https://lp-cms-production.imgix.net/2025-04/shutterstockRF234381985.jpg?w=1024&amp;auto=format&amp;q=75 1024w, https://lp-cms-production.imgix.net/2025-04/shutterstockRF234381985.jpg?w=1080&amp;auto=format&amp;q=75 1080w, https://lp-cms-production.imgix.net/2025-04/shutterstockRF234381985.jpg?w=1200&amp;auto=format&amp;q=75 1200w, https://lp-cms-production.imgix.net/2025-04/shutterstockRF234381985.jpg?w=1280&amp;auto=format&amp;q=75 1280w, https://lp-cms-production.imgix.net/2025-04/shutterstockRF234381985.jpg?w=1920&amp;auto=format&amp;q=75 1920w, https://lp-cms-production.imgix.net/2025-04/shutterstockRF234381985.jpg?w=2048&amp;auto=format&amp;q=75 2048w, https://lp-cms-production.imgix.net/2025-04/shutterstockRF234381985.jpg?w=3840&amp;auto=format&amp;q=75 3840w" src="https://lp-cms-production.imgix.net/2025-04/shutterstockRF234381985.jpg?w=3840&amp;auto=format&amp;q=75"/>
                                                                    <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                        Puerto Rico
                                                                        <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                            <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                        </svg>
                                                                    </span>
                                                                </a>
                                                                <a href="https://www.lonelyplanet.com/destinations/mexico/cancun" class="block pb-2 mb-2 border-b border-black-200 hover:text-blue hover:border-blue group/arrow" role="menuitem">
                                                                    <img alt="Cancun" loading="lazy" width="160" height="160" decoding="async" data-nimg="1" class="max-w-full object-cover w-full h-40 mb-2" style="color:transparent" sizes="(max-width: 768px) 100vw, 160px" srcSet="https://lp-cms-production.imgix.net/2022-12/Mexico-iStock-484404659-RFC.jpeg?w=640&amp;auto=format&amp;q=75 640w, https://lp-cms-production.imgix.net/2022-12/Mexico-iStock-484404659-RFC.jpeg?w=750&amp;auto=format&amp;q=75 750w, https://lp-cms-production.imgix.net/2022-12/Mexico-iStock-484404659-RFC.jpeg?w=828&amp;auto=format&amp;q=75 828w, https://lp-cms-production.imgix.net/2022-12/Mexico-iStock-484404659-RFC.jpeg?w=1024&amp;auto=format&amp;q=75 1024w, https://lp-cms-production.imgix.net/2022-12/Mexico-iStock-484404659-RFC.jpeg?w=1080&amp;auto=format&amp;q=75 1080w, https://lp-cms-production.imgix.net/2022-12/Mexico-iStock-484404659-RFC.jpeg?w=1200&amp;auto=format&amp;q=75 1200w, https://lp-cms-production.imgix.net/2022-12/Mexico-iStock-484404659-RFC.jpeg?w=1280&amp;auto=format&amp;q=75 1280w, https://lp-cms-production.imgix.net/2022-12/Mexico-iStock-484404659-RFC.jpeg?w=1920&amp;auto=format&amp;q=75 1920w, https://lp-cms-production.imgix.net/2022-12/Mexico-iStock-484404659-RFC.jpeg?w=2048&amp;auto=format&amp;q=75 2048w, https://lp-cms-production.imgix.net/2022-12/Mexico-iStock-484404659-RFC.jpeg?w=3840&amp;auto=format&amp;q=75 3840w" src="https://lp-cms-production.imgix.net/2022-12/Mexico-iStock-484404659-RFC.jpeg?w=3840&amp;auto=format&amp;q=75"/>
                                                                    <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                        Cancun
                                                                        <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                            <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                        </svg>
                                                                    </span>
                                                                </a>
                                                                <a href="https://www.lonelyplanet.com/destinations/usa/miami" class="block pb-2 mb-2 border-b border-black-200 hover:text-blue hover:border-blue group/arrow" role="menuitem">
                                                                    <img alt="Miami" loading="lazy" width="160" height="160" decoding="async" data-nimg="1" class="max-w-full object-cover w-full h-40 mb-2" style="color:transparent" sizes="(max-width: 768px) 100vw, 160px" srcSet="https://lp-cms-production.imgix.net/2020-11/GettyRF_888220956.jpg?w=640&amp;auto=format&amp;q=75 640w, https://lp-cms-production.imgix.net/2020-11/GettyRF_888220956.jpg?w=750&amp;auto=format&amp;q=75 750w, https://lp-cms-production.imgix.net/2020-11/GettyRF_888220956.jpg?w=828&amp;auto=format&amp;q=75 828w, https://lp-cms-production.imgix.net/2020-11/GettyRF_888220956.jpg?w=1024&amp;auto=format&amp;q=75 1024w, https://lp-cms-production.imgix.net/2020-11/GettyRF_888220956.jpg?w=1080&amp;auto=format&amp;q=75 1080w, https://lp-cms-production.imgix.net/2020-11/GettyRF_888220956.jpg?w=1200&amp;auto=format&amp;q=75 1200w, https://lp-cms-production.imgix.net/2020-11/GettyRF_888220956.jpg?w=1280&amp;auto=format&amp;q=75 1280w, https://lp-cms-production.imgix.net/2020-11/GettyRF_888220956.jpg?w=1920&amp;auto=format&amp;q=75 1920w, https://lp-cms-production.imgix.net/2020-11/GettyRF_888220956.jpg?w=2048&amp;auto=format&amp;q=75 2048w, https://lp-cms-production.imgix.net/2020-11/GettyRF_888220956.jpg?w=3840&amp;auto=format&amp;q=75 3840w" src="https://lp-cms-production.imgix.net/2020-11/GettyRF_888220956.jpg?w=3840&amp;auto=format&amp;q=75"/>
                                                                    <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                        Miami
                                                                        <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                            <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                        </svg>
                                                                    </span>
                                                                </a>
                                                                <a href="https://www.lonelyplanet.com/destinations/usa/rocky-mountains/yellowstone-national-park" class="block pb-2 mb-2 border-b border-black-200 hover:text-blue hover:border-blue group/arrow" role="menuitem">
                                                                    <img alt="Yellowstone" loading="lazy" width="160" height="160" decoding="async" data-nimg="1" class="max-w-full object-cover w-full h-40 mb-2" style="color:transparent" sizes="(max-width: 768px) 100vw, 160px" srcSet="https://lp-cms-production.imgix.net/2025-04/shutterstock287588156.jpg?w=640&amp;auto=format&amp;q=75 640w, https://lp-cms-production.imgix.net/2025-04/shutterstock287588156.jpg?w=750&amp;auto=format&amp;q=75 750w, https://lp-cms-production.imgix.net/2025-04/shutterstock287588156.jpg?w=828&amp;auto=format&amp;q=75 828w, https://lp-cms-production.imgix.net/2025-04/shutterstock287588156.jpg?w=1024&amp;auto=format&amp;q=75 1024w, https://lp-cms-production.imgix.net/2025-04/shutterstock287588156.jpg?w=1080&amp;auto=format&amp;q=75 1080w, https://lp-cms-production.imgix.net/2025-04/shutterstock287588156.jpg?w=1200&amp;auto=format&amp;q=75 1200w, https://lp-cms-production.imgix.net/2025-04/shutterstock287588156.jpg?w=1280&amp;auto=format&amp;q=75 1280w, https://lp-cms-production.imgix.net/2025-04/shutterstock287588156.jpg?w=1920&amp;auto=format&amp;q=75 1920w, https://lp-cms-production.imgix.net/2025-04/shutterstock287588156.jpg?w=2048&amp;auto=format&amp;q=75 2048w, https://lp-cms-production.imgix.net/2025-04/shutterstock287588156.jpg?w=3840&amp;auto=format&amp;q=75 3840w" src="https://lp-cms-production.imgix.net/2025-04/shutterstock287588156.jpg?w=3840&amp;auto=format&amp;q=75"/>
                                                                    <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                        Yellowstone
                                                                        <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                            <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                        </svg>
                                                                    </span>
                                                                </a>
                                                            </div>
                                                        </div>
                                                        <div class="submenu-section" data-section-index="1" id="section-Destinations-1" style="display:none" role="region" aria-label="Countries">
                                                            <div class="grid grid-cols-3 gap-4">
                                                                <div>
                                                                    <p class="mb-4 text-sm font-semibold">Europe</p>
                                                                    <a href="https://www.lonelyplanet.com/destinations/croatia" class="block pb-2 mb-2 border-b hover:text-blue border-black-200 hover:border-blue group/arrow" role="menuitem">
                                                                        <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                            Croatia
                                                                            <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                                <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                            </svg>
                                                                        </span>
                                                                    </a>
                                                                    <a href="https://www.lonelyplanet.com/destinations/france" class="block pb-2 mb-2 border-b hover:text-blue border-black-200 hover:border-blue group/arrow" role="menuitem">
                                                                        <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                            France
                                                                            <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                                <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                            </svg>
                                                                        </span>
                                                                    </a>
                                                                    <a href="https://www.lonelyplanet.com/destinations/greece" class="block pb-2 mb-2 border-b hover:text-blue border-black-200 hover:border-blue group/arrow" role="menuitem">
                                                                        <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                            Greece
                                                                            <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                                <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                            </svg>
                                                                        </span>
                                                                    </a>
                                                                    <a href="https://www.lonelyplanet.com/destinations/iceland" class="block pb-2 mb-2 border-b hover:text-blue border-black-200 hover:border-blue group/arrow" role="menuitem">
                                                                        <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                            Iceland
                                                                            <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                                <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                            </svg>
                                                                        </span>
                                                                    </a>
                                                                    <a href="https://www.lonelyplanet.com/destinations/italy" class="block pb-2 mb-2 border-b hover:text-blue border-black-200 hover:border-blue group/arrow" role="menuitem">
                                                                        <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                            Italy
                                                                            <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                                <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                            </svg>
                                                                        </span>
                                                                    </a>
                                                                    <a href="https://www.lonelyplanet.com/destinations/portugal" class="block pb-2 mb-2 border-b hover:text-blue border-black-200 hover:border-blue group/arrow" role="menuitem">
                                                                        <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                            Portugal
                                                                            <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                                <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                            </svg>
                                                                        </span>
                                                                    </a>
                                                                    <a href="https://www.lonelyplanet.com/destinations/spain" class="block pb-2 mb-2 border-b hover:text-blue border-black-200 hover:border-blue group/arrow" role="menuitem">
                                                                        <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                            Spain
                                                                            <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                                <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                            </svg>
                                                                        </span>
                                                                    </a>
                                                                </div>
                                                                <div>
                                                                    <p class="mb-4 text-sm font-semibold">The Americas</p>
                                                                    <a href="https://www.lonelyplanet.com/destinations/canada" class="block pb-2 mb-2 border-b hover:text-blue border-black-200 hover:border-blue group/arrow" role="menuitem">
                                                                        <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                            Canada
                                                                            <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                                <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                            </svg>
                                                                        </span>
                                                                    </a>
                                                                    <a href="https://www.lonelyplanet.com/destinations/costa-rica" class="block pb-2 mb-2 border-b hover:text-blue border-black-200 hover:border-blue group/arrow" role="menuitem">
                                                                        <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                            Costa Rica
                                                                            <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                                <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                            </svg>
                                                                        </span>
                                                                    </a>
                                                                    <a href="https://www.lonelyplanet.com/destinations/mexico" class="block pb-2 mb-2 border-b hover:text-blue border-black-200 hover:border-blue group/arrow" role="menuitem">
                                                                        <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                            Mexico
                                                                            <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                                <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                            </svg>
                                                                        </span>
                                                                    </a>
                                                                    <a href="https://www.lonelyplanet.com/destinations/peru" class="block pb-2 mb-2 border-b hover:text-blue border-black-200 hover:border-blue group/arrow" role="menuitem">
                                                                        <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                            Peru
                                                                            <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                                <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                            </svg>
                                                                        </span>
                                                                    </a>
                                                                    <a href="https://www.lonelyplanet.com/destinations/usa" class="block pb-2 mb-2 border-b hover:text-blue border-black-200 hover:border-blue group/arrow" role="menuitem">
                                                                        <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                            United States
                                                                            <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                                <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                            </svg>
                                                                        </span>
                                                                    </a>
                                                                </div>
                                                                <div>
                                                                    <p class="mb-4 text-sm font-semibold">More</p>
                                                                    <a href="https://www.lonelyplanet.com/destinations/australia" class="block pb-2 mb-2 border-b hover:text-blue border-black-200 hover:border-blue group/arrow" role="menuitem">
                                                                        <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                            Australia
                                                                            <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                                <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                            </svg>
                                                                        </span>
                                                                    </a>
                                                                    <a href="https://www.lonelyplanet.com/destinations/japan" class="block pb-2 mb-2 border-b hover:text-blue border-black-200 hover:border-blue group/arrow" role="menuitem">
                                                                        <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                            Japan
                                                                            <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                                <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                            </svg>
                                                                        </span>
                                                                    </a>
                                                                    <a href="https://www.lonelyplanet.com/destinations/morocco" class="block pb-2 mb-2 border-b hover:text-blue border-black-200 hover:border-blue group/arrow" role="menuitem">
                                                                        <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                            Morocco
                                                                            <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                                <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                            </svg>
                                                                        </span>
                                                                    </a>
                                                                    <a href="https://www.lonelyplanet.com/destinations/sri-lanka" class="block pb-2 mb-2 border-b hover:text-blue border-black-200 hover:border-blue group/arrow" role="menuitem">
                                                                        <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                            Sri Lanka
                                                                            <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                                <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                            </svg>
                                                                        </span>
                                                                    </a>
                                                                    <a href="https://www.lonelyplanet.com/destinations/thailand" class="block pb-2 mb-2 border-b hover:text-blue border-black-200 hover:border-blue group/arrow" role="menuitem">
                                                                        <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                            Thailand
                                                                            <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                                <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                            </svg>
                                                                        </span>
                                                                    </a>
                                                                    <a href="https://www.lonelyplanet.com/destinations/turkey" class="block pb-2 mb-2 border-b hover:text-blue border-black-200 hover:border-blue group/arrow" role="menuitem">
                                                                        <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                            Turkey
                                                                            <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                                <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                            </svg>
                                                                        </span>
                                                                    </a>
                                                                    <a href="https://www.lonelyplanet.com/destinations/vietnam" class="block pb-2 mb-2 border-b hover:text-blue border-black-200 hover:border-blue group/arrow" role="menuitem">
                                                                        <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                            Vietnam
                                                                            <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                                <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                            </svg>
                                                                        </span>
                                                                    </a>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="submenu-section" data-section-index="2" id="section-Destinations-2" style="display:none" role="region" aria-label="Regions">
                                                            <div class="grid grid-cols-3 gap-4">
                                                                <div>
                                                                    <p class="mb-4 text-sm font-semibold">Europe</p>
                                                                    <a href="https://www.lonelyplanet.com/destinations/italy/amalfi-coast" class="block pb-2 mb-2 border-b hover:text-blue border-black-200 hover:border-blue group/arrow" role="menuitem">
                                                                        <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                            Amalfi Coast
                                                                            <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                                <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                            </svg>
                                                                        </span>
                                                                    </a>
                                                                    <a href="https://www.lonelyplanet.com/destinations/portugal/the-azores-1341351" class="block pb-2 mb-2 border-b hover:text-blue border-black-200 hover:border-blue group/arrow" role="menuitem">
                                                                        <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                            Azores
                                                                            <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                                <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                            </svg>
                                                                        </span>
                                                                    </a>
                                                                    <a href="https://www.lonelyplanet.com/destinations/italy/cinque-terre" class="block pb-2 mb-2 border-b hover:text-blue border-black-200 hover:border-blue group/arrow" role="menuitem">
                                                                        <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                            Cinque Terre
                                                                            <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                                <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                            </svg>
                                                                        </span>
                                                                    </a>
                                                                    <a href="https://www.lonelyplanet.com/destinations/italy/puglia" class="block pb-2 mb-2 border-b hover:text-blue border-black-200 hover:border-blue group/arrow" role="menuitem">
                                                                        <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                            Puglia
                                                                            <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                                <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                            </svg>
                                                                        </span>
                                                                    </a>
                                                                    <a href="https://www.lonelyplanet.com/destinations/italy/sardinia" class="block pb-2 mb-2 border-b hover:text-blue border-black-200 hover:border-blue group/arrow" role="menuitem">
                                                                        <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                            Sardinia
                                                                            <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                                <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                            </svg>
                                                                        </span>
                                                                    </a>
                                                                    <a href="https://www.lonelyplanet.com/destinations/italy/sicily" class="block pb-2 mb-2 border-b hover:text-blue border-black-200 hover:border-blue group/arrow" role="menuitem">
                                                                        <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                            Sicily
                                                                            <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                                <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                            </svg>
                                                                        </span>
                                                                    </a>
                                                                    <a href="https://www.lonelyplanet.com/destinations/italy/tuscany" class="block pb-2 mb-2 border-b hover:text-blue border-black-200 hover:border-blue group/arrow" role="menuitem">
                                                                        <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                            Tuscany
                                                                            <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                                <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                            </svg>
                                                                        </span>
                                                                    </a>
                                                                </div>
                                                                <div>
                                                                    <p class="mb-4 text-sm font-semibold">The Americas</p>
                                                                    <a href="https://www.lonelyplanet.com/destinations/usa/alaska" class="block pb-2 mb-2 border-b hover:text-blue border-black-200 hover:border-blue group/arrow" role="menuitem">
                                                                        <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                            Alaska
                                                                            <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                                <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                            </svg>
                                                                        </span>
                                                                    </a>
                                                                    <a href="https://www.lonelyplanet.com/destinations/usa/california" class="block pb-2 mb-2 border-b hover:text-blue border-black-200 hover:border-blue group/arrow" role="menuitem">
                                                                        <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                            California
                                                                            <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                                <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                            </svg>
                                                                        </span>
                                                                    </a>
                                                                    <a href="https://www.lonelyplanet.com/destinations/usa/rocky-mountains/colorado" class="block pb-2 mb-2 border-b hover:text-blue border-black-200 hover:border-blue group/arrow" role="menuitem">
                                                                        <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                            Colorado
                                                                            <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                                <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                            </svg>
                                                                        </span>
                                                                    </a>
                                                                    <a href="https://www.lonelyplanet.com/destinations/usa/florida" class="block pb-2 mb-2 border-b hover:text-blue border-black-200 hover:border-blue group/arrow" role="menuitem">
                                                                        <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                            Florida
                                                                            <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                                <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                            </svg>
                                                                        </span>
                                                                    </a>
                                                                    <a href="https://www.lonelyplanet.com/destinations/usa/hawaii" class="block pb-2 mb-2 border-b hover:text-blue border-black-200 hover:border-blue group/arrow" role="menuitem">
                                                                        <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                            Hawaii
                                                                            <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                                <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                            </svg>
                                                                        </span>
                                                                    </a>
                                                                    <a href="https://www.lonelyplanet.com/destinations/argentina/patagonia" class="block pb-2 mb-2 border-b hover:text-blue border-black-200 hover:border-blue group/arrow" role="menuitem">
                                                                        <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                            Patagonia
                                                                            <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                                <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                            </svg>
                                                                        </span>
                                                                    </a>
                                                                    <a href="https://www.lonelyplanet.com/destinations/usa/texas" class="block pb-2 mb-2 border-b hover:text-blue border-black-200 hover:border-blue group/arrow" role="menuitem">
                                                                        <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                            Texas
                                                                            <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                                <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                            </svg>
                                                                        </span>
                                                                    </a>
                                                                </div>
                                                                <div>
                                                                    <p class="mb-4 text-sm font-semibold">More</p>
                                                                    <a href="https://www.lonelyplanet.com/destinations/indonesia/bali" class="block pb-2 mb-2 border-b hover:text-blue border-black-200 hover:border-blue group/arrow" role="menuitem">
                                                                        <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                            Bali
                                                                            <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                                <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                            </svg>
                                                                        </span>
                                                                    </a>
                                                                    <a href="https://www.lonelyplanet.com/destinations/thailand/phuket-province" class="block pb-2 mb-2 border-b hover:text-blue border-black-200 hover:border-blue group/arrow" role="menuitem">
                                                                        <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                            Phuket
                                                                            <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                                <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                            </svg>
                                                                        </span>
                                                                    </a>
                                                                    <a href="https://www.lonelyplanet.com/destinations/southeast-asia" class="block pb-2 mb-2 border-b hover:text-blue border-black-200 hover:border-blue group/arrow" role="menuitem">
                                                                        <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                            Southeast Asia
                                                                            <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                                <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                            </svg>
                                                                        </span>
                                                                    </a>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="submenu-section" data-section-index="3" id="section-Destinations-3" style="display:none" role="region" aria-label="Cities">
                                                            <div class="grid grid-cols-3 gap-4">
                                                                <div>
                                                                    <p class="mb-4 text-sm font-semibold">Europe</p>
                                                                    <a href="https://www.lonelyplanet.com/destinations/spain/barcelona" class="block pb-2 mb-2 border-b hover:text-blue border-black-200 hover:border-blue group/arrow" role="menuitem">
                                                                        <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                            Barcelona
                                                                            <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                                <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                            </svg>
                                                                        </span>
                                                                    </a>
                                                                    <a href="https://www.lonelyplanet.com/destinations/england/london" class="block pb-2 mb-2 border-b hover:text-blue border-black-200 hover:border-blue group/arrow" role="menuitem">
                                                                        <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                            London
                                                                            <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                                <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                            </svg>
                                                                        </span>
                                                                    </a>
                                                                    <a href="https://www.lonelyplanet.com/destinations/spain/madrid" class="block pb-2 mb-2 border-b hover:text-blue border-black-200 hover:border-blue group/arrow" role="menuitem">
                                                                        <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                            Madrid
                                                                            <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                                <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                            </svg>
                                                                        </span>
                                                                    </a>
                                                                    <a href="https://www.lonelyplanet.com/destinations/france/paris" class="block pb-2 mb-2 border-b hover:text-blue border-black-200 hover:border-blue group/arrow" role="menuitem">
                                                                        <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                            Paris
                                                                            <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                                <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                            </svg>
                                                                        </span>
                                                                    </a>
                                                                    <a href="https://www.lonelyplanet.com/destinations/portugal/the-north/porto" class="block pb-2 mb-2 border-b hover:text-blue border-black-200 hover:border-blue group/arrow" role="menuitem">
                                                                        <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                            Porto
                                                                            <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                                <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                            </svg>
                                                                        </span>
                                                                    </a>
                                                                    <a href="https://www.lonelyplanet.com/destinations/italy/rome" class="block pb-2 mb-2 border-b hover:text-blue border-black-200 hover:border-blue group/arrow" role="menuitem">
                                                                        <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                            Rome
                                                                            <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                                <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                            </svg>
                                                                        </span>
                                                                    </a>
                                                                    <a href="https://www.lonelyplanet.com/destinations/greece/cyclades/santorini-thira" class="block pb-2 mb-2 border-b hover:text-blue border-black-200 hover:border-blue group/arrow" role="menuitem">
                                                                        <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                            Santorini
                                                                            <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                                <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                            </svg>
                                                                        </span>
                                                                    </a>
                                                                </div>
                                                                <div>
                                                                    <p class="mb-4 text-sm font-semibold">The Americas</p>
                                                                    <a href="https://www.lonelyplanet.com/destinations/argentina/buenos-aires" class="block pb-2 mb-2 border-b hover:text-blue border-black-200 hover:border-blue group/arrow" role="menuitem">
                                                                        <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                            Buenos Aires
                                                                            <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                                <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                            </svg>
                                                                        </span>
                                                                    </a>
                                                                    <a href="https://www.lonelyplanet.com/destinations/peru/lima" class="block pb-2 mb-2 border-b hover:text-blue border-black-200 hover:border-blue group/arrow" role="menuitem">
                                                                        <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                            Lima
                                                                            <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                                <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                            </svg>
                                                                        </span>
                                                                    </a>
                                                                    <a href="https://www.lonelyplanet.com/destinations/usa/los-angeles" class="block pb-2 mb-2 border-b hover:text-blue border-black-200 hover:border-blue group/arrow" role="menuitem">
                                                                        <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                            Los Angeles
                                                                            <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                                <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                            </svg>
                                                                        </span>
                                                                    </a>
                                                                    <a href="https://www.lonelyplanet.com/destinations/mexico/mexico-city" class="block pb-2 mb-2 border-b hover:text-blue border-black-200 hover:border-blue group/arrow" role="menuitem">
                                                                        <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                            Mexico City
                                                                            <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                                <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                            </svg>
                                                                        </span>
                                                                    </a>
                                                                    <a href="https://www.lonelyplanet.com/destinations/usa/new-orleans" class="block pb-2 mb-2 border-b hover:text-blue border-black-200 hover:border-blue group/arrow" role="menuitem">
                                                                        <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                            New Orleans
                                                                            <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                                <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                            </svg>
                                                                        </span>
                                                                    </a>
                                                                    <a href="https://www.lonelyplanet.com/destinations/usa/new-york-city" class="block pb-2 mb-2 border-b hover:text-blue border-black-200 hover:border-blue group/arrow" role="menuitem">
                                                                        <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                            New York City
                                                                            <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                                <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                            </svg>
                                                                        </span>
                                                                    </a>
                                                                    <a href="https://www.lonelyplanet.com/destinations/usa/san-francisco" class="block pb-2 mb-2 border-b hover:text-blue border-black-200 hover:border-blue group/arrow" role="menuitem">
                                                                        <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                            San Francisco
                                                                            <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                                <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                            </svg>
                                                                        </span>
                                                                    </a>
                                                                </div>
                                                                <div>
                                                                    <p class="mb-4 text-sm font-semibold">More</p>
                                                                    <a href="https://www.lonelyplanet.com/destinations/thailand/bangkok" class="block pb-2 mb-2 border-b hover:text-blue border-black-200 hover:border-blue group/arrow" role="menuitem">
                                                                        <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                            Bangkok
                                                                            <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                                <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                            </svg>
                                                                        </span>
                                                                    </a>
                                                                    <a href="https://www.lonelyplanet.com/destinations/thailand/chiang-mai-province/chiang-mai" class="block pb-2 mb-2 border-b hover:text-blue border-black-200 hover:border-blue group/arrow" role="menuitem">
                                                                        <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                            Chiang Mai
                                                                            <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                                <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                            </svg>
                                                                        </span>
                                                                    </a>
                                                                    <a href="https://www.lonelyplanet.com/destinations/vietnam/ho-chi-minh-city" class="block pb-2 mb-2 border-b hover:text-blue border-black-200 hover:border-blue group/arrow" role="menuitem">
                                                                        <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                            Ho Chi Minh City
                                                                            <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                                <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                            </svg>
                                                                        </span>
                                                                    </a>
                                                                    <a href="https://www.lonelyplanet.com/destinations/turkey/istanbul" class="block pb-2 mb-2 border-b hover:text-blue border-black-200 hover:border-blue group/arrow" role="menuitem">
                                                                        <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                            Istanbul
                                                                            <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                                <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                            </svg>
                                                                        </span>
                                                                    </a>
                                                                    <a href="https://www.lonelyplanet.com/destinations/japan/kansai/kyoto" class="block pb-2 mb-2 border-b hover:text-blue border-black-200 hover:border-blue group/arrow" role="menuitem">
                                                                        <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                            Kyoto
                                                                            <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                                <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                            </svg>
                                                                        </span>
                                                                    </a>
                                                                    <a href="https://www.lonelyplanet.com/destinations/morocco/marrakesh" class="block pb-2 mb-2 border-b hover:text-blue border-black-200 hover:border-blue group/arrow" role="menuitem">
                                                                        <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                            Marrakesh
                                                                            <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                                <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                            </svg>
                                                                        </span>
                                                                    </a>
                                                                    <a href="https://www.lonelyplanet.com/destinations/japan/tokyo" class="block pb-2 mb-2 border-b hover:text-blue border-black-200 hover:border-blue group/arrow" role="menuitem">
                                                                        <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                            Tokyo
                                                                            <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                                <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                            </svg>
                                                                        </span>
                                                                    </a>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="submenu-section" data-section-index="4" id="section-Destinations-4" style="display:none" role="region" aria-label="Continents">
                                                            <div class="grid grid-cols-3 gap-4">
                                                                <a href="https://www.lonelyplanet.com/destinations/africa" class="block pb-2 mb-2 border-b border-black-200 hover:text-blue hover:border-blue group/arrow" role="menuitem">
                                                                    <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                        Africa
                                                                        <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                            <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                        </svg>
                                                                    </span>
                                                                </a>
                                                                <a href="https://www.lonelyplanet.com/destinations/antarctica" class="block pb-2 mb-2 border-b border-black-200 hover:text-blue hover:border-blue group/arrow" role="menuitem">
                                                                    <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                        Antarctica
                                                                        <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                            <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                        </svg>
                                                                    </span>
                                                                </a>
                                                                <a href="https://www.lonelyplanet.com/destinations/asia" class="block pb-2 mb-2 border-b border-black-200 hover:text-blue hover:border-blue group/arrow" role="menuitem">
                                                                    <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                        Asia
                                                                        <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                            <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                        </svg>
                                                                    </span>
                                                                </a>
                                                                <a href="https://www.lonelyplanet.com/destinations/pacific" class="block pb-2 mb-2 border-b border-black-200 hover:text-blue hover:border-blue group/arrow" role="menuitem">
                                                                    <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                        Australia &amp;The Pacific
                                                                        <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                            <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                        </svg>
                                                                    </span>
                                                                </a>
                                                                <a href="https://www.lonelyplanet.com/destinations/caribbean" class="block pb-2 mb-2 border-b border-black-200 hover:text-blue hover:border-blue group/arrow" role="menuitem">
                                                                    <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                        The Caribbean
                                                                        <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                            <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                        </svg>
                                                                    </span>
                                                                </a>
                                                                <a href="https://www.lonelyplanet.com/destinations/central-america" class="block pb-2 mb-2 border-b border-black-200 hover:text-blue hover:border-blue group/arrow" role="menuitem">
                                                                    <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                        Central America
                                                                        <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                            <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                        </svg>
                                                                    </span>
                                                                </a>
                                                                <a href="https://www.lonelyplanet.com/destinations/europe" class="block pb-2 mb-2 border-b border-black-200 hover:text-blue hover:border-blue group/arrow" role="menuitem">
                                                                    <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                        Europe
                                                                        <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                            <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                        </svg>
                                                                    </span>
                                                                </a>
                                                                <a href="https://www.lonelyplanet.com/destinations/middle-east" class="block pb-2 mb-2 border-b border-black-200 hover:text-blue hover:border-blue group/arrow" role="menuitem">
                                                                    <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                        Middle East
                                                                        <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                            <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                        </svg>
                                                                    </span>
                                                                </a>
                                                                <a href="https://www.lonelyplanet.com/destinations/north-america" class="block pb-2 mb-2 border-b border-black-200 hover:text-blue hover:border-blue group/arrow" role="menuitem">
                                                                    <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                        North America
                                                                        <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                            <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                        </svg>
                                                                    </span>
                                                                </a>
                                                                <a href="https://www.lonelyplanet.com/destinations/south-america" class="block pb-2 mb-2 border-b border-black-200 hover:text-blue hover:border-blue group/arrow" role="menuitem">
                                                                    <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                        South America
                                                                        <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                            <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                        </svg>
                                                                    </span>
                                                                </a>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-span-3 p-6 bg-white border-l border-black-200">
                                                    <div>
                                                        <img alt="" loading="lazy" width="350" height="200" decoding="async" data-nimg="1" class="max-w-full object-cover w-full h-48 mb-4" style="color:transparent" sizes="(max-width: 768px) 100vw, 350px" srcSet="https://lonelyplanetstatic.imgix.net/marketing/2025/global_nav/nav_bit.jpg?w=640&amp;auto=format&amp;q=75 640w, https://lonelyplanetstatic.imgix.net/marketing/2025/global_nav/nav_bit.jpg?w=750&amp;auto=format&amp;q=75 750w, https://lonelyplanetstatic.imgix.net/marketing/2025/global_nav/nav_bit.jpg?w=828&amp;auto=format&amp;q=75 828w, https://lonelyplanetstatic.imgix.net/marketing/2025/global_nav/nav_bit.jpg?w=1024&amp;auto=format&amp;q=75 1024w, https://lonelyplanetstatic.imgix.net/marketing/2025/global_nav/nav_bit.jpg?w=1080&amp;auto=format&amp;q=75 1080w, https://lonelyplanetstatic.imgix.net/marketing/2025/global_nav/nav_bit.jpg?w=1200&amp;auto=format&amp;q=75 1200w, https://lonelyplanetstatic.imgix.net/marketing/2025/global_nav/nav_bit.jpg?w=1280&amp;auto=format&amp;q=75 1280w, https://lonelyplanetstatic.imgix.net/marketing/2025/global_nav/nav_bit.jpg?w=1920&amp;auto=format&amp;q=75 1920w, https://lonelyplanetstatic.imgix.net/marketing/2025/global_nav/nav_bit.jpg?w=2048&amp;auto=format&amp;q=75 2048w, https://lonelyplanetstatic.imgix.net/marketing/2025/global_nav/nav_bit.jpg?w=3840&amp;auto=format&amp;q=75 3840w" src="https://lonelyplanetstatic.imgix.net/marketing/2025/global_nav/nav_bit.jpg?w=3840&amp;auto=format&amp;q=75"/>
                                                        <h3 class="mb-2 text-xl">Not sure where to start?</h3>
                                                        <p class="mb-4 text-sm">Get inspired by destinations chosen in this year &#x27;s Best in Travel list.</p>
                                                        <a href="https://www.lonelyplanet.com/best-in-travel" class="inline-block w-full px-4 py-2 text-sm text-center text-white uppercase transition-colors bg-black rounded hover:bg-blue" role="menuitem">Start Discovering</a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="relative flex group after:content-[&#x27;&#x27;] after:absolute after:top-0 after:left-0 after:right-0 after:h-full after:bg-transparent after:pointer-events-none after:z-[1] hover:after:h-[100vh] hover:after:pointer-events-auto" role="none">
                                    <button type="button" class="inline-flex items-center h-full px-8 py-2 group focus:outline-none hover:text-blue hover:underline" aria-expanded="false" aria-controls="submenu-Books" aria-haspopup="true">
                                        <a href="https://shop.lonelyplanet.com/?utm_source=lonelyplanet&amp;utm_campaign=navigation&amp;utm_content=homepage" class="uppercase hover:text-blue focus:outline-none group-hover:text-blue group-hover:underline relative z-[2]" tabindex="-1">Books</a>
                                    </button>
                                    <div id="submenu-Books" class="fixed z-50 invisible opacity-0 group-hover:visible group-hover:opacity-100 bg-white shadow-lg transition-all duration-200 left-1/2 transform -translate-x-1/2 max-w-[1392px] w-[calc(100%-32px)] top-[8rem]" role="menu" aria-label="Books submenu">
                                        <div class="w-full">
                                            <div class="grid grid-cols-12 min-h-[400px]">
                                                <div class="flex flex-col h-full col-span-2 bg-black-100">
                                                    <nav class="px-6 pt-8" role="navigation" aria-label="Books submenu navigation">
                                                        <div class="space-y-1">
                                                            <button type="button" class="w-full text-left relative inline-block py-2 px-4 my-1 transition-transform duration-200 bg-blue-900 rotate-[-1.5deg] text-white" data-section-index="0" data-menu-title="Books" role="menuitem" aria-controls="section-Books-0">
                                                                <div class="text-sm uppercase text-white">Destination Guides</div>
                                                            </button>
                                                            <button type="button" class="w-full text-left relative inline-block py-2 px-4 my-1 transition-transform duration-200 " data-section-index="1" data-menu-title="Books" role="menuitem" aria-controls="section-Books-1">
                                                                <div class="text-sm uppercase hover:text-blue cursor-pointer">Trending Guides</div>
                                                            </button>
                                                            <button type="button" class="w-full text-left relative inline-block py-2 px-4 my-1 transition-transform duration-200 " data-section-index="2" data-menu-title="Books" role="menuitem" aria-controls="section-Books-2">
                                                                <div class="text-sm uppercase hover:text-blue cursor-pointer">Gifts &amp;New Releases</div>
                                                            </button>
                                                            <button type="button" class="w-full text-left relative inline-block py-2 px-4 my-1 transition-transform duration-200 " data-section-index="3" data-menu-title="Books" role="menuitem" aria-controls="section-Books-3">
                                                                <div class="text-sm uppercase hover:text-blue cursor-pointer">Guide Types</div>
                                                            </button>
                                                        </div>
                                                    </nav>
                                                    <div class="px-6 pt-6 pb-8 mt-auto">
                                                        <a href="https://shop.lonelyplanet.com/?utm_source=lonelyplanet&amp;utm_campaign=navigation&amp;utm_content=homepage" class="text-sm uppercase hover:text-blue" role="menuitem">view all books</a>
                                                    </div>
                                                </div>
                                                <div class="col-span-7 p-6">
                                                    <div class="grid grid-cols-1 gap-y-6 uppercase">
                                                        <div class="submenu-section" data-section-index="0" id="section-Books-0" style="display:block" role="region" aria-label="Destination Guides">
                                                            <div class="grid grid-cols-3 gap-4">
                                                                <div>
                                                                    <p class="mb-4 text-sm font-semibold">Europe</p>
                                                                    <a href="http://shop.lonelyplanet.com/collections/croatia?utm_source=lonelyplanet&amp;utm_campaign=navigation&amp;utm_content=destinationguides-croatia" class="block pb-2 mb-2 border-b hover:text-blue border-black-200 hover:border-blue group/arrow" role="menuitem">
                                                                        <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                            Croatia
                                                                            <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                                <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                            </svg>
                                                                        </span>
                                                                    </a>
                                                                    <a href="http://shop.lonelyplanet.com/collections/europe?utm_source=lonelyplanet&amp;utm_campaign=navigation&amp;utm_content=destinationguides-europe" class="block pb-2 mb-2 border-b hover:text-blue border-black-200 hover:border-blue group/arrow" role="menuitem">
                                                                        <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                            Europe
                                                                            <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                                <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                            </svg>
                                                                        </span>
                                                                    </a>
                                                                    <a href="http://shop.lonelyplanet.com/collections/greece?utm_source=lonelyplanet&amp;utm_campaign=navigation&amp;utm_content=destinationguides-greece" class="block pb-2 mb-2 border-b hover:text-blue border-black-200 hover:border-blue group/arrow" role="menuitem">
                                                                        <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                            Greece
                                                                            <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                                <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                            </svg>
                                                                        </span>
                                                                    </a>
                                                                    <a href="http://shop.lonelyplanet.com/collections/ireland?utm_source=lonelyplanet&amp;utm_campaign=navigation&amp;utm_content=destinationguides-ireland" class="block pb-2 mb-2 border-b hover:text-blue border-black-200 hover:border-blue group/arrow" role="menuitem">
                                                                        <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                            Ireland
                                                                            <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                                <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                            </svg>
                                                                        </span>
                                                                    </a>
                                                                    <a href="http://shop.lonelyplanet.com/collections/italy?utm_source=lonelyplanet&amp;utm_campaign=navigation&amp;utm_content=destinationguides-italy" class="block pb-2 mb-2 border-b hover:text-blue border-black-200 hover:border-blue group/arrow" role="menuitem">
                                                                        <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                            Italy
                                                                            <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                                <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                            </svg>
                                                                        </span>
                                                                    </a>
                                                                    <a href="http://shop.lonelyplanet.com/collections/portugal?utm_source=lonelyplanet&amp;utm_campaign=navigation&amp;utm_content=destinationguides-portugal" class="block pb-2 mb-2 border-b hover:text-blue border-black-200 hover:border-blue group/arrow" role="menuitem">
                                                                        <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                            Portugal
                                                                            <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                                <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                            </svg>
                                                                        </span>
                                                                    </a>
                                                                    <a href="http://shop.lonelyplanet.com/collections/spain?utm_source=lonelyplanet&amp;utm_campaign=navigation&amp;utm_content=destinationguides-spain" class="block pb-2 mb-2 border-b hover:text-blue border-black-200 hover:border-blue group/arrow" role="menuitem">
                                                                        <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                            Spain
                                                                            <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                                <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                            </svg>
                                                                        </span>
                                                                    </a>
                                                                </div>
                                                                <div>
                                                                    <p class="mb-4 text-sm font-semibold">The Americas</p>
                                                                    <a href="https://shop.lonelyplanet.com/collections/canada?utm_source=lonelyplanet&amp;utm_campaign=navigation&amp;utm_content=destinationguides-canada" class="block pb-2 mb-2 border-b hover:text-blue border-black-200 hover:border-blue group/arrow" role="menuitem">
                                                                        <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                            Canada
                                                                            <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                                <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                            </svg>
                                                                        </span>
                                                                    </a>
                                                                    <a href="https://shop.lonelyplanet.com/collections/all-central-america-guides?sort_by=title-ascending?utm_source=lonelyplanet&amp;utm_campaign=navigation&amp;utm_content=destinationguides-centralamerica" class="block pb-2 mb-2 border-b hover:text-blue border-black-200 hover:border-blue group/arrow" role="menuitem">
                                                                        <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                            Central America
                                                                            <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                                <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                            </svg>
                                                                        </span>
                                                                    </a>
                                                                    <a href="http://shop.lonelyplanet.com/collections/mexico?utm_source=lonelyplanet&amp;utm_campaign=navigation&amp;utm_content=destinationguides-mexico" class="block pb-2 mb-2 border-b hover:text-blue border-black-200 hover:border-blue group/arrow" role="menuitem">
                                                                        <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                            Mexico
                                                                            <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                                <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                            </svg>
                                                                        </span>
                                                                    </a>
                                                                    <a href="http://shop.lonelyplanet.com/collections/north-america?utm_source=lonelyplanet&amp;utm_campaign=navigation&amp;utm_content=destinationguides-northamerica" class="block pb-2 mb-2 border-b hover:text-blue border-black-200 hover:border-blue group/arrow" role="menuitem">
                                                                        <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                            North America
                                                                            <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                                <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                            </svg>
                                                                        </span>
                                                                    </a>
                                                                    <a href="https://shop.lonelyplanet.com/collections/peru?utm_source=lonelyplanet&amp;utm_campaign=navigation&amp;utm_content=destinationguides-peru" class="block pb-2 mb-2 border-b hover:text-blue border-black-200 hover:border-blue group/arrow" role="menuitem">
                                                                        <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                            Peru
                                                                            <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                                <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                            </svg>
                                                                        </span>
                                                                    </a>
                                                                    <a href="http://shop.lonelyplanet.com/collections/south-america?utm_source=lonelyplanet&amp;utm_campaign=navigation&amp;utm_content=destinationguides-southamerica" class="block pb-2 mb-2 border-b hover:text-blue border-black-200 hover:border-blue group/arrow" role="menuitem">
                                                                        <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                            South America
                                                                            <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                                <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                            </svg>
                                                                        </span>
                                                                    </a>
                                                                    <a href="http://shop.lonelyplanet.com/collections/usa?utm_source=lonelyplanet&amp;utm_campaign=navigation&amp;utm_content=destinationguides-us" class="block pb-2 mb-2 border-b hover:text-blue border-black-200 hover:border-blue group/arrow" role="menuitem">
                                                                        <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                            United States
                                                                            <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                                <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                            </svg>
                                                                        </span>
                                                                    </a>
                                                                </div>
                                                                <div>
                                                                    <p class="mb-4 text-sm font-semibold">More</p>
                                                                    <a href="http://shop.lonelyplanet.com/collections/africa?utm_source=lonelyplanet&amp;utm_campaign=navigation&amp;utm_content=destinationguides-africa" class="block pb-2 mb-2 border-b hover:text-blue border-black-200 hover:border-blue group/arrow" role="menuitem">
                                                                        <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                            Africa
                                                                            <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                                <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                            </svg>
                                                                        </span>
                                                                    </a>
                                                                    <a href="http://shop.lonelyplanet.com/collections/asia?utm_source=lonelyplanet&amp;utm_campaign=navigation&amp;utm_content=destinationguides-asia" class="block pb-2 mb-2 border-b hover:text-blue border-black-200 hover:border-blue group/arrow" role="menuitem">
                                                                        <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                            Asia
                                                                            <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                                <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                            </svg>
                                                                        </span>
                                                                    </a>
                                                                    <a href="http://shop.lonelyplanet.com/collections/australia?utm_source=lonelyplanet&amp;utm_campaign=navigation&amp;utm_content=destinationguides-australia" class="block pb-2 mb-2 border-b hover:text-blue border-black-200 hover:border-blue group/arrow" role="menuitem">
                                                                        <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                            Australia
                                                                            <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                                <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                            </svg>
                                                                        </span>
                                                                    </a>
                                                                    <a href="http://shop.lonelyplanet.com/collections/india?utm_source=lonelyplanet&amp;utm_campaign=navigation&amp;utm_content=destinationguides-india" class="block pb-2 mb-2 border-b hover:text-blue border-black-200 hover:border-blue group/arrow" role="menuitem">
                                                                        <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                            India
                                                                            <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                                <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                            </svg>
                                                                        </span>
                                                                    </a>
                                                                    <a href="http://shop.lonelyplanet.com/collections/japan?utm_source=lonelyplanet&amp;utm_campaign=navigation&amp;utm_content=destinationguides-japan" class="block pb-2 mb-2 border-b hover:text-blue border-black-200 hover:border-blue group/arrow" role="menuitem">
                                                                        <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                            Japan
                                                                            <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                                <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                            </svg>
                                                                        </span>
                                                                    </a>
                                                                    <a href="http://shop.lonelyplanet.com/collections/thailand?utm_source=lonelyplanet&amp;utm_campaign=navigation&amp;utm_content=destinationguides-thailand" class="block pb-2 mb-2 border-b hover:text-blue border-black-200 hover:border-blue group/arrow" role="menuitem">
                                                                        <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                            Thailand
                                                                            <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                                <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                            </svg>
                                                                        </span>
                                                                    </a>
                                                                    <a href="http://shop.lonelyplanet.com/collections/vietnam?utm_source=lonelyplanet&amp;utm_campaign=navigation&amp;utm_content=destinationguides-vietnam" class="block pb-2 mb-2 border-b hover:text-blue border-black-200 hover:border-blue group/arrow" role="menuitem">
                                                                        <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                            Vietnam
                                                                            <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                                <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                            </svg>
                                                                        </span>
                                                                    </a>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="submenu-section" data-section-index="1" id="section-Books-1" style="display:none" role="region" aria-label="Trending Guides">
                                                            <div class="grid grid-cols-3 gap-4">
                                                                <a href="https://shop.lonelyplanet.com/collections/bali?utm_source=lonelyplanet&amp;utm_campaign=navigation&amp;utm_content=trending-bali" class="block pb-2 mb-2 border-b border-black-200 hover:text-blue hover:border-blue group/arrow" role="menuitem">
                                                                    <img alt="Bali" loading="lazy" width="160" height="160" decoding="async" data-nimg="1" class="max-w-full object-cover w-full h-40 mb-2" style="color:transparent" sizes="(max-width: 768px) 100vw, 160px" srcSet="https://lp-cms-production.imgix.net/2025-04/shutterstock2574309095.jpg?w=640&amp;auto=format&amp;q=75 640w, https://lp-cms-production.imgix.net/2025-04/shutterstock2574309095.jpg?w=750&amp;auto=format&amp;q=75 750w, https://lp-cms-production.imgix.net/2025-04/shutterstock2574309095.jpg?w=828&amp;auto=format&amp;q=75 828w, https://lp-cms-production.imgix.net/2025-04/shutterstock2574309095.jpg?w=1024&amp;auto=format&amp;q=75 1024w, https://lp-cms-production.imgix.net/2025-04/shutterstock2574309095.jpg?w=1080&amp;auto=format&amp;q=75 1080w, https://lp-cms-production.imgix.net/2025-04/shutterstock2574309095.jpg?w=1200&amp;auto=format&amp;q=75 1200w, https://lp-cms-production.imgix.net/2025-04/shutterstock2574309095.jpg?w=1280&amp;auto=format&amp;q=75 1280w, https://lp-cms-production.imgix.net/2025-04/shutterstock2574309095.jpg?w=1920&amp;auto=format&amp;q=75 1920w, https://lp-cms-production.imgix.net/2025-04/shutterstock2574309095.jpg?w=2048&amp;auto=format&amp;q=75 2048w, https://lp-cms-production.imgix.net/2025-04/shutterstock2574309095.jpg?w=3840&amp;auto=format&amp;q=75 3840w" src="https://lp-cms-production.imgix.net/2025-04/shutterstock2574309095.jpg?w=3840&amp;auto=format&amp;q=75"/>
                                                                    <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                        Bali
                                                                        <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                            <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                        </svg>
                                                                    </span>
                                                                </a>
                                                                <a href="https://shop.lonelyplanet.com/collections/indonesia?utm_source=lonelyplanet&amp;utm_campaign=navigation&amp;utm_content=trending-indonesia" class="block pb-2 mb-2 border-b border-black-200 hover:text-blue hover:border-blue group/arrow" role="menuitem">
                                                                    <img alt="Indonesia" loading="lazy" width="160" height="160" decoding="async" data-nimg="1" class="max-w-full object-cover w-full h-40 mb-2" style="color:transparent" sizes="(max-width: 768px) 100vw, 160px" srcSet="https://lp-cms-production.imgix.net/2025-04/Shutterstock1124482919.jpg?w=640&amp;auto=format&amp;q=75 640w, https://lp-cms-production.imgix.net/2025-04/Shutterstock1124482919.jpg?w=750&amp;auto=format&amp;q=75 750w, https://lp-cms-production.imgix.net/2025-04/Shutterstock1124482919.jpg?w=828&amp;auto=format&amp;q=75 828w, https://lp-cms-production.imgix.net/2025-04/Shutterstock1124482919.jpg?w=1024&amp;auto=format&amp;q=75 1024w, https://lp-cms-production.imgix.net/2025-04/Shutterstock1124482919.jpg?w=1080&amp;auto=format&amp;q=75 1080w, https://lp-cms-production.imgix.net/2025-04/Shutterstock1124482919.jpg?w=1200&amp;auto=format&amp;q=75 1200w, https://lp-cms-production.imgix.net/2025-04/Shutterstock1124482919.jpg?w=1280&amp;auto=format&amp;q=75 1280w, https://lp-cms-production.imgix.net/2025-04/Shutterstock1124482919.jpg?w=1920&amp;auto=format&amp;q=75 1920w, https://lp-cms-production.imgix.net/2025-04/Shutterstock1124482919.jpg?w=2048&amp;auto=format&amp;q=75 2048w, https://lp-cms-production.imgix.net/2025-04/Shutterstock1124482919.jpg?w=3840&amp;auto=format&amp;q=75 3840w" src="https://lp-cms-production.imgix.net/2025-04/Shutterstock1124482919.jpg?w=3840&amp;auto=format&amp;q=75"/>
                                                                    <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                        Indonesia
                                                                        <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                            <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                        </svg>
                                                                    </span>
                                                                </a>
                                                                <a href="https://shop.lonelyplanet.com/collections/south-korea?utm_source=lonelyplanet&amp;utm_campaign=navigation&amp;utm_content=trending-korea" class="block pb-2 mb-2 border-b border-black-200 hover:text-blue hover:border-blue group/arrow" role="menuitem">
                                                                    <img alt="Korea" loading="lazy" width="160" height="160" decoding="async" data-nimg="1" class="max-w-full object-cover w-full h-40 mb-2" style="color:transparent" sizes="(max-width: 768px) 100vw, 160px" srcSet="https://lp-cms-production.imgix.net/2025-04/shutterstock1874286289.jpg?w=640&amp;auto=format&amp;q=75 640w, https://lp-cms-production.imgix.net/2025-04/shutterstock1874286289.jpg?w=750&amp;auto=format&amp;q=75 750w, https://lp-cms-production.imgix.net/2025-04/shutterstock1874286289.jpg?w=828&amp;auto=format&amp;q=75 828w, https://lp-cms-production.imgix.net/2025-04/shutterstock1874286289.jpg?w=1024&amp;auto=format&amp;q=75 1024w, https://lp-cms-production.imgix.net/2025-04/shutterstock1874286289.jpg?w=1080&amp;auto=format&amp;q=75 1080w, https://lp-cms-production.imgix.net/2025-04/shutterstock1874286289.jpg?w=1200&amp;auto=format&amp;q=75 1200w, https://lp-cms-production.imgix.net/2025-04/shutterstock1874286289.jpg?w=1280&amp;auto=format&amp;q=75 1280w, https://lp-cms-production.imgix.net/2025-04/shutterstock1874286289.jpg?w=1920&amp;auto=format&amp;q=75 1920w, https://lp-cms-production.imgix.net/2025-04/shutterstock1874286289.jpg?w=2048&amp;auto=format&amp;q=75 2048w, https://lp-cms-production.imgix.net/2025-04/shutterstock1874286289.jpg?w=3840&amp;auto=format&amp;q=75 3840w" src="https://lp-cms-production.imgix.net/2025-04/shutterstock1874286289.jpg?w=3840&amp;auto=format&amp;q=75"/>
                                                                    <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                        Korea
                                                                        <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                            <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                        </svg>
                                                                    </span>
                                                                </a>
                                                                <a href="https://shop.lonelyplanet.com/collections/germany?utm_source=lonelyplanet&amp;utm_campaign=navigation&amp;utm_content=trending-germany" class="block pb-2 mb-2 border-b border-black-200 hover:text-blue hover:border-blue group/arrow" role="menuitem">
                                                                    <img alt="Germany" loading="lazy" width="160" height="160" decoding="async" data-nimg="1" class="max-w-full object-cover w-full h-40 mb-2" style="color:transparent" sizes="(max-width: 768px) 100vw, 160px" srcSet="https://lp-cms-production.imgix.net/2025-04/Shutterstock1502282249.jpg?w=640&amp;auto=format&amp;q=75 640w, https://lp-cms-production.imgix.net/2025-04/Shutterstock1502282249.jpg?w=750&amp;auto=format&amp;q=75 750w, https://lp-cms-production.imgix.net/2025-04/Shutterstock1502282249.jpg?w=828&amp;auto=format&amp;q=75 828w, https://lp-cms-production.imgix.net/2025-04/Shutterstock1502282249.jpg?w=1024&amp;auto=format&amp;q=75 1024w, https://lp-cms-production.imgix.net/2025-04/Shutterstock1502282249.jpg?w=1080&amp;auto=format&amp;q=75 1080w, https://lp-cms-production.imgix.net/2025-04/Shutterstock1502282249.jpg?w=1200&amp;auto=format&amp;q=75 1200w, https://lp-cms-production.imgix.net/2025-04/Shutterstock1502282249.jpg?w=1280&amp;auto=format&amp;q=75 1280w, https://lp-cms-production.imgix.net/2025-04/Shutterstock1502282249.jpg?w=1920&amp;auto=format&amp;q=75 1920w, https://lp-cms-production.imgix.net/2025-04/Shutterstock1502282249.jpg?w=2048&amp;auto=format&amp;q=75 2048w, https://lp-cms-production.imgix.net/2025-04/Shutterstock1502282249.jpg?w=3840&amp;auto=format&amp;q=75 3840w" src="https://lp-cms-production.imgix.net/2025-04/Shutterstock1502282249.jpg?w=3840&amp;auto=format&amp;q=75"/>
                                                                    <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                        Germany
                                                                        <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                            <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                        </svg>
                                                                    </span>
                                                                </a>
                                                                <a href="https://shop.lonelyplanet.com/products/sicily?utm_source=lonelyplanet&amp;utm_campaign=navigation&amp;utm_content=trending-sicily" class="block pb-2 mb-2 border-b border-black-200 hover:text-blue hover:border-blue group/arrow" role="menuitem">
                                                                    <img alt="Sicily" loading="lazy" width="160" height="160" decoding="async" data-nimg="1" class="max-w-full object-cover w-full h-40 mb-2" style="color:transparent" sizes="(max-width: 768px) 100vw, 160px" srcSet="https://lp-cms-production.imgix.net/2024-02/Italy-Sicily-Favignana-Maremagnum-GettyImages-595333295-RF.jpg?w=640&amp;auto=format&amp;q=75 640w, https://lp-cms-production.imgix.net/2024-02/Italy-Sicily-Favignana-Maremagnum-GettyImages-595333295-RF.jpg?w=750&amp;auto=format&amp;q=75 750w, https://lp-cms-production.imgix.net/2024-02/Italy-Sicily-Favignana-Maremagnum-GettyImages-595333295-RF.jpg?w=828&amp;auto=format&amp;q=75 828w, https://lp-cms-production.imgix.net/2024-02/Italy-Sicily-Favignana-Maremagnum-GettyImages-595333295-RF.jpg?w=1024&amp;auto=format&amp;q=75 1024w, https://lp-cms-production.imgix.net/2024-02/Italy-Sicily-Favignana-Maremagnum-GettyImages-595333295-RF.jpg?w=1080&amp;auto=format&amp;q=75 1080w, https://lp-cms-production.imgix.net/2024-02/Italy-Sicily-Favignana-Maremagnum-GettyImages-595333295-RF.jpg?w=1200&amp;auto=format&amp;q=75 1200w, https://lp-cms-production.imgix.net/2024-02/Italy-Sicily-Favignana-Maremagnum-GettyImages-595333295-RF.jpg?w=1280&amp;auto=format&amp;q=75 1280w, https://lp-cms-production.imgix.net/2024-02/Italy-Sicily-Favignana-Maremagnum-GettyImages-595333295-RF.jpg?w=1920&amp;auto=format&amp;q=75 1920w, https://lp-cms-production.imgix.net/2024-02/Italy-Sicily-Favignana-Maremagnum-GettyImages-595333295-RF.jpg?w=2048&amp;auto=format&amp;q=75 2048w, https://lp-cms-production.imgix.net/2024-02/Italy-Sicily-Favignana-Maremagnum-GettyImages-595333295-RF.jpg?w=3840&amp;auto=format&amp;q=75 3840w" src="https://lp-cms-production.imgix.net/2024-02/Italy-Sicily-Favignana-Maremagnum-GettyImages-595333295-RF.jpg?w=3840&amp;auto=format&amp;q=75"/>
                                                                    <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                        Sicily
                                                                        <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                            <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                        </svg>
                                                                    </span>
                                                                </a>
                                                                <a href="https://shop.lonelyplanet.com/products/western-balkans?utm_source=lonelyplanet&amp;utm_campaign=navigation&amp;utm_content=trending-westernbalkans" class="block pb-2 mb-2 border-b border-black-200 hover:text-blue hover:border-blue group/arrow" role="menuitem">
                                                                    <img alt="Western Balkans" loading="lazy" width="160" height="160" decoding="async" data-nimg="1" class="max-w-full object-cover w-full h-40 mb-2" style="color:transparent" sizes="(max-width: 768px) 100vw, 160px" srcSet="https://lp-cms-production.imgix.net/2024-01/LPT1018028.jpg?w=640&amp;auto=format&amp;q=75 640w, https://lp-cms-production.imgix.net/2024-01/LPT1018028.jpg?w=750&amp;auto=format&amp;q=75 750w, https://lp-cms-production.imgix.net/2024-01/LPT1018028.jpg?w=828&amp;auto=format&amp;q=75 828w, https://lp-cms-production.imgix.net/2024-01/LPT1018028.jpg?w=1024&amp;auto=format&amp;q=75 1024w, https://lp-cms-production.imgix.net/2024-01/LPT1018028.jpg?w=1080&amp;auto=format&amp;q=75 1080w, https://lp-cms-production.imgix.net/2024-01/LPT1018028.jpg?w=1200&amp;auto=format&amp;q=75 1200w, https://lp-cms-production.imgix.net/2024-01/LPT1018028.jpg?w=1280&amp;auto=format&amp;q=75 1280w, https://lp-cms-production.imgix.net/2024-01/LPT1018028.jpg?w=1920&amp;auto=format&amp;q=75 1920w, https://lp-cms-production.imgix.net/2024-01/LPT1018028.jpg?w=2048&amp;auto=format&amp;q=75 2048w, https://lp-cms-production.imgix.net/2024-01/LPT1018028.jpg?w=3840&amp;auto=format&amp;q=75 3840w" src="https://lp-cms-production.imgix.net/2024-01/LPT1018028.jpg?w=3840&amp;auto=format&amp;q=75"/>
                                                                    <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                        Western Balkans
                                                                        <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                            <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                        </svg>
                                                                    </span>
                                                                </a>
                                                            </div>
                                                        </div>
                                                        <div class="submenu-section" data-section-index="2" id="section-Books-2" style="display:none" role="region" aria-label="Gifts &amp; New Releases">
                                                            <div class="grid grid-cols-2 gap-4">
                                                                <div>
                                                                    <p class="mb-4 text-sm font-semibold">Gifts &amp;Inspiration</p>
                                                                    <a href="https://shop.lonelyplanet.com/products/lonely-planets-best-in-travel-2025?utm_source=lonelyplanet&amp;utm_campaign=navigation&amp;utm_content=gifts-bit" class="block pb-2 mb-2 border-b hover:text-blue border-black-200 hover:border-blue group/arrow" role="menuitem">
                                                                        <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                            Best in Travel 2025
                                                                            <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                                <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                            </svg>
                                                                        </span>
                                                                    </a>
                                                                    <a href="https://shop.lonelyplanet.com/collections/outdoor-travel-and-adventures?utm_source=lonelyplanet&amp;utm_campaign=navigation&amp;utm_content=gifts-outdoor" class="block pb-2 mb-2 border-b hover:text-blue border-black-200 hover:border-blue group/arrow" role="menuitem">
                                                                        <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                            Outdoor Adventure
                                                                            <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                                <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                            </svg>
                                                                        </span>
                                                                    </a>
                                                                    <a href="https://shop.lonelyplanet.com/collections/food-drink?utm_source=lonelyplanet&amp;utm_campaign=navigation&amp;utm_content=gifts-food" class="block pb-2 mb-2 border-b hover:text-blue border-black-200 hover:border-blue group/arrow" role="menuitem">
                                                                        <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                            Food
                                                                            <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                                <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                            </svg>
                                                                        </span>
                                                                    </a>
                                                                    <a href="https://shop.lonelyplanet.com/collections/kids?utm_source=lonelyplanet&amp;utm_campaign=navigation&amp;utm_content=gifts-kids" class="block pb-2 mb-2 border-b hover:text-blue border-black-200 hover:border-blue group/arrow" role="menuitem">
                                                                        <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                            Lonely Planet Kids
                                                                            <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                                <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                            </svg>
                                                                        </span>
                                                                    </a>
                                                                    <a href="https://shop.lonelyplanet.com/collections/lonely-planet-merchandise?utm_source=lonelyplanet&amp;utm_campaign=navigation&amp;utm_content=gifts-merchandise" class="block pb-2 mb-2 border-b hover:text-blue border-black-200 hover:border-blue group/arrow" role="menuitem">
                                                                        <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                            Merchandise
                                                                            <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                                <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                            </svg>
                                                                        </span>
                                                                    </a>
                                                                    <a href="https://shop.lonelyplanet.com/collections/gifts-inspiration?utm_source=lonelyplanet&amp;utm_campaign=navigation&amp;utm_content=gifts-inspiration" class="block pb-2 mb-2 border-b hover:text-blue border-black-200 hover:border-blue group/arrow" role="menuitem">
                                                                        <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                            View All Gifts &amp;Inspiration
                                                                            <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                                <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                            </svg>
                                                                        </span>
                                                                    </a>
                                                                </div>
                                                                <div>
                                                                    <p class="mb-4 text-sm font-semibold">New Releases</p>
                                                                    <a href="https://shop.lonelyplanet.com/products/portugal?utm_source=lonelyplanet&amp;utm_campaign=navigation&amp;utm_content=gifts-portugal" class="block pb-2 mb-2 border-b hover:text-blue border-black-200 hover:border-blue group/arrow" role="menuitem">
                                                                        <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                            Portugal
                                                                            <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                                <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                            </svg>
                                                                        </span>
                                                                    </a>
                                                                    <a href="https://shop.lonelyplanet.com/products/rome?utm_source=lonelyplanet&amp;utm_campaign=navigation&amp;utm_content=gifts-rome" class="block pb-2 mb-2 border-b hover:text-blue border-black-200 hover:border-blue group/arrow" role="menuitem">
                                                                        <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                            Rome
                                                                            <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                                <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                            </svg>
                                                                        </span>
                                                                    </a>
                                                                    <a href="https://shop.lonelyplanet.com/products/spain?utm_source=lonelyplanet&amp;utm_campaign=navigation&amp;utm_content=gifts-spain" class="block pb-2 mb-2 border-b hover:text-blue border-black-200 hover:border-blue group/arrow" role="menuitem">
                                                                        <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                            Spain
                                                                            <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                                <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                            </svg>
                                                                        </span>
                                                                    </a>
                                                                    <a href="https://shop.lonelyplanet.com/collections/new-releases?utm_source=lonelyplanet&amp;utm_campaign=navigation&amp;utm_content=gifts-newreleases" class="block pb-2 mb-2 border-b hover:text-blue border-black-200 hover:border-blue group/arrow" role="menuitem">
                                                                        <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                            View All New Releases
                                                                            <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                                <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                            </svg>
                                                                        </span>
                                                                    </a>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="submenu-section" data-section-index="3" id="section-Books-3" style="display:none" role="region" aria-label="Guide Types">
                                                            <div class="grid grid-cols-3 gap-4">
                                                                <a href="https://shop.lonelyplanet.com/collections/classic-guides?utm_source=lonelyplanet&amp;utm_campaign=navigation&amp;utm_content=guidetypes-classic" class="block pb-2 mb-2 border-b border-black-200 hover:text-blue hover:border-blue group/arrow" role="menuitem">
                                                                    <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                        Classic Guides
                                                                        <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                            <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                        </svg>
                                                                    </span>
                                                                    <p class="my-4 text-sm text-black normal-case">For travelers seeking the most comprehensive insights. These guides provide in-depth insights for your trips, helping you explore destinations deeply for unforgettable experiences, whether popular or off the beaten path.</p>
                                                                </a>
                                                                <a href="https://shop.lonelyplanet.com/collections/experience-guides?utm_source=lonelyplanet&amp;utm_campaign=navigation&amp;utm_content=guidetypes-experience" class="block pb-2 mb-2 border-b border-black-200 hover:text-blue hover:border-blue group/arrow" role="menuitem">
                                                                    <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                        Experience Guides
                                                                        <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                            <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                        </svg>
                                                                    </span>
                                                                    <p class="my-4 text-sm text-black normal-case">For travelers looking to design a trip that feels unique. Experience guides offer travelers innovative ways to discover iconic destinations, featuring unique adventures and trip-building tools for personalized journeys.</p>
                                                                </a>
                                                                <a href="https://shop.lonelyplanet.com/collections/language-guides-all?utm_source=lonelyplanet&amp;utm_campaign=navigation&amp;utm_content=guidetypes-language" class="block pb-2 mb-2 border-b border-black-200 hover:text-blue hover:border-blue group/arrow" role="menuitem">
                                                                    <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                        Language Guides
                                                                        <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                            <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                        </svg>
                                                                    </span>
                                                                    <p class="my-4 text-sm text-black normal-case">For travelers looking to immerse themselves in local culture and language essentials. Language Guides help to let no barriers - language or culture - get in your way.</p>
                                                                </a>
                                                                <a href="https://shop.lonelyplanet.com/collections/epic-guides?utm_source=lonelyplanet&amp;utm_campaign=navigation&amp;utm_content=guidetypes-epic" class="block pb-2 mb-2 border-b border-black-200 hover:text-blue hover:border-blue group/arrow" role="menuitem">
                                                                    <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                        Epic Guides
                                                                        <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                            <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                        </svg>
                                                                    </span>
                                                                    <p class="my-4 text-sm text-black normal-case">From hikes to bike rides and surf breaks, discover the most epic adventures across the world to inspire your next trip. Epic Guides provide inspiration and first-person stories for a lifetime of unforgettable travel experiences.</p>
                                                                </a>
                                                                <a href="https://shop.lonelyplanet.com/collections/pocket?utm_source=lonelyplanet&amp;utm_campaign=navigation&amp;utm_content=guidetypes-pocket" class="block pb-2 mb-2 border-b border-black-200 hover:text-blue hover:border-blue group/arrow" role="menuitem">
                                                                    <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                        Pocket Guides
                                                                        <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                            <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                        </svg>
                                                                    </span>
                                                                    <p class="my-4 text-sm text-black normal-case">For travelers on a short trip that want to make the most of their time exploring. Pocket Guides are compact guides for short trips that offer the best local experiences.</p>
                                                                </a>
                                                                <a href="https://shop.lonelyplanet.com/collections/maps?utm_source=lonelyplanet&amp;utm_campaign=navigation&amp;utm_content=guidetypes-maps" class="block pb-2 mb-2 border-b border-black-200 hover:text-blue hover:border-blue group/arrow" role="menuitem">
                                                                    <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                        Maps
                                                                        <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                            <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                        </svg>
                                                                    </span>
                                                                    <p class="my-4 text-sm text-black normal-case">Durable and waterproof, with a handy slipcase and an easy-fold format, Lonely Planet &#x27;s country maps are designed to let you explore with ease.</p>
                                                                </a>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-span-3 p-6 bg-white border-l border-black-200">
                                                    <div>
                                                        <img alt="" loading="lazy" width="350" height="200" decoding="async" data-nimg="1" class="max-w-full object-cover w-full h-48 mb-4" style="color:transparent" sizes="(max-width: 768px) 100vw, 350px" srcSet="https://lonelyplanetstatic.imgix.net/marketing/2025/global_nav/gifts_new_releases_italy_book_4X.jpg?w=640&amp;auto=format&amp;q=75 640w, https://lonelyplanetstatic.imgix.net/marketing/2025/global_nav/gifts_new_releases_italy_book_4X.jpg?w=750&amp;auto=format&amp;q=75 750w, https://lonelyplanetstatic.imgix.net/marketing/2025/global_nav/gifts_new_releases_italy_book_4X.jpg?w=828&amp;auto=format&amp;q=75 828w, https://lonelyplanetstatic.imgix.net/marketing/2025/global_nav/gifts_new_releases_italy_book_4X.jpg?w=1024&amp;auto=format&amp;q=75 1024w, https://lonelyplanetstatic.imgix.net/marketing/2025/global_nav/gifts_new_releases_italy_book_4X.jpg?w=1080&amp;auto=format&amp;q=75 1080w, https://lonelyplanetstatic.imgix.net/marketing/2025/global_nav/gifts_new_releases_italy_book_4X.jpg?w=1200&amp;auto=format&amp;q=75 1200w, https://lonelyplanetstatic.imgix.net/marketing/2025/global_nav/gifts_new_releases_italy_book_4X.jpg?w=1280&amp;auto=format&amp;q=75 1280w, https://lonelyplanetstatic.imgix.net/marketing/2025/global_nav/gifts_new_releases_italy_book_4X.jpg?w=1920&amp;auto=format&amp;q=75 1920w, https://lonelyplanetstatic.imgix.net/marketing/2025/global_nav/gifts_new_releases_italy_book_4X.jpg?w=2048&amp;auto=format&amp;q=75 2048w, https://lonelyplanetstatic.imgix.net/marketing/2025/global_nav/gifts_new_releases_italy_book_4X.jpg?w=3840&amp;auto=format&amp;q=75 3840w" src="https://lonelyplanetstatic.imgix.net/marketing/2025/global_nav/gifts_new_releases_italy_book_4X.jpg?w=3840&amp;auto=format&amp;q=75"/>
                                                        <h3 class="mb-2 text-xl">Italy &#x27;s Newest Addition</h3>
                                                        <p class="mb-4 text-sm">Explore Italy &#x27;s iconic landmarks and hidden gems with expert insights in Lonely Planet &#x27;s latest Italy guide.</p>
                                                        <a href="https://shop.lonelyplanet.com/products/italy?utm_source=lonelyplanet&amp;utm_campaign=navigation&amp;utm_content=shopnewrelease" class="inline-block w-full px-4 py-2 text-sm text-center text-white uppercase transition-colors bg-black rounded hover:bg-blue" role="menuitem">shop the newest release</a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="relative flex group after:content-[&#x27;&#x27;] after:absolute after:top-0 after:left-0 after:right-0 after:h-full after:bg-transparent after:pointer-events-none after:z-[1] hover:after:h-[100vh] hover:after:pointer-events-auto" role="none">
                                    <button type="button" class="inline-flex items-center h-full px-8 py-2 group focus:outline-none hover:text-blue hover:underline" aria-expanded="false" aria-controls="submenu-Trips" aria-haspopup="true">
                                        <a href="https://www.elsewhere.io/?utm_source=lonelyplanet&amp;utm_medium=globalnav&amp;utm_campaign=header" class="uppercase hover:text-blue focus:outline-none group-hover:text-blue group-hover:underline relative z-[2]" tabindex="-1">Trips</a>
                                    </button>
                                    <div id="submenu-Trips" class="fixed z-50 invisible opacity-0 group-hover:visible group-hover:opacity-100 bg-white shadow-lg transition-all duration-200 left-1/2 transform -translate-x-1/2 max-w-[1392px] w-[calc(100%-32px)] top-[8rem]" role="menu" aria-label="Trips submenu">
                                        <div class="w-full">
                                            <div class="grid grid-cols-12 min-h-[400px]">
                                                <div class="flex flex-col h-full col-span-2 bg-black-100">
                                                    <nav class="px-6 pt-8" role="navigation" aria-label="Trips submenu navigation">
                                                        <div class="space-y-1">
                                                            <button type="button" class="w-full text-left relative inline-block py-2 px-4 my-1 transition-transform duration-200 bg-blue-900 rotate-[-1.5deg] text-white" data-section-index="0" data-menu-title="Trips" role="menuitem" aria-controls="section-Trips-0">
                                                                <div class="text-sm uppercase text-white">Popular Trips</div>
                                                            </button>
                                                        </div>
                                                    </nav>
                                                    <div class="px-6 pt-6 pb-8 mt-auto">
                                                        <a href="https://www.elsewhere.io/destinations?utm_source=lonelyplanet&amp;utm_medium=globalnav&amp;utm_campaign=view_all" class="text-sm uppercase hover:text-blue" role="menuitem">view all trips</a>
                                                    </div>
                                                </div>
                                                <div class="col-span-7 p-6">
                                                    <div class="grid grid-cols-1 gap-y-6 uppercase">
                                                        <div class="submenu-section" data-section-index="0" id="section-Trips-0" style="display:block" role="region" aria-label="Popular Trips">
                                                            <div class="grid grid-cols-3 gap-4">
                                                                <a href="https://www.elsewhere.io/costa-rica?utm_source=lonelyplanet&amp;utm_medium=globalnav&amp;utm_campaign=costa-rica" class="block pb-2 mb-2 border-b border-black-200 hover:text-blue hover:border-blue group/arrow" role="menuitem">
                                                                    <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                        Costa Rica
                                                                        <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                            <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                        </svg>
                                                                    </span>
                                                                </a>
                                                                <a href="https://www.elsewhere.io/france?utm_source=lonelyplanet&amp;utm_medium=globalnav&amp;utm_campaign=france" class="block pb-2 mb-2 border-b border-black-200 hover:text-blue hover:border-blue group/arrow" role="menuitem">
                                                                    <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                        France
                                                                        <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                            <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                        </svg>
                                                                    </span>
                                                                </a>
                                                                <a href="https://www.elsewhere.io/iceland?utm_source=lonelyplanet&amp;utm_medium=globalnav&amp;utm_campaign=iceland" class="block pb-2 mb-2 border-b border-black-200 hover:text-blue hover:border-blue group/arrow" role="menuitem">
                                                                    <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                        Iceland
                                                                        <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                            <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                        </svg>
                                                                    </span>
                                                                </a>
                                                                <a href="https://www.elsewhere.io/italy?utm_source=lonelyplanet&amp;utm_medium=globalnav&amp;utm_campaign=italy" class="block pb-2 mb-2 border-b border-black-200 hover:text-blue hover:border-blue group/arrow" role="menuitem">
                                                                    <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                        Italy
                                                                        <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                            <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                        </svg>
                                                                    </span>
                                                                </a>
                                                                <a href="https://www.elsewhere.io/japan?utm_source=lonelyplanet&amp;utm_medium=globalnav&amp;utm_campaign=japan" class="block pb-2 mb-2 border-b border-black-200 hover:text-blue hover:border-blue group/arrow" role="menuitem">
                                                                    <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                        Japan
                                                                        <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                            <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                        </svg>
                                                                    </span>
                                                                </a>
                                                                <a href="https://www.elsewhere.io/morocco?utm_source=lonelyplanet&amp;utm_medium=globalnav&amp;utm_campaign=morocco" class="block pb-2 mb-2 border-b border-black-200 hover:text-blue hover:border-blue group/arrow" role="menuitem">
                                                                    <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                        Morocco
                                                                        <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                            <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                        </svg>
                                                                    </span>
                                                                </a>
                                                                <a href="https://www.elsewhere.io/portugal?utm_source=lonelyplanet&amp;utm_medium=globalnav&amp;utm_campaign=portugal" class="block pb-2 mb-2 border-b border-black-200 hover:text-blue hover:border-blue group/arrow" role="menuitem">
                                                                    <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                        Portugal
                                                                        <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                            <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                        </svg>
                                                                    </span>
                                                                </a>
                                                                <a href="https://www.elsewhere.io/spain?utm_source=lonelyplanet&amp;utm_medium=globalnav&amp;utm_campaign=spain" class="block pb-2 mb-2 border-b border-black-200 hover:text-blue hover:border-blue group/arrow" role="menuitem">
                                                                    <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                        Spain
                                                                        <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                            <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                        </svg>
                                                                    </span>
                                                                </a>
                                                                <a href="https://www.elsewhere.io/turkey?utm_source=lonelyplanet&amp;utm_medium=globalnav&amp;utm_campaign=turkey" class="block pb-2 mb-2 border-b border-black-200 hover:text-blue hover:border-blue group/arrow" role="menuitem">
                                                                    <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                        Turkey
                                                                        <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                            <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                        </svg>
                                                                    </span>
                                                                </a>
                                                                <a href="https://www.elsewhere.io/vietnam?utm_source=lonelyplanet&amp;utm_medium=globalnav&amp;utm_campaign=vietnam" class="block pb-2 mb-2 border-b border-black-200 hover:text-blue hover:border-blue group/arrow" role="menuitem">
                                                                    <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                        Vietnam
                                                                        <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                            <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                        </svg>
                                                                    </span>
                                                                </a>
                                                                <a href="https://www.getyourguide.com/?partner_id=MH9NGR8&amp;cmp=homepage_navigation&amp;deeplink_id=cc65224c-8be2-598a-9a86-0b28ce4c3c48&amp;page_id=b9971ae8-0937-5d9f-97f4-58ffcff19023" class="block pb-2 mb-2 border-b border-black-200 hover:text-blue hover:border-blue group/arrow" role="menuitem">
                                                                    <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                        Book tours &amp;activities
                                                                        <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                            <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                        </svg>
                                                                    </span>
                                                                </a>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-span-3 p-6 bg-white border-l border-black-200">
                                                    <div>
                                                        <img alt="" loading="lazy" width="350" height="200" decoding="async" data-nimg="1" class="max-w-full object-cover w-full h-48 mb-4" style="color:transparent" sizes="(max-width: 768px) 100vw, 350px" srcSet="https://lonelyplanetstatic.imgix.net/marketing/2025/global_nav/nav_elsewhere.jpg?w=640&amp;auto=format&amp;q=75 640w, https://lonelyplanetstatic.imgix.net/marketing/2025/global_nav/nav_elsewhere.jpg?w=750&amp;auto=format&amp;q=75 750w, https://lonelyplanetstatic.imgix.net/marketing/2025/global_nav/nav_elsewhere.jpg?w=828&amp;auto=format&amp;q=75 828w, https://lonelyplanetstatic.imgix.net/marketing/2025/global_nav/nav_elsewhere.jpg?w=1024&amp;auto=format&amp;q=75 1024w, https://lonelyplanetstatic.imgix.net/marketing/2025/global_nav/nav_elsewhere.jpg?w=1080&amp;auto=format&amp;q=75 1080w, https://lonelyplanetstatic.imgix.net/marketing/2025/global_nav/nav_elsewhere.jpg?w=1200&amp;auto=format&amp;q=75 1200w, https://lonelyplanetstatic.imgix.net/marketing/2025/global_nav/nav_elsewhere.jpg?w=1280&amp;auto=format&amp;q=75 1280w, https://lonelyplanetstatic.imgix.net/marketing/2025/global_nav/nav_elsewhere.jpg?w=1920&amp;auto=format&amp;q=75 1920w, https://lonelyplanetstatic.imgix.net/marketing/2025/global_nav/nav_elsewhere.jpg?w=2048&amp;auto=format&amp;q=75 2048w, https://lonelyplanetstatic.imgix.net/marketing/2025/global_nav/nav_elsewhere.jpg?w=3840&amp;auto=format&amp;q=75 3840w" src="https://lonelyplanetstatic.imgix.net/marketing/2025/global_nav/nav_elsewhere.jpg?w=3840&amp;auto=format&amp;q=75"/>
                                                        <h3 class="mb-2 text-xl">Your next trip starts here</h3>
                                                        <p class="mb-4 text-sm">Let our local experts craft your dream trip at Elsewhere by Lonely Planet.</p>
                                                        <a href="https://www.elsewhere.io?utm_source=lonelyplanet&amp;utm_medium=globalnav&amp;utm_campaign=main-placement" class="inline-block w-full px-4 py-2 text-sm text-center text-white uppercase transition-colors bg-black rounded hover:bg-blue" role="menuitem">Start Planning</a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="relative flex group after:content-[&#x27;&#x27;] after:absolute after:top-0 after:left-0 after:right-0 after:h-full after:bg-transparent after:pointer-events-none after:z-[1] hover:after:h-[100vh] hover:after:pointer-events-auto" role="none">
                                    <button type="button" class="inline-flex items-center h-full px-8 py-2 group focus:outline-none hover:text-blue hover:underline" aria-expanded="false" aria-controls="submenu-Stories" aria-haspopup="true">
                                        <a href="https://www.lonelyplanet.com/articles" class="uppercase hover:text-blue focus:outline-none group-hover:text-blue group-hover:underline relative z-[2]" tabindex="-1">Stories</a>
                                    </button>
                                    <div id="submenu-Stories" class="fixed z-50 invisible opacity-0 group-hover:visible group-hover:opacity-100 bg-white shadow-lg transition-all duration-200 left-1/2 transform -translate-x-1/2 max-w-[1392px] w-[calc(100%-32px)] top-[8rem]" role="menu" aria-label="Stories submenu">
                                        <div class="w-full">
                                            <div class="grid grid-cols-12 min-h-[400px]">
                                                <div class="flex flex-col h-full col-span-2 bg-black-100">
                                                    <nav class="px-6 pt-8" role="navigation" aria-label="Stories submenu navigation">
                                                        <div class="space-y-1">
                                                            <button type="button" class="w-full text-left relative inline-block py-2 px-4 my-1 transition-transform duration-200 bg-blue-900 rotate-[-1.5deg] text-white" data-section-index="0" data-menu-title="Stories" role="menuitem" aria-controls="section-Stories-0">
                                                                <div class="text-sm uppercase text-white">Summer Guide</div>
                                                            </button>
                                                            <button type="button" class="w-full text-left relative inline-block py-2 px-4 my-1 transition-transform duration-200 " data-section-index="1" data-menu-title="Stories" role="menuitem" aria-controls="section-Stories-1">
                                                                <div class="text-sm uppercase hover:text-blue cursor-pointer">Best in Travel</div>
                                                            </button>
                                                            <button type="button" class="w-full text-left relative inline-block py-2 px-4 my-1 transition-transform duration-200 " data-section-index="2" data-menu-title="Stories" role="menuitem" aria-controls="section-Stories-2">
                                                                <div class="text-sm uppercase hover:text-blue cursor-pointer">Newsletter</div>
                                                            </button>
                                                            <button type="button" class="w-full text-left relative inline-block py-2 px-4 my-1 transition-transform duration-200 " data-section-index="3" data-menu-title="Stories" role="menuitem" aria-controls="section-Stories-3">
                                                                <div class="text-sm uppercase hover:text-blue cursor-pointer">By Topic</div>
                                                            </button>
                                                        </div>
                                                    </nav>
                                                    <div class="px-6 pt-6 pb-8 mt-auto">
                                                        <a href="https://www.lonelyplanet.com/articles" class="text-sm uppercase hover:text-blue" role="menuitem">view all stories</a>
                                                    </div>
                                                </div>
                                                <div class="col-span-10 p-6">
                                                    <div class="grid grid-cols-1 gap-y-6 ">
                                                        <div class="submenu-section" data-section-index="0" id="section-Stories-0" style="display:block" role="region" aria-label="Summer Guide">
                                                            <div class="col-span-full">
                                                                <a href="https://www.lonelyplanet.com/campaigns/ultimate-guide-to-summer" class="block group/arrow" role="menuitem">
                                                                    <div class="grid grid-cols-2 gap-4">
                                                                        <div>
                                                                            <img alt="The Ultimate Guide to Summer" loading="lazy" width="550" height="300" decoding="async" data-nimg="1" class="max-w-full object-cover w-full h-full" style="color:transparent" sizes="(max-width: 768px) 100vw, 550px" srcSet="https://lonelyplanetstatic.imgix.net/marketing/2025/global_nav/nav_summer_guide_2x.jpg?w=640&amp;auto=format&amp;q=75 640w, https://lonelyplanetstatic.imgix.net/marketing/2025/global_nav/nav_summer_guide_2x.jpg?w=750&amp;auto=format&amp;q=75 750w, https://lonelyplanetstatic.imgix.net/marketing/2025/global_nav/nav_summer_guide_2x.jpg?w=828&amp;auto=format&amp;q=75 828w, https://lonelyplanetstatic.imgix.net/marketing/2025/global_nav/nav_summer_guide_2x.jpg?w=1024&amp;auto=format&amp;q=75 1024w, https://lonelyplanetstatic.imgix.net/marketing/2025/global_nav/nav_summer_guide_2x.jpg?w=1080&amp;auto=format&amp;q=75 1080w, https://lonelyplanetstatic.imgix.net/marketing/2025/global_nav/nav_summer_guide_2x.jpg?w=1200&amp;auto=format&amp;q=75 1200w, https://lonelyplanetstatic.imgix.net/marketing/2025/global_nav/nav_summer_guide_2x.jpg?w=1280&amp;auto=format&amp;q=75 1280w, https://lonelyplanetstatic.imgix.net/marketing/2025/global_nav/nav_summer_guide_2x.jpg?w=1920&amp;auto=format&amp;q=75 1920w, https://lonelyplanetstatic.imgix.net/marketing/2025/global_nav/nav_summer_guide_2x.jpg?w=2048&amp;auto=format&amp;q=75 2048w, https://lonelyplanetstatic.imgix.net/marketing/2025/global_nav/nav_summer_guide_2x.jpg?w=3840&amp;auto=format&amp;q=75 3840w" src="https://lonelyplanetstatic.imgix.net/marketing/2025/global_nav/nav_summer_guide_2x.jpg?w=3840&amp;auto=format&amp;q=75"/>
                                                                        </div>
                                                                        <div class="flex flex-col justify-between gap-y-2">
                                                                            <div>
                                                                                <h3 class="mb-2 text-2xl font-semibold uppercase">The Ultimate Guide to Summer</h3>
                                                                                <p class="mb-4 text-base normal-case whitespace-pre-line">Whether your travels include the beaches of Europe or the USA &#x27;s sizzling cities and majestic parks, we &#x27;ll be adding all sorts of resources to inspire your next summer travel adventure.

Dive into our guides, stories, and more to find Lonely Planet &#x27;s favorite (and sometimes unexpected) summer destinations.</p>
                                                                            </div>
                                                                            <p class="flex items-center justify-between pb-2 mb-4 text-base uppercase border-b border-black-200 group-hover/arrow:border-blue group-hover/arrow:text-blue">
                                                                                Explore our Summer Guide
                                                                                <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                                    <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                                </svg>
                                                                            </p>
                                                                        </div>
                                                                    </div>
                                                                </a>
                                                            </div>
                                                        </div>
                                                        <div class="submenu-section" data-section-index="1" id="section-Stories-1" style="display:none" role="region" aria-label="Best in Travel">
                                                            <div class="col-span-full">
                                                                <a href="https://www.lonelyplanet.com/best-in-travel" class="block group/arrow" role="menuitem">
                                                                    <div class="grid grid-cols-2 gap-4">
                                                                        <div>
                                                                            <img alt="Best in Travel" loading="lazy" width="550" height="300" decoding="async" data-nimg="1" class="max-w-full object-cover w-full h-full" style="color:transparent" sizes="(max-width: 768px) 100vw, 550px" srcSet="https://lonelyplanetstatic.imgix.net/marketing/2025/global_nav/nav_bit.jpg?w=640&amp;auto=format&amp;q=75 640w, https://lonelyplanetstatic.imgix.net/marketing/2025/global_nav/nav_bit.jpg?w=750&amp;auto=format&amp;q=75 750w, https://lonelyplanetstatic.imgix.net/marketing/2025/global_nav/nav_bit.jpg?w=828&amp;auto=format&amp;q=75 828w, https://lonelyplanetstatic.imgix.net/marketing/2025/global_nav/nav_bit.jpg?w=1024&amp;auto=format&amp;q=75 1024w, https://lonelyplanetstatic.imgix.net/marketing/2025/global_nav/nav_bit.jpg?w=1080&amp;auto=format&amp;q=75 1080w, https://lonelyplanetstatic.imgix.net/marketing/2025/global_nav/nav_bit.jpg?w=1200&amp;auto=format&amp;q=75 1200w, https://lonelyplanetstatic.imgix.net/marketing/2025/global_nav/nav_bit.jpg?w=1280&amp;auto=format&amp;q=75 1280w, https://lonelyplanetstatic.imgix.net/marketing/2025/global_nav/nav_bit.jpg?w=1920&amp;auto=format&amp;q=75 1920w, https://lonelyplanetstatic.imgix.net/marketing/2025/global_nav/nav_bit.jpg?w=2048&amp;auto=format&amp;q=75 2048w, https://lonelyplanetstatic.imgix.net/marketing/2025/global_nav/nav_bit.jpg?w=3840&amp;auto=format&amp;q=75 3840w" src="https://lonelyplanetstatic.imgix.net/marketing/2025/global_nav/nav_bit.jpg?w=3840&amp;auto=format&amp;q=75"/>
                                                                        </div>
                                                                        <div class="flex flex-col justify-between gap-y-2">
                                                                            <div>
                                                                                <h3 class="mb-2 text-2xl font-semibold uppercase">Best in Travel</h3>
                                                                                <p class="mb-4 text-base normal-case whitespace-pre-line">Lonely Planet &#x27;s annual Best in Travel list ranks the year &#x27;s 30 hottest (and coolest) destinations, including popular favorites and under-the-radar spots chosen by our global team of expert contributors.</p>
                                                                            </div>
                                                                            <p class="flex items-center justify-between pb-2 mb-4 text-base uppercase border-b border-black-200 group-hover/arrow:border-blue group-hover/arrow:text-blue">
                                                                                Explore Best in Travel 2025
                                                                                <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                                    <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                                </svg>
                                                                            </p>
                                                                        </div>
                                                                    </div>
                                                                </a>
                                                            </div>
                                                        </div>
                                                        <div class="submenu-section" data-section-index="2" id="section-Stories-2" style="display:none" role="region" aria-label="Newsletter">
                                                            <div class="col-span-full">
                                                                <a href="https://www.lonelyplanet.com/newsletter" class="block group/arrow" role="menuitem">
                                                                    <div class="grid grid-cols-2 gap-4">
                                                                        <div>
                                                                            <img alt="Subscribe to Lonely Planet&#x27;s newsletter" loading="lazy" width="550" height="300" decoding="async" data-nimg="1" class="max-w-full object-cover w-full h-full" style="color:transparent" sizes="(max-width: 768px) 100vw, 550px" srcSet="https://lonelyplanetstatic.imgix.net/marketing/2025/global_nav/nav_newsletter.jpg?w=640&amp;auto=format&amp;q=75 640w, https://lonelyplanetstatic.imgix.net/marketing/2025/global_nav/nav_newsletter.jpg?w=750&amp;auto=format&amp;q=75 750w, https://lonelyplanetstatic.imgix.net/marketing/2025/global_nav/nav_newsletter.jpg?w=828&amp;auto=format&amp;q=75 828w, https://lonelyplanetstatic.imgix.net/marketing/2025/global_nav/nav_newsletter.jpg?w=1024&amp;auto=format&amp;q=75 1024w, https://lonelyplanetstatic.imgix.net/marketing/2025/global_nav/nav_newsletter.jpg?w=1080&amp;auto=format&amp;q=75 1080w, https://lonelyplanetstatic.imgix.net/marketing/2025/global_nav/nav_newsletter.jpg?w=1200&amp;auto=format&amp;q=75 1200w, https://lonelyplanetstatic.imgix.net/marketing/2025/global_nav/nav_newsletter.jpg?w=1280&amp;auto=format&amp;q=75 1280w, https://lonelyplanetstatic.imgix.net/marketing/2025/global_nav/nav_newsletter.jpg?w=1920&amp;auto=format&amp;q=75 1920w, https://lonelyplanetstatic.imgix.net/marketing/2025/global_nav/nav_newsletter.jpg?w=2048&amp;auto=format&amp;q=75 2048w, https://lonelyplanetstatic.imgix.net/marketing/2025/global_nav/nav_newsletter.jpg?w=3840&amp;auto=format&amp;q=75 3840w" src="https://lonelyplanetstatic.imgix.net/marketing/2025/global_nav/nav_newsletter.jpg?w=3840&amp;auto=format&amp;q=75"/>
                                                                        </div>
                                                                        <div class="flex flex-col justify-between gap-y-2">
                                                                            <div>
                                                                                <h3 class="mb-2 text-2xl font-semibold uppercase">Subscribe to Lonely Planet &#x27;s newsletter</h3>
                                                                                <p class="mb-4 text-base normal-case whitespace-pre-line">Join our community to get discounts, travel inspiration and trip ideas – just in time for summer!</p>
                                                                            </div>
                                                                            <p class="flex items-center justify-between pb-2 mb-4 text-base uppercase border-b border-black-200 group-hover/arrow:border-blue group-hover/arrow:text-blue">
                                                                                Learn more about our newsletters
                                                                                <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                                    <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                                </svg>
                                                                            </p>
                                                                        </div>
                                                                    </div>
                                                                </a>
                                                            </div>
                                                        </div>
                                                        <div class="submenu-section" data-section-index="3" id="section-Stories-3" style="display:none" role="region" aria-label="By Topic">
                                                            <div class="grid grid-cols-3 gap-4">
                                                                <a href="https://www.lonelyplanet.com/articles/category/adventure-travel" class="block pb-2 mb-2 border-b border-black-200 hover:text-blue hover:border-blue group/arrow" role="menuitem">
                                                                    <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                        Adventure Travel
                                                                        <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                            <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                        </svg>
                                                                    </span>
                                                                </a>
                                                                <a href="https://www.lonelyplanet.com/articles/category/art-and-culture" class="block pb-2 mb-2 border-b border-black-200 hover:text-blue hover:border-blue group/arrow" role="menuitem">
                                                                    <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                        Art and Culture
                                                                        <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                            <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                        </svg>
                                                                    </span>
                                                                </a>
                                                                <a href="https://www.lonelyplanet.com/articles/category/beaches" class="block pb-2 mb-2 border-b border-black-200 hover:text-blue hover:border-blue group/arrow" role="menuitem">
                                                                    <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                        Beaches, Coasts and Islands
                                                                        <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                            <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                        </svg>
                                                                    </span>
                                                                </a>
                                                                <a href="https://www.lonelyplanet.com/articles/category/family-travel" class="block pb-2 mb-2 border-b border-black-200 hover:text-blue hover:border-blue group/arrow" role="menuitem">
                                                                    <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                        Family Holidays
                                                                        <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                            <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                        </svg>
                                                                    </span>
                                                                </a>
                                                                <a href="https://www.lonelyplanet.com/articles/category/festivals-and-events" class="block pb-2 mb-2 border-b border-black-200 hover:text-blue hover:border-blue group/arrow" role="menuitem">
                                                                    <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                        Festivals
                                                                        <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                            <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                        </svg>
                                                                    </span>
                                                                </a>
                                                                <a href="https://www.lonelyplanet.com/articles/category/food-and-drink" class="block pb-2 mb-2 border-b border-black-200 hover:text-blue hover:border-blue group/arrow" role="menuitem">
                                                                    <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                        Food and Drink
                                                                        <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                            <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                        </svg>
                                                                    </span>
                                                                </a>
                                                                <a href="https://www.lonelyplanet.com/articles/category/romance" class="block pb-2 mb-2 border-b border-black-200 hover:text-blue hover:border-blue group/arrow" role="menuitem">
                                                                    <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                        Honeymoon and Romance
                                                                        <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                            <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                        </svg>
                                                                    </span>
                                                                </a>
                                                                <a href="https://www.lonelyplanet.com/articles/category/road-trips" class="block pb-2 mb-2 border-b border-black-200 hover:text-blue hover:border-blue group/arrow" role="menuitem">
                                                                    <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                        Road Trips
                                                                        <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                            <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                        </svg>
                                                                    </span>
                                                                </a>
                                                                <a href="https://www.lonelyplanet.com/articles/category/sustainable-travel" class="block pb-2 mb-2 border-b border-black-200 hover:text-blue hover:border-blue group/arrow" role="menuitem">
                                                                    <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                        Sustainable Travel
                                                                        <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                            <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                        </svg>
                                                                    </span>
                                                                </a>
                                                                <a href="https://www.lonelyplanet.com/articles/category/budget-travel" class="block pb-2 mb-2 border-b border-black-200 hover:text-blue hover:border-blue group/arrow" role="menuitem">
                                                                    <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                        Travel on a Budget
                                                                        <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                            <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                        </svg>
                                                                    </span>
                                                                </a>
                                                                <a href="https://www.lonelyplanet.com/articles/category/wildlife-and-nature" class="block pb-2 mb-2 border-b border-black-200 hover:text-blue hover:border-blue group/arrow" role="menuitem">
                                                                    <span class="flex items-center justify-between text-sm uppercase gap-x-2">
                                                                        Wildlife and Nature
                                                                        <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="invisible w-4 h-4 group-hover/arrow:visible" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                                            <path d="M14.4998 7.80903C14.4742 7.74825 14.4372 7.69292 14.3908 7.64603L8.68084 1.93803C8.58696 1.84427 8.45967 1.79165 8.32699 1.79175C8.19431 1.79184 8.0671 1.84464 7.97334 1.93853C7.87959 2.03241 7.82697 2.1597 7.82707 2.29238C7.82716 2.42506 7.87996 2.55227 7.97384 2.64603L12.8278 7.50003H1.96484C1.83224 7.50003 1.70506 7.5527 1.61129 7.64647C1.51752 7.74024 1.46484 7.86742 1.46484 8.00003C1.46484 8.13263 1.51752 8.25981 1.61129 8.35358C1.70506 8.44735 1.83224 8.50003 1.96484 8.50003H12.8278L7.97384 13.354C7.87996 13.4478 7.82716 13.575 7.82707 13.7077C7.82697 13.8404 7.87959 13.9676 7.97334 14.0615C8.0671 14.1554 8.19431 14.2082 8.32699 14.2083C8.45967 14.2084 8.58696 14.1558 8.68084 14.062L14.3878 8.35403C14.4342 8.30713 14.4712 8.2518 14.4968 8.19103C14.5478 8.069 14.5489 7.93184 14.4998 7.80903Z"></path>
                                                                        </svg>
                                                                    </span>
                                                                </a>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="hidden lg:block">
                                    <div class="relative group lg:px-8 lg:py-2">
                                        <button type="button" class="flex items-center justify-center p-2 transition-colors rounded-full bg-black-100 hover:bg-blue hover:text-white lg:bg-transparent lg:p-0 lg:hover:bg-transparent lg:hover:text-blue" aria-label="Search">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" class="inline-flex" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                                                <path d="M15.3542 14.646L11.9312 11.224C13.028 9.94433 13.5865 8.28959 13.4896 6.60701C13.3926 4.92443 12.6476 3.34483 11.411 2.19971C10.1744 1.05458 8.54232 0.432967 6.85726 0.465313C5.1722 0.49766 3.56517 1.18145 2.37343 2.37319C1.1817 3.56493 0.497904 5.17196 0.465557 6.85702C0.433211 8.54208 1.05483 10.1742 2.19995 11.4108C3.34507 12.6474 4.92467 13.3923 6.60725 13.4893C8.28983 13.5863 9.94457 13.0278 11.2242 11.931L14.6462 15.354C14.7401 15.4478 14.8674 15.5006 15.0002 15.5006C15.133 15.5006 15.2603 15.4478 15.3542 15.354C15.4481 15.2601 15.5008 15.1327 15.5008 15C15.5008 14.8672 15.4481 14.7398 15.3542 14.646ZM1.5002 6.99996C1.5002 5.91216 1.82277 4.84879 2.42712 3.94432C3.03147 3.03985 3.89045 2.3349 4.89544 1.91862C5.90044 1.50234 7.0063 1.39342 8.0732 1.60564C9.14009 1.81786 10.1201 2.34168 10.8893 3.11087C11.6585 3.88006 12.1823 4.86007 12.3945 5.92696C12.6067 6.99386 12.4978 8.09972 12.0815 9.10472C11.6653 10.1097 10.9603 10.9687 10.0558 11.573C9.15137 12.1774 8.088 12.5 7.0002 12.5C5.542 12.4984 4.14398 11.9184 3.11287 10.8873C2.08176 9.85618 1.50179 8.45816 1.5002 6.99996Z"></path>
                                            </svg>
                                            <span class="sr-only">Search</span>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="items-center hidden lg:flex gap-x-4 lg:gap-x-6">
                            <a class="flex items-center hover:text-blue" href="https://shop.lonelyplanet.com/cart?utm_source=lonelyplanet&amp;utm_campaign=lpcart">
                                <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="mr-2" viewBox="0 0 576 512" fill="currentColor" aria-hidden="true">
                                    <path d="M0 24C0 10.7 10.7 0 24 0H69.5c22 0 41.5 12.8 50.6 32h411c26.3 0 45.5 25 38.6 50.4l-41 152.3c-8.5 31.4-37 53.3-69.5 53.3H170.7l5.4 28.5c2.2 11.3 12.1 19.5 23.6 19.5H488c13.3 0 24 10.7 24 24s-10.7 24-24 24H199.7c-34.6 0-64.3-24.6-70.7-58.5L77.4 54.5c-.7-3.8-4-6.5-7.9-6.5H24C10.7 48 0 37.3 0 24zM128 464a48 48 0 1 1 96 0 48 48 0 1 1 -96 0zm336-48a48 48 0 1 1 0 96 48 48 0 1 1 0-96z"></path>
                                </svg>
                                CART
                            </a>
                            <div class="relative py-2 group">
                                <div class="flex items-center h-full">
                                    <a class="relative z-10 px-4 py-2 text-white pointer-events-auto rounded-pill bg-blue btn btn-primary focus:outline-hidden whitespace-nowrap" href="/api/auth/login" aria-label="Sign in">Sign In</a>
                                </div>
                            </div>
                        </div>
                        <div class="flex lg:hidden">
                            <button type="button" class="-m-2.5 inline-flex items-center justify-center rounded-md p-2.5-400 relative w-10 h-10" id="mobile-menu-button" aria-expanded="false" aria-controls="mobile-menu">
                                <span class="sr-only">Open main menu</span>
                                <svg width="20" height="20" class="absolute hamburger-icon block" viewBox="0 0 14 12" fill="currentColor" xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
                                    <path d="M1 11.25H13C13.1989 11.25 13.3897 11.171 13.5303 11.0303C13.671 10.8897 13.75 10.6989 13.75 10.5C13.75 10.3011 13.671 10.1103 13.5303 9.96967C13.3897 9.82902 13.1989 9.75 13 9.75H1C0.801088 9.75 0.610323 9.82902 0.46967 9.96967C0.329018 10.1103 0.25 10.3011 0.25 10.5C0.25 10.6989 0.329018 10.8897 0.46967 11.0303C0.610323 11.171 0.801088 11.25 1 11.25Z"></path>
                                    <path d="M1 6.75H11C11.1989 6.75 11.3897 6.67098 11.5303 6.53033C11.671 6.38968 11.75 6.19891 11.75 6C11.75 5.80109 11.671 5.61032 11.5303 5.46967C11.3897 5.32902 11.1989 5.25 11 5.25H1C0.801088 5.25 0.610323 5.32902 0.46967 5.46967C0.329018 5.61032 0.25 5.80109 0.25 6C0.25 6.19891 0.329018 6.38968 0.46967 6.53033C0.610323 6.67098 0.801088 6.75 1 6.75Z"></path>
                                    <path d="M13 0.75H1C0.801088 0.75 0.610323 0.829018 0.46967 0.96967C0.329018 1.11032 0.25 1.30109 0.25 1.5C0.25 1.69891 0.329018 1.88968 0.46967 2.03033C0.610323 2.17098 0.801088 2.25 1 2.25H13C13.1989 2.25 13.3897 2.17098 13.5303 2.03033C13.671 1.88968 13.75 1.69891 13.75 1.5C13.75 1.30109 13.671 1.11032 13.5303 0.96967C13.3897 0.829018 13.1989 0.75 13 0.75Z"></path>
                                </svg>
                                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" class="absolute hidden" viewBox="0 0 17 17" fill="currentColor" aria-hidden="true">
                                    <path d="M15.6587 15.66L1.00067 1M15.6587 1.00133L1 15.66L15.6587 1.00133Z" stroke="currentColor" stroke-width="1.4" stroke-miterlimit="10"></path>
                                </svg>
                            </button>
                        </div>
                    </nav>
                </header>
                <div class="fixed inset-0 z-50 bg-white transform transition-transform duration-300 ease-in-out translate-x-full pointer-events-auto lg:hidden top-[118px]" role="navigation" aria-label="Mobile navigation">
                    <div class="h-full overflow-y-auto">
                        <nav class="w-full">
                            <div>
                                <div class="border-b border-black-200">
                                    <div class="container">
                                        <button type="button" data-panel-trigger="Destinations" class="flex items-center justify-between w-full py-4 text-lg text-black uppercase hover:text-blue" aria-expanded="false" aria-controls="panel-Destinations">
                                            Destinations
                                            <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="w-4 h-4" viewBox="0 0 8 12" fill="currentColor" aria-hidden="true">
                                                <path d="M1.08921 0.148971C1.03905 0.106489 0.981007 0.074303 0.918404 0.0542515C0.8558 0.0341999 0.78986 0.0266754 0.724348 0.0321076C0.592041 0.0430785 0.469511 0.106159 0.383714 0.207471C0.297916 0.308784 0.255879 0.44003 0.26685 0.572337C0.277821 0.704644 0.340901 0.827174 0.442214 0.912971L6.46021 5.99997L0.442214 11.087C0.340901 11.1728 0.277821 11.2953 0.26685 11.4276C0.255879 11.5599 0.297916 11.6912 0.383714 11.7925C0.469511 11.8938 0.592041 11.9569 0.724348 11.9678C0.856655 11.9788 0.987901 11.9368 1.08921 11.851L7.55821 6.38197C7.61379 6.33504 7.65846 6.27654 7.68909 6.21056C7.71973 6.14458 7.73561 6.07272 7.73561 5.99997C7.73561 5.92723 7.71973 5.85536 7.68909 5.78938C7.65846 5.7234 7.61379 5.66491 7.55821 5.61797L1.08921 0.148971Z"></path>
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                                <div class="border-b border-black-200">
                                    <div class="container">
                                        <button type="button" data-panel-trigger="Books" class="flex items-center justify-between w-full py-4 text-lg text-black uppercase hover:text-blue" aria-expanded="false" aria-controls="panel-Books">
                                            Books
                                            <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="w-4 h-4" viewBox="0 0 8 12" fill="currentColor" aria-hidden="true">
                                                <path d="M1.08921 0.148971C1.03905 0.106489 0.981007 0.074303 0.918404 0.0542515C0.8558 0.0341999 0.78986 0.0266754 0.724348 0.0321076C0.592041 0.0430785 0.469511 0.106159 0.383714 0.207471C0.297916 0.308784 0.255879 0.44003 0.26685 0.572337C0.277821 0.704644 0.340901 0.827174 0.442214 0.912971L6.46021 5.99997L0.442214 11.087C0.340901 11.1728 0.277821 11.2953 0.26685 11.4276C0.255879 11.5599 0.297916 11.6912 0.383714 11.7925C0.469511 11.8938 0.592041 11.9569 0.724348 11.9678C0.856655 11.9788 0.987901 11.9368 1.08921 11.851L7.55821 6.38197C7.61379 6.33504 7.65846 6.27654 7.68909 6.21056C7.71973 6.14458 7.73561 6.07272 7.73561 5.99997C7.73561 5.92723 7.71973 5.85536 7.68909 5.78938C7.65846 5.7234 7.61379 5.66491 7.55821 5.61797L1.08921 0.148971Z"></path>
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                                <div class="border-b border-black-200">
                                    <div class="container">
                                        <button type="button" data-panel-trigger="Trips" class="flex items-center justify-between w-full py-4 text-lg text-black uppercase hover:text-blue" aria-expanded="false" aria-controls="panel-Trips">
                                            Trips
                                            <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="w-4 h-4" viewBox="0 0 8 12" fill="currentColor" aria-hidden="true">
                                                <path d="M1.08921 0.148971C1.03905 0.106489 0.981007 0.074303 0.918404 0.0542515C0.8558 0.0341999 0.78986 0.0266754 0.724348 0.0321076C0.592041 0.0430785 0.469511 0.106159 0.383714 0.207471C0.297916 0.308784 0.255879 0.44003 0.26685 0.572337C0.277821 0.704644 0.340901 0.827174 0.442214 0.912971L6.46021 5.99997L0.442214 11.087C0.340901 11.1728 0.277821 11.2953 0.26685 11.4276C0.255879 11.5599 0.297916 11.6912 0.383714 11.7925C0.469511 11.8938 0.592041 11.9569 0.724348 11.9678C0.856655 11.9788 0.987901 11.9368 1.08921 11.851L7.55821 6.38197C7.61379 6.33504 7.65846 6.27654 7.68909 6.21056C7.71973 6.14458 7.73561 6.07272 7.73561 5.99997C7.73561 5.92723 7.71973 5.85536 7.68909 5.78938C7.65846 5.7234 7.61379 5.66491 7.55821 5.61797L1.08921 0.148971Z"></path>
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                                <div class="border-b border-black-200">
                                    <div class="container">
                                        <button type="button" data-panel-trigger="Stories" class="flex items-center justify-between w-full py-4 text-lg text-black uppercase hover:text-blue" aria-expanded="false" aria-controls="panel-Stories">
                                            Stories
                                            <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="w-4 h-4" viewBox="0 0 8 12" fill="currentColor" aria-hidden="true">
                                                <path d="M1.08921 0.148971C1.03905 0.106489 0.981007 0.074303 0.918404 0.0542515C0.8558 0.0341999 0.78986 0.0266754 0.724348 0.0321076C0.592041 0.0430785 0.469511 0.106159 0.383714 0.207471C0.297916 0.308784 0.255879 0.44003 0.26685 0.572337C0.277821 0.704644 0.340901 0.827174 0.442214 0.912971L6.46021 5.99997L0.442214 11.087C0.340901 11.1728 0.277821 11.2953 0.26685 11.4276C0.255879 11.5599 0.297916 11.6912 0.383714 11.7925C0.469511 11.8938 0.592041 11.9569 0.724348 11.9678C0.856655 11.9788 0.987901 11.9368 1.08921 11.851L7.55821 6.38197C7.61379 6.33504 7.65846 6.27654 7.68909 6.21056C7.71973 6.14458 7.73561 6.07272 7.73561 5.99997C7.73561 5.92723 7.71973 5.85536 7.68909 5.78938C7.65846 5.7234 7.61379 5.66491 7.55821 5.61797L1.08921 0.148971Z"></path>
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                                <div class="border-b border-black-200">
                                    <div class="container">
                                        <button type="button" data-panel-trigger="Account" class="flex items-center justify-between w-full py-4 text-lg text-black uppercase hover:text-blue" aria-expanded="false" aria-controls="panel-Account">
                                            Account
                                            <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="w-4 h-4" viewBox="0 0 8 12" fill="currentColor" aria-hidden="true">
                                                <path d="M1.08921 0.148971C1.03905 0.106489 0.981007 0.074303 0.918404 0.0542515C0.8558 0.0341999 0.78986 0.0266754 0.724348 0.0321076C0.592041 0.0430785 0.469511 0.106159 0.383714 0.207471C0.297916 0.308784 0.255879 0.44003 0.26685 0.572337C0.277821 0.704644 0.340901 0.827174 0.442214 0.912971L6.46021 5.99997L0.442214 11.087C0.340901 11.1728 0.277821 11.2953 0.26685 11.4276C0.255879 11.5599 0.297916 11.6912 0.383714 11.7925C0.469511 11.8938 0.592041 11.9569 0.724348 11.9678C0.856655 11.9788 0.987901 11.9368 1.08921 11.851L7.55821 6.38197C7.61379 6.33504 7.65846 6.27654 7.68909 6.21056C7.71973 6.14458 7.73561 6.07272 7.73561 5.99997C7.73561 5.92723 7.71973 5.85536 7.68909 5.78938C7.65846 5.7234 7.61379 5.66491 7.55821 5.61797L1.08921 0.148971Z"></path>
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </nav>
                    </div>
                    <div class="absolute z-10 w-full px-4 bottom-4">
                        <a class="flex btn btn-primary" href="https://shop.lonelyplanet.com/cart?utm_source=lonelyplanet&amp;utm_campaign=lpcart">
                            <div class="flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="mr-2" viewBox="0 0 576 512" fill="currentColor" aria-hidden="true">
                                    <path d="M0 24C0 10.7 10.7 0 24 0H69.5c22 0 41.5 12.8 50.6 32h411c26.3 0 45.5 25 38.6 50.4l-41 152.3c-8.5 31.4-37 53.3-69.5 53.3H170.7l5.4 28.5c2.2 11.3 12.1 19.5 23.6 19.5H488c13.3 0 24 10.7 24 24s-10.7 24-24 24H199.7c-34.6 0-64.3-24.6-70.7-58.5L77.4 54.5c-.7-3.8-4-6.5-7.9-6.5H24C10.7 48 0 37.3 0 24zM128 464a48 48 0 1 1 96 0 48 48 0 1 1 -96 0zm336-48a48 48 0 1 1 0 96 48 48 0 1 1 0-96z"></path>
                                </svg>
                                Cart
                            </div>
                        </a>
                    </div>
                </div>
                <main>
                    <div>
                        <section class="hero_hero__ZRqSa relative z-10 pb-6 lg:pb-16">
                            <figure>
                                <div class="h-full"></div>
                            </figure>
                        </section>
                        <section aria-labelledby="explore" class="container pb-6 lg:pb-16 lg:flex lg:flex-wrap lg:justify-around mx-auto relative">
                            <div class="lg:w-2/3 lg:pr-8 max-w-4xl">
                                <h1 class="text-3xl lg:text-6xl text-blue font-semibold contents">
                                    <span></span>
                                </h1>
                                <div class="text-xl inline"></div>
                                <form class="mb-8" name="places-list" action="/places">
                                    <div class="my-8 lg:flex">
                                        <div class="mb-2 lg:w-1/2 lg:pr-8">
                                            <label for="placeType">
                                                Filter by type of place
                                                <select aria-label="Filter by type of place" class="mt-1 w-full bg-white border border-black-200 rounded-lg h-10 cursor-pointer pl-4 pr-8 pt-0 pb-0 disabled:cursor-not-allowed disabled:opacity-20" id="placeType" name="type">
                                                    <option value="All" selected="">All types</option>
                                                    <option value="Continent">Continents &amp;subcontinents</option>
                                                    <option value="Region">Regions</option>
                                                    <option value="Country">Countries</option>
                                                    <option value="City">Cities</option>
                                                    <option value="Neighborhood">Neighborhoods</option>
                                                </select>
                                            </label>
                                        </div>
                                        <div class="mb-4 lg:w-1/2 lg:pl-8">
                                            <label for="placeSort">
                                                Sort places by
                                                <select aria-label="Sort places by" class="mt-1 w-full bg-white border border-black-200 rounded-lg h-10 cursor-pointer pl-4 pr-8 pt-0 pb-0 disabled:cursor-not-allowed disabled:opacity-20" id="placeSort" name="sort">
                                                    <option value="DESC" selected="">Most Popular</option>
                                                    <option value="ALPHA_ASC">A - Z</option>
                                                    <option value="ALPHA_DESC">Z - A</option>
                                                </select>
                                            </label>
                                        </div>
                                    </div>
                                </form>
                                <ul class="flex flex-row flex-wrap sm:-mx-2">
                                    <li class="w-full sm:w-1/2 lg:w-1/3 sm:px-4 mb-8">
                                        <article class="relative flex-shrink-0 lg:flex-shrink mx-4 first:ml-0 last:mr-0 w-full aspect-square">
                                            <img alt="Fjallbacka, Sweden" loading="lazy" width="360" height="360" decoding="async" data-nimg="1" class="max-w-full object-cover rounded w-full h-full max-h-96" style="color:transparent" srcSet="https://lp-cms-production.imgix.net/2023-03/GettyRF_483631496.jpg?fit=crop&amp;w=360&amp;ar=1%3A1&amp;auto=format&amp;q=75 1x, https://lp-cms-production.imgix.net/2023-03/GettyRF_483631496.jpg?fit=crop&amp;w=360&amp;ar=1%3A1&amp;auto=format&amp;q=75 2x" src="https://lp-cms-production.imgix.net/2023-03/GettyRF_483631496.jpg?fit=crop&amp;w=360&amp;ar=1%3A1&amp;auto=format&amp;q=75"/>
                                            <div class="">
                                                <a href="/destinations/scandinavia" class="text-lg font-bold card-link lg:text-xl">
                                                    <div class="text-sm uppercase font-semibold tracking-wide z-10 mt-4 text-black-400 block">Europe</div>
                                                    Scandinavia
                                                </a>
                                            </div>
                                        </article>
                                    </li>
                                    <li class="w-full sm:w-1/2 lg:w-1/3 sm:px-4 mb-8">
                                        <article class="relative flex-shrink-0 lg:flex-shrink mx-4 first:ml-0 last:mr-0 w-full aspect-square">
                                            <img alt="Plaza de Espana in Seville." loading="lazy" width="360" height="360" decoding="async" data-nimg="1" class="max-w-full object-cover rounded w-full h-full max-h-96" style="color:transparent" srcSet="https://lp-cms-production.imgix.net/2022-12/GettyImages-521901580.jpeg?fit=crop&amp;w=360&amp;ar=1%3A1&amp;auto=format&amp;q=75 1x, https://lp-cms-production.imgix.net/2022-12/GettyImages-521901580.jpeg?fit=crop&amp;w=360&amp;ar=1%3A1&amp;auto=format&amp;q=75 2x" src="https://lp-cms-production.imgix.net/2022-12/GettyImages-521901580.jpeg?fit=crop&amp;w=360&amp;ar=1%3A1&amp;auto=format&amp;q=75"/>
                                            <div class="">
                                                <a href="/destinations/spain" class="text-lg font-bold card-link lg:text-xl">
                                                    <div class="text-sm uppercase font-semibold tracking-wide z-10 mt-4 text-black-400 block">Mediterranean Europe</div>
                                                    Spain
                                                </a>
                                            </div>
                                        </article>
                                    </li>
                                    <li class="w-full sm:w-1/2 lg:w-1/3 sm:px-4 mb-8">
                                        <article class="relative flex-shrink-0 lg:flex-shrink mx-4 first:ml-0 last:mr-0 w-full aspect-square">
                                            <img alt="The Taj Mahal as viewed through the doorway of the main gate, which frames the building neatly. Crowds walk towards the white marble structure, which has a domed roof and four pillars surrounding it." loading="lazy" width="360" height="360" decoding="async" data-nimg="1" class="max-w-full object-cover rounded w-full h-full max-h-96" style="color:transparent" srcSet="https://lp-cms-production.imgix.net/2022-12/sylwia-bartyzel-eU4pipU_8HA.jpg?fit=crop&amp;w=360&amp;ar=1%3A1&amp;auto=format&amp;q=75 1x, https://lp-cms-production.imgix.net/2022-12/sylwia-bartyzel-eU4pipU_8HA.jpg?fit=crop&amp;w=360&amp;ar=1%3A1&amp;auto=format&amp;q=75 2x" src="https://lp-cms-production.imgix.net/2022-12/sylwia-bartyzel-eU4pipU_8HA.jpg?fit=crop&amp;w=360&amp;ar=1%3A1&amp;auto=format&amp;q=75"/>
                                            <div class="">
                                                <a href="/destinations/india" class="text-lg font-bold card-link lg:text-xl">
                                                    <div class="text-sm uppercase font-semibold tracking-wide z-10 mt-4 text-black-400 block">Asia</div>
                                                    India
                                                </a>
                                            </div>
                                        </article>
                                    </li>
                                    <li class="w-full sm:w-1/2 lg:w-1/3 sm:px-4 mb-8">
                                        <article class="relative flex-shrink-0 lg:flex-shrink mx-4 first:ml-0 last:mr-0 w-full aspect-square">
                                            <img alt="Woman enjoying the lavender fields in Provence. France. Aerial view." loading="lazy" width="360" height="360" decoding="async" data-nimg="1" class="max-w-full object-cover rounded w-full h-full max-h-96" style="color:transparent" srcSet="https://lp-cms-production.imgix.net/2021-10/Woman%20in%20lavender%20field%20By%20CACTUS%20Creative%20Studio%20Stocksy_txp95a12c14B4D300_Medium_2725600.jpg?fit=crop&amp;w=360&amp;ar=1%3A1&amp;auto=format&amp;q=75 1x, https://lp-cms-production.imgix.net/2021-10/Woman%20in%20lavender%20field%20By%20CACTUS%20Creative%20Studio%20Stocksy_txp95a12c14B4D300_Medium_2725600.jpg?fit=crop&amp;w=360&amp;ar=1%3A1&amp;auto=format&amp;q=75 2x" src="https://lp-cms-production.imgix.net/2021-10/Woman%20in%20lavender%20field%20By%20CACTUS%20Creative%20Studio%20Stocksy_txp95a12c14B4D300_Medium_2725600.jpg?fit=crop&amp;w=360&amp;ar=1%3A1&amp;auto=format&amp;q=75"/>
                                            <div class="">
                                                <a href="/destinations/france" class="text-lg font-bold card-link lg:text-xl">
                                                    <div class="text-sm uppercase font-semibold tracking-wide z-10 mt-4 text-black-400 block">Western Europe</div>
                                                    France
                                                </a>
                                            </div>
                                        </article>
                                    </li>
                                    <li class="w-full sm:w-1/2 lg:w-1/3 sm:px-4 mb-8">
                                        <article class="relative flex-shrink-0 lg:flex-shrink mx-4 first:ml-0 last:mr-0 w-full aspect-square">
                                            <img alt="Costa Rica mountains and hillside was amazing.  view into valley was unbelievable. It was so much green for miles, and miles. cloud formation." loading="lazy" width="360" height="360" decoding="async" data-nimg="1" class="max-w-full object-cover rounded w-full h-full max-h-96" style="color:transparent" srcSet="https://lp-cms-production.imgix.net/2023-02/Costa%20Rica%20David%20Thompson%20GettyImages-120472128%20RFC.jpg?fit=crop&amp;w=360&amp;ar=1%3A1&amp;auto=format&amp;q=75 1x, https://lp-cms-production.imgix.net/2023-02/Costa%20Rica%20David%20Thompson%20GettyImages-120472128%20RFC.jpg?fit=crop&amp;w=360&amp;ar=1%3A1&amp;auto=format&amp;q=75 2x" src="https://lp-cms-production.imgix.net/2023-02/Costa%20Rica%20David%20Thompson%20GettyImages-120472128%20RFC.jpg?fit=crop&amp;w=360&amp;ar=1%3A1&amp;auto=format&amp;q=75"/>
                                            <div class="">
                                                <a href="/destinations/central-america" class="text-lg font-bold card-link lg:text-xl">
                                                    <div class="text-sm uppercase font-semibold tracking-wide z-10 mt-4 text-black-400 block">Americas</div>
                                                    Central America
                                                </a>
                                            </div>
                                        </article>
                                    </li>
                                    <li class="w-full sm:w-1/2 lg:w-1/3 sm:px-4 mb-8">
                                        <article class="relative flex-shrink-0 lg:flex-shrink mx-4 first:ml-0 last:mr-0 w-full aspect-square">
                                            <img alt="Kayaking in the Caribbean Sea." loading="lazy" width="360" height="360" decoding="async" data-nimg="1" class="max-w-full object-cover rounded w-full h-full max-h-96" style="color:transparent" srcSet="https://lp-cms-production.imgix.net/2021-10/Kayaking%20in%20the%20Caribbean%20Sea%20Antionio%20Busiello%20GettyImages-1167011568%20rfc.jpg?fit=crop&amp;w=360&amp;ar=1%3A1&amp;auto=format&amp;q=75 1x, https://lp-cms-production.imgix.net/2021-10/Kayaking%20in%20the%20Caribbean%20Sea%20Antionio%20Busiello%20GettyImages-1167011568%20rfc.jpg?fit=crop&amp;w=360&amp;ar=1%3A1&amp;auto=format&amp;q=75 2x" src="https://lp-cms-production.imgix.net/2021-10/Kayaking%20in%20the%20Caribbean%20Sea%20Antionio%20Busiello%20GettyImages-1167011568%20rfc.jpg?fit=crop&amp;w=360&amp;ar=1%3A1&amp;auto=format&amp;q=75"/>
                                            <div class="">
                                                <a href="/destinations/caribbean" class="text-lg font-bold card-link lg:text-xl">
                                                    <div class="text-sm uppercase font-semibold tracking-wide z-10 mt-4 text-black-400 block">Americas</div>
                                                    Caribbean
                                                </a>
                                            </div>
                                        </article>
                                    </li>
                                    <li class="w-full sm:w-1/2 lg:w-1/3 sm:px-4 mb-8">
                                        <article class="relative flex-shrink-0 lg:flex-shrink mx-4 first:ml-0 last:mr-0 w-full aspect-square">
                                            <img alt="Tiananmen under sky and cloud.
*********
ancient, beijing, blue, building, china, chinese, city, communism, famous, forbidden, gate, historic, history, landmark, landscape, mao, monument, nature, ornate, palace, peoples, prc, red, sky, square, summer, temple, tiananmen, tourism, travel, white" loading="lazy" width="360" height="360" decoding="async" data-nimg="1" class="max-w-full object-cover rounded w-full h-full max-h-96" style="color:transparent" srcSet="https://lp-cms-production.imgix.net/2023-03/shutterstockRF_*********.jpg?fit=crop&amp;w=360&amp;ar=1%3A1&amp;auto=format&amp;q=75 1x, https://lp-cms-production.imgix.net/2023-03/shutterstockRF_*********.jpg?fit=crop&amp;w=360&amp;ar=1%3A1&amp;auto=format&amp;q=75 2x" src="https://lp-cms-production.imgix.net/2023-03/shutterstockRF_*********.jpg?fit=crop&amp;w=360&amp;ar=1%3A1&amp;auto=format&amp;q=75"/>
                                            <div class="">
                                                <a href="/destinations/china" class="text-lg font-bold card-link lg:text-xl">
                                                    <div class="text-sm uppercase font-semibold tracking-wide z-10 mt-4 text-black-400 block">Asia</div>
                                                    China
                                                </a>
                                            </div>
                                        </article>
                                    </li>
                                    <li class="w-full sm:w-1/2 lg:w-1/3 sm:px-4 mb-8">
                                        <article class="relative flex-shrink-0 lg:flex-shrink mx-4 first:ml-0 last:mr-0 w-full aspect-square">
                                            <img alt="Herd of elephants walking through grassland." loading="lazy" width="360" height="360" decoding="async" data-nimg="1" class="max-w-full object-cover rounded w-full h-full max-h-96" style="color:transparent" srcSet="https://lp-cms-production.imgix.net/2019-06/7639de4149312929192b15efb4cdfd2d8220ee244320b176aaf65f341d3bc9cb.jpg?fit=crop&amp;w=360&amp;ar=1%3A1&amp;auto=format&amp;q=75 1x, https://lp-cms-production.imgix.net/2019-06/7639de4149312929192b15efb4cdfd2d8220ee244320b176aaf65f341d3bc9cb.jpg?fit=crop&amp;w=360&amp;ar=1%3A1&amp;auto=format&amp;q=75 2x" src="https://lp-cms-production.imgix.net/2019-06/7639de4149312929192b15efb4cdfd2d8220ee244320b176aaf65f341d3bc9cb.jpg?fit=crop&amp;w=360&amp;ar=1%3A1&amp;auto=format&amp;q=75"/>
                                            <div class="">
                                                <a href="/destinations/southern-africa" class="text-lg font-bold card-link lg:text-xl">
                                                    <div class="text-sm uppercase font-semibold tracking-wide z-10 mt-4 text-black-400 block">Africa</div>
                                                    Southern Africa
                                                </a>
                                            </div>
                                        </article>
                                    </li>
                                    <li class="w-full sm:w-1/2 lg:w-1/3 sm:px-4 mb-8">
                                        <article class="relative flex-shrink-0 lg:flex-shrink mx-4 first:ml-0 last:mr-0 w-full aspect-square">
                                            <img alt="The Long Walk to Windsor Castle." loading="lazy" width="360" height="360" decoding="async" data-nimg="1" class="max-w-full object-cover rounded w-full h-full max-h-96" style="color:transparent" srcSet="https://lp-cms-production.imgix.net/2021-10/The%20Long%20Walk%20to%20Windsor%20Castle%20Chris%20Jackson%20GettyImages-1223590078%20RM.jpg?fit=crop&amp;w=360&amp;ar=1%3A1&amp;auto=format&amp;q=75 1x, https://lp-cms-production.imgix.net/2021-10/The%20Long%20Walk%20to%20Windsor%20Castle%20Chris%20Jackson%20GettyImages-1223590078%20RM.jpg?fit=crop&amp;w=360&amp;ar=1%3A1&amp;auto=format&amp;q=75 2x" src="https://lp-cms-production.imgix.net/2021-10/The%20Long%20Walk%20to%20Windsor%20Castle%20Chris%20Jackson%20GettyImages-1223590078%20RM.jpg?fit=crop&amp;w=360&amp;ar=1%3A1&amp;auto=format&amp;q=75"/>
                                            <div class="">
                                                <a href="/destinations/england" class="text-lg font-bold card-link lg:text-xl">
                                                    <div class="text-sm uppercase font-semibold tracking-wide z-10 mt-4 text-black-400 block">Great Britain</div>
                                                    England
                                                </a>
                                            </div>
                                        </article>
                                    </li>
                                    <li class="w-full sm:w-1/2 lg:w-1/3 sm:px-4 mb-8">
                                        <article class="relative flex-shrink-0 lg:flex-shrink mx-4 first:ml-0 last:mr-0 w-full aspect-square">
                                            <img alt="Hiker stands amidst a field of wildflowers on the rolling Carrizo Hills" loading="lazy" width="360" height="360" decoding="async" data-nimg="1" class="max-w-full object-cover rounded w-full h-full max-h-96" style="color:transparent" srcSet="https://lp-cms-production.imgix.net/2021-10/Carrizo%20Hills%20Superbloom%20By%20Nathan%20Yan%20Stocksy_txp95a12c14B4D300_Medium_1715586.jpg?fit=crop&amp;w=360&amp;ar=1%3A1&amp;auto=format&amp;q=75 1x, https://lp-cms-production.imgix.net/2021-10/Carrizo%20Hills%20Superbloom%20By%20Nathan%20Yan%20Stocksy_txp95a12c14B4D300_Medium_1715586.jpg?fit=crop&amp;w=360&amp;ar=1%3A1&amp;auto=format&amp;q=75 2x" src="https://lp-cms-production.imgix.net/2021-10/Carrizo%20Hills%20Superbloom%20By%20Nathan%20Yan%20Stocksy_txp95a12c14B4D300_Medium_1715586.jpg?fit=crop&amp;w=360&amp;ar=1%3A1&amp;auto=format&amp;q=75"/>
                                            <div class="">
                                                <a href="/destinations/usa/california" class="text-lg font-bold card-link lg:text-xl">
                                                    <div class="text-sm uppercase font-semibold tracking-wide z-10 mt-4 text-black-400 block">Western USA</div>
                                                    California
                                                </a>
                                            </div>
                                        </article>
                                    </li>
                                    <li class="w-full sm:w-1/2 lg:w-1/3 sm:px-4 mb-8">
                                        <article class="relative flex-shrink-0 lg:flex-shrink mx-4 first:ml-0 last:mr-0 w-full aspect-square">
                                            <img alt="Bow Valley Provincial Park (part of the Kananaskis Country park system) is established east of the Canadian Rockies in the arch of the valley, while the upper course of the Bow River flows through the Banff National Park. The Canmore Nordic Centre Provincial Park is located between the Banff National Park and Canmore in the Bow River Valley." loading="lazy" width="360" height="360" decoding="async" data-nimg="1" class="max-w-full object-cover rounded w-full h-full max-h-96" style="color:transparent" srcSet="https://lp-cms-production.imgix.net/2021-10/Morants%20Curve%2C%20Bow%20River%2C%20Canadian%20Pacific%20Railway%2C%20near%20Lake%20Louise%2C%20Banff%20National%20Park%2C%20UNESCO%20World%20Heritage%20Site%2C%20Alberta%2C%20Rocky%20Mountains%2C%20Canada%2C%20North%20America%20Gavin%20Hellier%20Stocksy_txp95a12c14B4D300_Medium_35616.jpg?fit=crop&amp;w=360&amp;ar=1%3A1&amp;auto=format&amp;q=75 1x, https://lp-cms-production.imgix.net/2021-10/Morants%20Curve%2C%20Bow%20River%2C%20Canadian%20Pacific%20Railway%2C%20near%20Lake%20Louise%2C%20Banff%20National%20Park%2C%20UNESCO%20World%20Heritage%20Site%2C%20Alberta%2C%20Rocky%20Mountains%2C%20Canada%2C%20North%20America%20Gavin%20Hellier%20Stocksy_txp95a12c14B4D300_Medium_35616.jpg?fit=crop&amp;w=360&amp;ar=1%3A1&amp;auto=format&amp;q=75 2x" src="https://lp-cms-production.imgix.net/2021-10/Morants%20Curve%2C%20Bow%20River%2C%20Canadian%20Pacific%20Railway%2C%20near%20Lake%20Louise%2C%20Banff%20National%20Park%2C%20UNESCO%20World%20Heritage%20Site%2C%20Alberta%2C%20Rocky%20Mountains%2C%20Canada%2C%20North%20America%20Gavin%20Hellier%20Stocksy_txp95a12c14B4D300_Medium_35616.jpg?fit=crop&amp;w=360&amp;ar=1%3A1&amp;auto=format&amp;q=75"/>
                                            <div class="">
                                                <a href="/destinations/canada" class="text-lg font-bold card-link lg:text-xl">
                                                    <div class="text-sm uppercase font-semibold tracking-wide z-10 mt-4 text-black-400 block">North America</div>
                                                    Canada
                                                </a>
                                            </div>
                                        </article>
                                    </li>
                                    <li class="w-full sm:w-1/2 lg:w-1/3 sm:px-4 mb-8">
                                        <article class="relative flex-shrink-0 lg:flex-shrink mx-4 first:ml-0 last:mr-0 w-full aspect-square">
                                            <img alt="Herbs and spices for sale in spice suq." loading="lazy" width="360" height="360" decoding="async" data-nimg="1" class="max-w-full object-cover rounded w-full h-full max-h-96" style="color:transparent" srcSet="https://lp-cms-production.imgix.net/2019-06/f071e0ebfc7ebca7c08346085152c1d924a6d8b41a753e5fb720e5aad7a0f81e.jpg?fit=crop&amp;w=360&amp;ar=1%3A1&amp;auto=format&amp;q=75 1x, https://lp-cms-production.imgix.net/2019-06/f071e0ebfc7ebca7c08346085152c1d924a6d8b41a753e5fb720e5aad7a0f81e.jpg?fit=crop&amp;w=360&amp;ar=1%3A1&amp;auto=format&amp;q=75 2x" src="https://lp-cms-production.imgix.net/2019-06/f071e0ebfc7ebca7c08346085152c1d924a6d8b41a753e5fb720e5aad7a0f81e.jpg?fit=crop&amp;w=360&amp;ar=1%3A1&amp;auto=format&amp;q=75"/>
                                            <div class="">
                                                <a href="/destinations/middle-east" class="text-lg font-bold card-link lg:text-xl">
                                                    <div class="text-sm uppercase font-semibold tracking-wide z-10 mt-4 text-black-400 block"></div>
                                                    Middle East
                                                </a>
                                            </div>
                                        </article>
                                    </li>
                                </ul>
                                <div class="flex flex-wrap flex-row items-start justify-center my-12">
                                    <div class="relative pr-2">
                                        <a href="/places?type=All&amp;sort=DESC&amp;page=1" class="p-3 font-semibold block rounded-pill border border-black-200 capitalize cursor-pointer transform duration-300 hover:bg-black-300 hover:text-white hover:border-black-300">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" viewBox="0 0 8 12" fill="currentColor" aria-hidden="true">
                                                <path d="M7.61604 0.207989C7.53055 0.108094 7.40916 0.0458916 7.27815 0.0348489C7.14713 0.0238062 7.01704 0.0648124 6.91604 0.148989L0.44204 5.61799C0.386464 5.66492 0.341798 5.72342 0.31116 5.7894C0.280522 5.85538 0.264648 5.92724 0.264648 5.99999C0.264648 6.07273 0.280522 6.1446 0.31116 6.21058C0.341798 6.27656 0.386464 6.33505 0.44204 6.38199L6.91104 11.851C7.01235 11.9368 7.1436 11.9788 7.27591 11.9679C7.40821 11.9569 7.53074 11.8938 7.61654 11.7925C7.70234 11.6912 7.74438 11.5599 7.7334 11.4276C7.72243 11.2953 7.65935 11.1728 7.55804 11.087L1.54004 5.99999L7.55804 0.912989C7.60816 0.870521 7.64942 0.818592 7.67947 0.760171C7.70951 0.701749 7.72774 0.63798 7.73313 0.572508C7.73852 0.507035 7.73095 0.441143 7.71086 0.378597C7.69077 0.316051 7.65855 0.258077 7.61604 0.207989V0.207989Z"></path>
                                            </svg>
                                        </a>
                                    </div>
                                    <ul class="flex justify-center items-baseline">
                                        <li class="inline-block mb-2">
                                            <a href="/places?type=All&amp;sort=DESC&amp;page=1" class="block font-semibold text-xs rounded-pill border border-black-200 capitalize transform duration-300 py-2 px-3 sm:px-4 hover:bg-black-300 hover:text-white hover:border-black-300">1</a>
                                        </li>
                                        <li class="inline-block mb-2 ml-1 md:ml-2">
                                            <a href="/places?type=All&amp;sort=DESC&amp;page=2" class="block font-semibold text-xs rounded-pill border border-black-200 capitalize transform duration-300 py-2 px-3 sm:px-4 hover:bg-black-300 hover:text-white hover:border-black-300 active bg-black-300 text-white border-black-300 cursor-default">2</a>
                                        </li>
                                        <li class="inline-block mb-2 ml-1 md:ml-2">
                                            <a href="/places?type=All&amp;sort=DESC&amp;page=3" class="block font-semibold text-xs rounded-pill border border-black-200 capitalize transform duration-300 py-2 px-3 sm:px-4 hover:bg-black-300 hover:text-white hover:border-black-300">3</a>
                                        </li>
                                        <li class="inline-block mb-2 ml-1 md:ml-2">
                                            <span>…</span>
                                        </li>
                                        <li class="inline-block mb-2 ml-1 md:ml-2">
                                            <a href="/places?type=All&amp;sort=DESC&amp;page=100" class="block font-semibold text-xs rounded-pill border border-black-200 capitalize transform duration-300 py-2 px-3 sm:px-4 hover:bg-black-300 hover:text-white hover:border-black-300">100</a>
                                        </li>
                                    </ul>
                                    <div class="relative pl-2">
                                        <a href="/places?type=All&amp;sort=DESC&amp;page=3" class="p-3 font-semibold block rounded-pill border border-black-200 capitalize cursor-pointer transform duration-300 hover:bg-black-300 hover:text-white hover:border-black-300">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" viewBox="0 0 8 12" fill="currentColor" aria-hidden="true">
                                                <path d="M1.08921 0.148971C1.03905 0.106489 0.981007 0.074303 0.918404 0.0542515C0.8558 0.0341999 0.78986 0.0266754 0.724348 0.0321076C0.592041 0.0430785 0.469511 0.106159 0.383714 0.207471C0.297916 0.308784 0.255879 0.44003 0.26685 0.572337C0.277821 0.704644 0.340901 0.827174 0.442214 0.912971L6.46021 5.99997L0.442214 11.087C0.340901 11.1728 0.277821 11.2953 0.26685 11.4276C0.255879 11.5599 0.297916 11.6912 0.383714 11.7925C0.469511 11.8938 0.592041 11.9569 0.724348 11.9678C0.856655 11.9788 0.987901 11.9368 1.08921 11.851L7.55821 6.38197C7.61379 6.33504 7.65846 6.27654 7.68909 6.21056C7.71973 6.14458 7.73561 6.07272 7.73561 5.99997C7.73561 5.92723 7.71973 5.85536 7.68909 5.78938C7.65846 5.7234 7.61379 5.66491 7.55821 5.61797L1.08921 0.148971Z"></path>
                                            </svg>
                                        </a>
                                    </div>
                                </div>
                            </div>
                            <aside class="mt-10 lg:mt-0 md:flex md:flex-wrap md:justify-center lg:w-1/3 lg:block">
                                <div class="w-full sticky top-0">
                                    <div class="hidden styles_gpt-ad__BVAfH bg-white pb-3"></div>
                                </div>
                            </aside>
                        </section>
                        <div class="my-12 md:mb-20 md:mt-0">
                            <div class="hidden styles_gpt-ad__BVAfH bg-white pb-3"></div>
                        </div>
                    </div>
                </main>
            </div>
            <footer>
                <section>
                    <div class="container pt-12 border-t border-black-200 md:pb-4 lg:py-12">
                        <div class="justify-between md:flex">
                            <div class="text-center md:w-1/4 flex-0 md:text-left">
                                <a href="/" class="no-underline">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="2em" height="1em" class="mx-auto text-3xl cursor-pointer text-blue w-36 md:text-4xl md:w-56 md:mx-0" viewBox="0 0 605.88 98.66" fill="currentColor" role="img" aria-label="Lonely Planet Home">
                                        <path d="M219.05 75.21c-10.54 0-16.72-6.28-16.72-16.93V0h15.76v57.32c0 3 1.28 4.47 4.15 4.47h3.83v13.42ZM395.14 75.21c-10.54 0-16.72-6.28-16.72-16.93V0h15.76v57.32c0 3 1.28 4.47 4.15 4.47h3.83v13.42Z"></path>
                                        <path d="M278.13 21.76h-15.76v29c0 7.45-3.83 10.65-9 10.65s-8.95-3.19-8.95-10.65v-29h-15.71V51.7c0 14.7 11 23.54 23.08 23.54 3.86 0 9.54-2 10.7-5.72v2.74c0 8.52-6 12.67-12.67 12.67a16.2 16.2 0 0 1-13.95-7.14L225.33 87c6.39 9.37 16.61 11.71 24.07 11.71 17.36 0 28.75-10.22 28.75-27.37V21.76ZM27 47.52c0-17.14 12.25-29.07 28.86-29.07s29 11.93 29 29.07-12.25 29.07-29 29.07S27 64.66 27 47.52Zm41.75 0c0-9.8-5.54-15.12-12.89-15.12s-12.67 5.32-12.67 15.12 5.43 15.23 12.67 15.23 12.89-5.43 12.89-15.23ZM197.26 51.46h-37.59c1.17 8.41 6 11.82 12.89 11.82 5.43 0 9.58-1.81 12.57-6.28L196 65.84c-5.32 7.24-14 10.76-23.43 10.76-17.16 0-28.87-11.6-28.87-29.07 0-17.14 11.18-29.07 27.26-29.07 15.65 0 26.41 11.82 26.41 29.07-.01 1.16-.01 2.76-.11 3.93ZM160 41h21.62c-1.39-7.12-5.23-10.1-10.62-10.1-6 .01-9.74 3.2-11 10.1ZM573 51.46h-37.55c1.17 8.41 6 11.82 12.89 11.82 5.43 0 9.58-1.81 12.57-6.28l10.86 8.84c-5.32 7.24-13.95 10.76-23.43 10.76-17.15 0-28.86-11.61-28.86-29.07 0-17.14 11.18-29.07 27.26-29.07 15.65 0 26.41 11.82 26.41 29.07-.01 1.16-.01 2.76-.15 3.93ZM535.76 41h21.62c-1.38-7.12-5.22-10.1-10.65-10.1-5.96.01-9.73 3.2-10.97 10.1ZM403.17 47.55c0 16.45 10.93 29.16 25.13 29.16 7.8 0 12.46-3.52 15.77-8.54v7h15.7V47.55c0-23-17.32-29.16-28-29.16-18.21.01-28.6 12.72-28.6 29.16Zm16.13.11c0-9.83 5.13-15.27 12.5-15.27s12.5 5.45 12.5 15.27-5.13 15.17-12.5 15.17-12.5-5.34-12.5-15.17ZM605.88 34h-12v20.12q0 7.66 6.07 7.67h6v13.42h-8c-12.46 0-19.81-7.67-19.81-20.45V3l15.76.13v18.63h12V34ZM16.72 75.24C6.18 75.24 0 69 0 58.31V0h15.76v57.36c0 3 1.28 4.47 4.15 4.47h3.83v13.41ZM344.39 18.43c-10.69 0-28 6.2-28 29.16v45.82h15.7v-25.2c3.31 5 8 8.54 15.77 8.54C362.06 76.75 373 64 373 47.59s-10.39-29.16-28.61-29.16Zm0 44.43c-7.37 0-12.5-5.34-12.5-15.17s5.13-15.27 12.5-15.27 12.5 5.45 12.5 15.27-5.15 15.17-12.52 15.17ZM139.11 42v33.21h-15.76V42.94c0-7.45-3.83-10.65-8.95-10.65s-9 3.19-9 10.65v32.27H89.69V42c0-14.7 10.65-23.54 24.71-23.54s24.71 8.83 24.71 23.54ZM515.38 42v33.21h-15.76V42.94c0-7.45-3.83-10.65-8.95-10.65s-8.95 3.19-8.95 10.65v32.27H466V42c0-14.7 10.65-23.54 24.71-23.54s24.67 8.83 24.67 23.54Z"></path>
                                    </svg>
                                </a>
                                <p class="mt-1 mb-10 md:mt-2">For Explorers Everywhere</p>
                                <h3 class="mb-4 text-sm font-semibold uppercase">Follow us</h3>
                                <nav class="flex justify-center mx-auto lg:flex-none lg:justify-normal">
                                    <ul class="flex space-x-4 lg:space-x-2">
                                        <li data-testid="Facebook">
                                            <a target="_blank" rel="noopener noreferrer" class="no-underline" href="https://www.facebook.com/lonelyplanet">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="text-2xl" viewBox="0 0 32 32" fill="currentColor" role="img" aria-label="Lonely Planet on Facebook">
                                                    <path d="M0 16C0 24.8366 7.16344 32 16 32C24.8366 32 32 24.8366 32 16C32 7.16344 24.8366 0 16 0C7.16344 0 0 7.16344 0 16Z" fill="#1877F2"></path>
                                                    <path d="M24 16C24 11.6 20.4 8 16 8C11.6 8 8 11.6 8 16C8 20 10.9 23.3 14.7 23.9V18.3H12.7V16H14.7V14.2C14.7 12.2 15.9 11.1 17.7 11.1C18.6 11.1 19.5 11.3 19.5 11.3V13.3H18.5C17.5 13.3 17.2 13.9 17.2 14.5V16H19.4L19 18.3H17.1V24C21.1 23.4 24 20 24 16Z" fill="white"></path>
                                                </svg>
                                            </a>
                                        </li>
                                        <li data-testid="Instagram">
                                            <a target="_blank" rel="noopener noreferrer" class="no-underline" href="https://www.instagram.com/lonelyplanet">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="text-2xl" viewBox="0 0 32 32" fill="currentColor" role="img" aria-label="Lonely Planet on Instagram">
                                                    <path d="M0 16C0 24.8366 7.16344 32 16 32C24.8366 32 32 24.8366 32 16C32 7.16344 24.8366 0 16 0C7.16344 0 0 7.16344 0 16Z" fill="#F00073"></path>
                                                    <path d="M16 9.19995C18.2 9.19995 18.5 9.19995 19.4 9.19995C20.2 9.19995 20.6 9.39995 20.9 9.49995C21.3 9.69995 21.6 9.79995 21.9 10.1C22.2 10.4 22.4 10.7 22.5 11.1C22.6 11.4 22.7 11.8 22.8 12.6C22.8 13.5 22.8 13.7 22.8 16C22.8 18.3 22.8 18.5 22.8 19.4C22.8 20.2 22.6 20.6 22.5 20.9C22.3 21.3 22.2 21.6 21.9 21.9C21.6 22.2 21.3 22.4 20.9 22.5C20.6 22.6 20.2 22.7 19.4 22.8C18.5 22.8 18.3 22.8 16 22.8C13.7 22.8 13.5 22.8 12.6 22.8C11.8 22.8 11.4 22.6 11.1 22.5C10.7 22.3 10.4 22.2 10.1 21.9C9.79995 21.6 9.59995 21.3 9.49995 20.9C9.39995 20.6 9.29995 20.2 9.19995 19.4C9.19995 18.5 9.19995 18.3 9.19995 16C9.19995 13.7 9.19995 13.5 9.19995 12.6C9.19995 11.8 9.39995 11.4 9.49995 11.1C9.69995 10.7 9.79995 10.4 10.1 10.1C10.4 9.79995 10.7 9.59995 11.1 9.49995C11.4 9.39995 11.8 9.29995 12.6 9.19995C13.5 9.19995 13.8 9.19995 16 9.19995ZM16 7.69995C13.7 7.69995 13.5 7.69995 12.6 7.69995C11.7 7.69995 11.1 7.89995 10.6 8.09995C10.1 8.29995 9.59995 8.59995 9.09995 9.09995C8.59995 9.59995 8.39995 9.99995 8.09995 10.6C7.89995 11.1 7.79995 11.7 7.69995 12.6C7.69995 13.5 7.69995 13.8 7.69995 16C7.69995 18.3 7.69995 18.5 7.69995 19.4C7.69995 20.3 7.89995 20.9 8.09995 21.4C8.29995 21.9 8.59995 22.4 9.09995 22.9C9.59995 23.4 9.99995 23.6 10.6 23.9C11.1 24.1 11.7 24.1999 12.6 24.2999C13.5 24.2999 13.8 24.2999 16 24.2999C18.2 24.2999 18.5 24.2999 19.4 24.2999C20.3 24.2999 20.9 24.1 21.4 23.9C21.9 23.7 22.4 23.4 22.9 22.9C23.4 22.4 23.6 22 23.9 21.4C24.1 20.9 24.1999 20.3 24.2999 19.4C24.2999 18.5 24.2999 18.2 24.2999 16C24.2999 13.8 24.2999 13.5 24.2999 12.6C24.2999 11.7 24.1 11.1 23.9 10.6C23.7 10.1 23.4 9.59995 22.9 9.09995C22.4 8.59995 22 8.39995 21.4 8.09995C20.9 7.89995 20.3 7.79995 19.4 7.69995C18.5 7.69995 18.3 7.69995 16 7.69995Z" fill="white"></path>
                                                    <path d="M16 11.7C13.6 11.7 11.7 13.6 11.7 16C11.7 18.4 13.6 20.3 16 20.3C18.4 20.3 20.3 18.4 20.3 16C20.3 13.6 18.4 11.7 16 11.7ZM16 18.8C14.5 18.8 13.2 17.6 13.2 16C13.2 14.5 14.4 13.2 16 13.2C17.5 13.2 18.8 14.4 18.8 16C18.8 17.5 17.5 18.8 16 18.8Z" fill="white"></path>
                                                    <path d="M20.4 12.6C20.9522 12.6 21.4 12.1522 21.4 11.6C21.4 11.0477 20.9522 10.6 20.4 10.6C19.8477 10.6 19.4 11.0477 19.4 11.6C19.4 12.1522 19.8477 12.6 20.4 12.6Z" fill="white"></path>
                                                </svg>
                                            </a>
                                        </li>
                                        <li data-testid="Twitter">
                                            <a target="_blank" rel="noopener noreferrer" class="no-underline" href="https://www.twitter.com/lonelyplanet">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="text-2xl" viewBox="0 0 300 300" fill="currentColor" role="img" aria-label="Lonely Planet on Twitter">
                                                    <path d="M150 300C232.85 300 300 232.85 300 150C300 67.15 232.85 0 150 0C67.15 0 0 67.15 0 150C0 232.85 67.15 300 150 300Z" fill="#000"></path>
                                                    <path d="M57.74 63.04L129.65 158.71L57.36 236.71H73.57L136.72 168.24L187.61 236.71H243.22L167.34 135.54L234.34 63.04H218.13L159.98 125.75L113.03 63.04H57.74ZM81.73 75.03H106.93L218.86 224.68H193.66L81.73 75.03Z" fill="white"></path>
                                                </svg>
                                            </a>
                                        </li>
                                        <li data-testid="YouTube">
                                            <a target="_blank" rel="noopener noreferrer" class="no-underline" href="https://www.youtube.com/lonelyplanet">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="text-2xl" viewBox="0 0 32 32" fill="currentColor" role="img" aria-label="Lonely Planet on YouTube">
                                                    <path d="M0 16C0 24.8366 7.16344 32 16 32C24.8366 32 32 24.8366 32 16C32 7.16344 24.8366 0 16 0C7.16344 0 0 7.16344 0 16Z" fill="#FF0000"></path>
                                                    <path d="M23.6 12.1C23.4 11.4 22.9 10.9 22.2 10.7C21 10.4 15.9 10.4 15.9 10.4C15.9 10.4 10.9 10.4 9.60001 10.7C8.90001 10.9 8.4 11.4 8.2 12.1C8 13.4 8 16 8 16C8 16 8 18.6 8.3 19.9C8.5 20.6 9 21.1 9.7 21.3C10.9 21.6 16 21.6 16 21.6C16 21.6 21 21.6 22.3 21.3C23 21.1 23.5 20.6 23.7 19.9C24 18.6 24 16 24 16C24 16 24 13.4 23.6 12.1ZM14.4 18.4V13.6L18.6 16L14.4 18.4Z" fill="white"></path>
                                                </svg>
                                            </a>
                                        </li>
                                        <li data-testid="Pinterest">
                                            <a target="_blank" rel="noopener noreferrer" class="no-underline" href="https://www.pinterest.com/lonelyplanet">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="text-2xl" viewBox="0 0 32 32" fill="currentColor" role="img" aria-label="Lonely Planet on Pinterest">
                                                    <path d="M0 16C0 24.8366 7.16344 32 16 32C24.8366 32 32 24.8366 32 16C32 7.16344 24.8366 0 16 0C7.16344 0 0 7.16344 0 16Z" fill="#E60023"></path>
                                                    <path d="M16 8C11.6 8 8 11.6 8 16C8 19.3 10 22.1 12.8 23.3C12.8 22.7 12.8 22.1 12.9 21.5C13.1 20.8 13.9 17.1 13.9 17.1C13.9 17.1 13.6 16.6 13.6 15.8C13.6 14.6 14.3 13.7 15.1 13.7C15.8 13.7 16.2 14.2 16.2 14.9C16.2 15.6 15.7 16.7 15.5 17.7C15.3 18.5 15.9 19.2 16.8 19.2C18.3 19.2 19.3 17.3 19.3 14.9C19.3 13.1 18.1 11.8 16 11.8C13.6 11.8 12.1 13.6 12.1 15.6C12.1 16.3 12.3 16.8 12.6 17.2C12.7 17.4 12.8 17.4 12.7 17.6C12.7 17.7 12.6 18.1 12.5 18.2C12.4 18.4 12.3 18.5 12.1 18.4C11 17.9 10.5 16.7 10.5 15.3C10.5 13 12.4 10.3 16.2 10.3C19.3 10.3 21.3 12.5 21.3 14.9C21.3 18 19.6 20.4 17 20.4C16.1 20.4 15.3 19.9 15 19.4C15 19.4 14.5 21.2 14.4 21.6C14.2 22.2 13.9 22.8 13.6 23.3C14.3 23.5 15.1 23.6 15.9 23.6C20.3 23.6 23.9 20 23.9 15.6C24 11.6 20.4 8 16 8Z" fill="white"></path>
                                                </svg>
                                            </a>
                                        </li>
                                    </ul>
                                </nav>
                                <h3 class="mt-10 mb-4 text-sm font-semibold">SUBSCRIBE</h3>
                                <div class="">
                                    <h3 class="text-lg font-semibold text-blue">Get 20% off your first order.</h3>
                                    <form>
                                        <input id="" required="" type="email" name="" placeholder="Email address" class="rounded-pill border border-black-200 focus:border-black-300 px-8 py-3 font-light my-4 placeholder-black max-w-full h-10 w-full" value=""/>
                                        <button class="btn btn btn-primary mb-4 w-full" type="submit" data-testid="btn">Subscribe now</button>
                                        <p class="text-xs text-black-400">
                                            Subscribe to Lonely Planet newsletters and promotions.
                                            <!-- -->
                                            <span class="whitespace-nowrap">
                                                Read our
                                                <!-- -->
                                                <a href="/legal" class="underline text-blue">Privacy Policy.</a>
                                            </span>
                                        </p>
                                    </form>
                                </div>
                            </div>
                            <div class="mt-10 md:w-3/5 flex-0 md:mt-0">
                                <div class="jsx-21109142f51e79b3 md:grid md:gap-4 grid-cols-3 grid-layout grid-rows-[150px_auto]">
                                    <nav class="jsx-21109142f51e79b3 fn-0 border-t md:border-t-0 border-black-200">
                                        <button name="Open Top destinations menu" type="button" class="jsx-21109142f51e79b3 w-full flex justify-between items-center pt-5 md:pt-0 md:cursor-default md:pointer-events-none pb-5 md:pb-0">
                                            <h3 class="jsx-21109142f51e79b3 text-sm font-semibold leading-none uppercase md:text-left">Top destinations</h3>
                                            <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="md:hidden text-2xl text-black-300 transform transition-transform" viewBox="0 0 32 32" fill="currentColor" aria-hidden="true">
                                                <path d="M16 31C18.9667 31 21.8668 30.1203 24.3336 28.4721C26.8003 26.8238 28.7229 24.4812 29.8582 21.7403C30.9935 18.9994 31.2906 15.9834 30.7118 13.0737C30.133 10.1639 28.7044 7.49119 26.6066 5.3934C24.5088 3.29562 21.8361 1.86701 18.9264 1.28823C16.0166 0.709449 13.0006 1.0065 10.2597 2.14181C7.51886 3.27713 5.17618 5.19972 3.52796 7.66645C1.87973 10.1332 1 13.0333 1 16C1 19.9783 2.58035 23.7936 5.3934 26.6066C8.20644 29.4197 12.0218 31 16 31ZM16 3.00001C18.5712 3.00001 21.0846 3.76244 23.2224 5.1909C25.3603 6.61936 27.0265 8.64968 28.0104 11.0251C28.9944 13.4006 29.2518 16.0144 28.7502 18.5362C28.2486 21.0579 27.0105 23.3743 25.1924 25.1924C23.3743 27.0105 21.0579 28.2486 18.5362 28.7502C16.0144 29.2518 13.4006 28.9944 11.0251 28.0104C8.64968 27.0265 6.61935 25.3603 5.1909 23.2224C3.76244 21.0846 3 18.5712 3 16C3.00529 12.5538 4.37663 9.25029 6.81345 6.81346C9.25028 4.37663 12.5538 3.0053 16 3.00001Z"></path>
                                                <path d="M15.2804 20.1201C15.4716 20.3106 15.7305 20.4176 16.0004 20.4176C16.2703 20.4176 16.5292 20.3106 16.7204 20.1201L21.8404 14.7001C21.9341 14.6071 22.0085 14.4965 22.0593 14.3746C22.1101 14.2528 22.1362 14.1221 22.1362 13.9901C22.1362 13.858 22.1101 13.7273 22.0593 13.6055C22.0085 13.4836 21.9341 13.373 21.8404 13.2801C21.7492 13.1869 21.6403 13.1129 21.5201 13.0624C21.3999 13.0119 21.2708 12.9859 21.1404 12.9859C21.01 12.9859 20.881 13.0119 20.7608 13.0624C20.6406 13.1129 20.5317 13.1869 20.4404 13.2801L16.0004 18.0001L11.6004 13.3201C11.5129 13.2139 11.4043 13.127 11.2817 13.0648C11.159 13.0025 11.0248 12.9662 10.8875 12.9582C10.7501 12.9502 10.6126 12.9706 10.4835 13.0181C10.3544 13.0657 10.2365 13.1393 10.1372 13.2345C10.0379 13.3297 9.95926 13.4444 9.90626 13.5713C9.85327 13.6983 9.82705 13.8348 9.82923 13.9724C9.83141 14.1099 9.86194 14.2455 9.91893 14.3707C9.97592 14.496 10.0581 14.6081 10.1604 14.7001L15.2804 20.1201Z"></path>
                                            </svg>
                                        </button>
                                        <ul class="jsx-21109142f51e79b3 overflow-hidden md:max-h-full transition-all duration-300 max-h-0 max-h-0 pb-0">
                                            <li class="jsx-21109142f51e79b3">
                                                <a href="/destinations/usa/new-york-city" class="jsx-21109142f51e79b3 block mt-6 text-sm text-black-400 md:mt-2">New York City</a>
                                            </li>
                                            <li class="jsx-21109142f51e79b3">
                                                <a href="/destinations/france/paris" class="jsx-21109142f51e79b3 block mt-6 text-sm text-black-400 md:mt-2">Paris</a>
                                            </li>
                                            <li class="jsx-21109142f51e79b3">
                                                <a href="/destinations/italy" class="jsx-21109142f51e79b3 block mt-6 text-sm text-black-400 md:mt-2">Italy</a>
                                            </li>
                                            <li class="jsx-21109142f51e79b3">
                                                <a href="/destinations/costa-rica" class="jsx-21109142f51e79b3 block mt-6 text-sm text-black-400 md:mt-2">Costa Rica</a>
                                            </li>
                                            <li class="jsx-21109142f51e79b3">
                                                <a href="/destinations/japan" class="jsx-21109142f51e79b3 block mt-6 text-sm text-black-400 md:mt-2">Japan</a>
                                            </li>
                                            <li class="jsx-21109142f51e79b3">
                                                <a href="/destinations/usa" class="jsx-21109142f51e79b3 block mt-6 text-sm text-black-400 md:mt-2">USA</a>
                                            </li>
                                            <li class="jsx-21109142f51e79b3">
                                                <a href="/destinations/the-netherlands/amsterdam" class="jsx-21109142f51e79b3 block mt-6 text-sm text-black-400 md:mt-2">Amsterdam</a>
                                            </li>
                                            <li class="jsx-21109142f51e79b3">
                                                <a href="/destinations/portugal" class="jsx-21109142f51e79b3 block mt-6 text-sm text-black-400 md:mt-2">Portugal</a>
                                            </li>
                                            <li class="jsx-21109142f51e79b3">
                                                <a href="/destinations/mexico/cancun" class="jsx-21109142f51e79b3 block mt-6 text-sm text-black-400 md:mt-2">Cancún</a>
                                            </li>
                                            <li class="jsx-21109142f51e79b3">
                                                <a href="/destinations/usa/chicago" class="jsx-21109142f51e79b3 block mt-6 text-sm text-black-400 md:mt-2">Chicago</a>
                                            </li>
                                            <li class="jsx-21109142f51e79b3">
                                                <a href="/destinations/england" class="jsx-21109142f51e79b3 block mt-6 text-sm text-black-400 md:mt-2">England</a>
                                            </li>
                                            <li class="jsx-21109142f51e79b3">
                                                <a href="/destinations/japan/tokyo" class="jsx-21109142f51e79b3 block mt-6 text-sm text-black-400 md:mt-2">Tokyo</a>
                                            </li>
                                            <li class="jsx-21109142f51e79b3">
                                                <a href="/destinations/france" class="jsx-21109142f51e79b3 block mt-6 text-sm text-black-400 md:mt-2">France</a>
                                            </li>
                                            <li class="jsx-21109142f51e79b3">
                                                <a href="/destinations/thailand" class="jsx-21109142f51e79b3 block mt-6 text-sm text-black-400 md:mt-2">Thailand</a>
                                            </li>
                                            <li class="jsx-21109142f51e79b3">
                                                <a href="/destinations/ireland" class="jsx-21109142f51e79b3 block mt-6 text-sm text-black-400 md:mt-2">Ireland</a>
                                            </li>
                                            <li class="jsx-21109142f51e79b3">
                                                <a href="/destinations/italy/rome" class="jsx-21109142f51e79b3 block mt-6 text-sm text-black-400 md:mt-2">Rome</a>
                                            </li>
                                            <li class="jsx-21109142f51e79b3">
                                                <a href="/destinations/england/london" class="jsx-21109142f51e79b3 block mt-6 text-sm text-black-400 md:mt-2">London</a>
                                            </li>
                                            <li class="jsx-21109142f51e79b3">
                                                <a href="/destinations/usa/los-angeles" class="jsx-21109142f51e79b3 block mt-6 text-sm text-black-400 md:mt-2">Los Angeles</a>
                                            </li>
                                            <li class="jsx-21109142f51e79b3">
                                                <a href="/destinations/mexico" class="jsx-21109142f51e79b3 block mt-6 text-sm text-black-400 md:mt-2">Mexico</a>
                                            </li>
                                            <li class="jsx-21109142f51e79b3">
                                                <a href="/destinations/usa/san-francisco" class="jsx-21109142f51e79b3 block mt-6 text-sm text-black-400 md:mt-2">San Francisco</a>
                                            </li>
                                            <li class="jsx-21109142f51e79b3">
                                                <a href="/places" class="jsx-21109142f51e79b3 block mt-6 text-sm text-black-400 md:mt-2">Explore More Destinations</a>
                                            </li>
                                        </ul>
                                    </nav>
                                    <nav class="jsx-21109142f51e79b3 fn-1 border-t md:border-t-0 border-black-200">
                                        <button name="Open Travel Interests menu" type="button" class="jsx-21109142f51e79b3 w-full flex justify-between items-center pt-5 md:pt-0 md:cursor-default md:pointer-events-none pb-5 md:pb-0">
                                            <h3 class="jsx-21109142f51e79b3 text-sm font-semibold leading-none uppercase md:text-left">Travel Interests</h3>
                                            <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="md:hidden text-2xl text-black-300 transform transition-transform" viewBox="0 0 32 32" fill="currentColor" aria-hidden="true">
                                                <path d="M16 31C18.9667 31 21.8668 30.1203 24.3336 28.4721C26.8003 26.8238 28.7229 24.4812 29.8582 21.7403C30.9935 18.9994 31.2906 15.9834 30.7118 13.0737C30.133 10.1639 28.7044 7.49119 26.6066 5.3934C24.5088 3.29562 21.8361 1.86701 18.9264 1.28823C16.0166 0.709449 13.0006 1.0065 10.2597 2.14181C7.51886 3.27713 5.17618 5.19972 3.52796 7.66645C1.87973 10.1332 1 13.0333 1 16C1 19.9783 2.58035 23.7936 5.3934 26.6066C8.20644 29.4197 12.0218 31 16 31ZM16 3.00001C18.5712 3.00001 21.0846 3.76244 23.2224 5.1909C25.3603 6.61936 27.0265 8.64968 28.0104 11.0251C28.9944 13.4006 29.2518 16.0144 28.7502 18.5362C28.2486 21.0579 27.0105 23.3743 25.1924 25.1924C23.3743 27.0105 21.0579 28.2486 18.5362 28.7502C16.0144 29.2518 13.4006 28.9944 11.0251 28.0104C8.64968 27.0265 6.61935 25.3603 5.1909 23.2224C3.76244 21.0846 3 18.5712 3 16C3.00529 12.5538 4.37663 9.25029 6.81345 6.81346C9.25028 4.37663 12.5538 3.0053 16 3.00001Z"></path>
                                                <path d="M15.2804 20.1201C15.4716 20.3106 15.7305 20.4176 16.0004 20.4176C16.2703 20.4176 16.5292 20.3106 16.7204 20.1201L21.8404 14.7001C21.9341 14.6071 22.0085 14.4965 22.0593 14.3746C22.1101 14.2528 22.1362 14.1221 22.1362 13.9901C22.1362 13.858 22.1101 13.7273 22.0593 13.6055C22.0085 13.4836 21.9341 13.373 21.8404 13.2801C21.7492 13.1869 21.6403 13.1129 21.5201 13.0624C21.3999 13.0119 21.2708 12.9859 21.1404 12.9859C21.01 12.9859 20.881 13.0119 20.7608 13.0624C20.6406 13.1129 20.5317 13.1869 20.4404 13.2801L16.0004 18.0001L11.6004 13.3201C11.5129 13.2139 11.4043 13.127 11.2817 13.0648C11.159 13.0025 11.0248 12.9662 10.8875 12.9582C10.7501 12.9502 10.6126 12.9706 10.4835 13.0181C10.3544 13.0657 10.2365 13.1393 10.1372 13.2345C10.0379 13.3297 9.95926 13.4444 9.90626 13.5713C9.85327 13.6983 9.82705 13.8348 9.82923 13.9724C9.83141 14.1099 9.86194 14.2455 9.91893 14.3707C9.97592 14.496 10.0581 14.6081 10.1604 14.7001L15.2804 20.1201Z"></path>
                                            </svg>
                                        </button>
                                        <ul class="jsx-21109142f51e79b3 overflow-hidden md:max-h-full transition-all duration-300 max-h-0 max-h-0 pb-0">
                                            <li class="jsx-21109142f51e79b3">
                                                <a href="/articles/category/adventure-travel" class="jsx-21109142f51e79b3 block mt-6 text-sm text-black-400 md:mt-2">Adventure Travel</a>
                                            </li>
                                            <li class="jsx-21109142f51e79b3">
                                                <a href="/articles/category/art-and-culture" class="jsx-21109142f51e79b3 block mt-6 text-sm text-black-400 md:mt-2">Art and Culture</a>
                                            </li>
                                            <li class="jsx-21109142f51e79b3">
                                                <a href="/articles/category/beaches" class="jsx-21109142f51e79b3 block mt-6 text-sm text-black-400 md:mt-2">Beaches, Coasts and Islands</a>
                                            </li>
                                            <li class="jsx-21109142f51e79b3">
                                                <a href="/articles/category/family-travel" class="jsx-21109142f51e79b3 block mt-6 text-sm text-black-400 md:mt-2">Family Holidays</a>
                                            </li>
                                            <li class="jsx-21109142f51e79b3">
                                                <a href="/articles/category/festivals-and-events" class="jsx-21109142f51e79b3 block mt-6 text-sm text-black-400 md:mt-2">Festivals</a>
                                            </li>
                                            <li class="jsx-21109142f51e79b3">
                                                <a href="/articles/category/food-and-drink" class="jsx-21109142f51e79b3 block mt-6 text-sm text-black-400 md:mt-2">Food and Drink</a>
                                            </li>
                                            <li class="jsx-21109142f51e79b3">
                                                <a href="/articles/category/romance" class="jsx-21109142f51e79b3 block mt-6 text-sm text-black-400 md:mt-2">Honeymoon and Romance</a>
                                            </li>
                                            <li class="jsx-21109142f51e79b3">
                                                <a href="/articles/category/road-trips" class="jsx-21109142f51e79b3 block mt-6 text-sm text-black-400 md:mt-2">Road Trips</a>
                                            </li>
                                            <li class="jsx-21109142f51e79b3">
                                                <a href="/articles/category/sustainable-travel" class="jsx-21109142f51e79b3 block mt-6 text-sm text-black-400 md:mt-2">Sustainable Travel</a>
                                            </li>
                                            <li class="jsx-21109142f51e79b3">
                                                <a href="/articles/category/budget-travel" class="jsx-21109142f51e79b3 block mt-6 text-sm text-black-400 md:mt-2">Travel on a Budget</a>
                                            </li>
                                            <li class="jsx-21109142f51e79b3">
                                                <a href="/articles/category/wildlife-and-nature" class="jsx-21109142f51e79b3 block mt-6 text-sm text-black-400 md:mt-2">Wildlife and Nature</a>
                                            </li>
                                        </ul>
                                    </nav>
                                    <nav class="jsx-21109142f51e79b3 fn-2 border-t md:border-t-0 border-black-200">
                                        <button name="Open Shop menu" type="button" class="jsx-21109142f51e79b3 w-full flex justify-between items-center pt-5 md:pt-0 md:cursor-default md:pointer-events-none pb-5 md:pb-0">
                                            <h3 class="jsx-21109142f51e79b3 text-sm font-semibold leading-none uppercase md:text-left">Shop</h3>
                                            <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="md:hidden text-2xl text-black-300 transform transition-transform" viewBox="0 0 32 32" fill="currentColor" aria-hidden="true">
                                                <path d="M16 31C18.9667 31 21.8668 30.1203 24.3336 28.4721C26.8003 26.8238 28.7229 24.4812 29.8582 21.7403C30.9935 18.9994 31.2906 15.9834 30.7118 13.0737C30.133 10.1639 28.7044 7.49119 26.6066 5.3934C24.5088 3.29562 21.8361 1.86701 18.9264 1.28823C16.0166 0.709449 13.0006 1.0065 10.2597 2.14181C7.51886 3.27713 5.17618 5.19972 3.52796 7.66645C1.87973 10.1332 1 13.0333 1 16C1 19.9783 2.58035 23.7936 5.3934 26.6066C8.20644 29.4197 12.0218 31 16 31ZM16 3.00001C18.5712 3.00001 21.0846 3.76244 23.2224 5.1909C25.3603 6.61936 27.0265 8.64968 28.0104 11.0251C28.9944 13.4006 29.2518 16.0144 28.7502 18.5362C28.2486 21.0579 27.0105 23.3743 25.1924 25.1924C23.3743 27.0105 21.0579 28.2486 18.5362 28.7502C16.0144 29.2518 13.4006 28.9944 11.0251 28.0104C8.64968 27.0265 6.61935 25.3603 5.1909 23.2224C3.76244 21.0846 3 18.5712 3 16C3.00529 12.5538 4.37663 9.25029 6.81345 6.81346C9.25028 4.37663 12.5538 3.0053 16 3.00001Z"></path>
                                                <path d="M15.2804 20.1201C15.4716 20.3106 15.7305 20.4176 16.0004 20.4176C16.2703 20.4176 16.5292 20.3106 16.7204 20.1201L21.8404 14.7001C21.9341 14.6071 22.0085 14.4965 22.0593 14.3746C22.1101 14.2528 22.1362 14.1221 22.1362 13.9901C22.1362 13.858 22.1101 13.7273 22.0593 13.6055C22.0085 13.4836 21.9341 13.373 21.8404 13.2801C21.7492 13.1869 21.6403 13.1129 21.5201 13.0624C21.3999 13.0119 21.2708 12.9859 21.1404 12.9859C21.01 12.9859 20.881 13.0119 20.7608 13.0624C20.6406 13.1129 20.5317 13.1869 20.4404 13.2801L16.0004 18.0001L11.6004 13.3201C11.5129 13.2139 11.4043 13.127 11.2817 13.0648C11.159 13.0025 11.0248 12.9662 10.8875 12.9582C10.7501 12.9502 10.6126 12.9706 10.4835 13.0181C10.3544 13.0657 10.2365 13.1393 10.1372 13.2345C10.0379 13.3297 9.95926 13.4444 9.90626 13.5713C9.85327 13.6983 9.82705 13.8348 9.82923 13.9724C9.83141 14.1099 9.86194 14.2455 9.91893 14.3707C9.97592 14.496 10.0581 14.6081 10.1604 14.7001L15.2804 20.1201Z"></path>
                                            </svg>
                                        </button>
                                        <ul class="jsx-21109142f51e79b3 overflow-hidden md:max-h-full transition-all duration-300 max-h-0 max-h-0 pb-0">
                                            <li class="jsx-21109142f51e79b3">
                                                <a href="https://shop.lonelyplanet.com/collections/guidebooks?utm_source=lonelyplanet&amp;utm_campaign=footer&amp;utm_content=destinationguides" class="jsx-21109142f51e79b3 block mt-6 text-sm text-black-400 md:mt-2">Destination Guides</a>
                                            </li>
                                            <li class="jsx-21109142f51e79b3">
                                                <a href="https://shop.lonelyplanet.com/pages/lonely-planet-kids?utm_source=lonelyplanet&amp;utm_campaign=footer&amp;utm_content=lpkids" class="jsx-21109142f51e79b3 block mt-6 text-sm text-black-400 md:mt-2">Lonely Planet Kids</a>
                                            </li>
                                            <li class="jsx-21109142f51e79b3">
                                                <a href="https://shop.lonelyplanet.com?utm_source=lonelyplanet&amp;utm_campaign=footer&amp;utm_content=lpshop" class="jsx-21109142f51e79b3 block mt-6 text-sm text-black-400 md:mt-2">Lonely Planet Shop</a>
                                            </li>
                                            <li class="jsx-21109142f51e79b3">
                                                <a href="https://support.lonelyplanet.com/hc/en-us/articles/218157937-General-guidebook-information-?utm_source=lonelyplanet&amp;utm_campaign=footer&amp;utm_content=nonenglishguides#product3" class="jsx-21109142f51e79b3 block mt-6 text-sm text-black-400 md:mt-2">Non-English Guides</a>
                                            </li>
                                        </ul>
                                    </nav>
                                    <nav class="jsx-21109142f51e79b3 fn-3 border-t md:border-t-0 border-black-200">
                                        <button name="Open About Us menu" type="button" class="jsx-21109142f51e79b3 w-full flex justify-between items-center pt-5 md:pt-0 md:cursor-default md:pointer-events-none pb-5 md:pb-0">
                                            <h3 class="jsx-21109142f51e79b3 text-sm font-semibold leading-none uppercase md:text-left">About Us</h3>
                                            <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="md:hidden text-2xl text-black-300 transform transition-transform" viewBox="0 0 32 32" fill="currentColor" aria-hidden="true">
                                                <path d="M16 31C18.9667 31 21.8668 30.1203 24.3336 28.4721C26.8003 26.8238 28.7229 24.4812 29.8582 21.7403C30.9935 18.9994 31.2906 15.9834 30.7118 13.0737C30.133 10.1639 28.7044 7.49119 26.6066 5.3934C24.5088 3.29562 21.8361 1.86701 18.9264 1.28823C16.0166 0.709449 13.0006 1.0065 10.2597 2.14181C7.51886 3.27713 5.17618 5.19972 3.52796 7.66645C1.87973 10.1332 1 13.0333 1 16C1 19.9783 2.58035 23.7936 5.3934 26.6066C8.20644 29.4197 12.0218 31 16 31ZM16 3.00001C18.5712 3.00001 21.0846 3.76244 23.2224 5.1909C25.3603 6.61936 27.0265 8.64968 28.0104 11.0251C28.9944 13.4006 29.2518 16.0144 28.7502 18.5362C28.2486 21.0579 27.0105 23.3743 25.1924 25.1924C23.3743 27.0105 21.0579 28.2486 18.5362 28.7502C16.0144 29.2518 13.4006 28.9944 11.0251 28.0104C8.64968 27.0265 6.61935 25.3603 5.1909 23.2224C3.76244 21.0846 3 18.5712 3 16C3.00529 12.5538 4.37663 9.25029 6.81345 6.81346C9.25028 4.37663 12.5538 3.0053 16 3.00001Z"></path>
                                                <path d="M15.2804 20.1201C15.4716 20.3106 15.7305 20.4176 16.0004 20.4176C16.2703 20.4176 16.5292 20.3106 16.7204 20.1201L21.8404 14.7001C21.9341 14.6071 22.0085 14.4965 22.0593 14.3746C22.1101 14.2528 22.1362 14.1221 22.1362 13.9901C22.1362 13.858 22.1101 13.7273 22.0593 13.6055C22.0085 13.4836 21.9341 13.373 21.8404 13.2801C21.7492 13.1869 21.6403 13.1129 21.5201 13.0624C21.3999 13.0119 21.2708 12.9859 21.1404 12.9859C21.01 12.9859 20.881 13.0119 20.7608 13.0624C20.6406 13.1129 20.5317 13.1869 20.4404 13.2801L16.0004 18.0001L11.6004 13.3201C11.5129 13.2139 11.4043 13.127 11.2817 13.0648C11.159 13.0025 11.0248 12.9662 10.8875 12.9582C10.7501 12.9502 10.6126 12.9706 10.4835 13.0181C10.3544 13.0657 10.2365 13.1393 10.1372 13.2345C10.0379 13.3297 9.95926 13.4444 9.90626 13.5713C9.85327 13.6983 9.82705 13.8348 9.82923 13.9724C9.83141 14.1099 9.86194 14.2455 9.91893 14.3707C9.97592 14.496 10.0581 14.6081 10.1604 14.7001L15.2804 20.1201Z"></path>
                                            </svg>
                                        </button>
                                        <ul class="jsx-21109142f51e79b3 overflow-hidden md:max-h-full transition-all duration-300 max-h-0 max-h-0 pb-0">
                                            <li class="jsx-21109142f51e79b3">
                                                <a href="/about" class="jsx-21109142f51e79b3 block mt-6 text-sm text-black-400 md:mt-2">About Lonely Planet</a>
                                            </li>
                                            <li class="jsx-21109142f51e79b3">
                                                <a href="https://support.lonelyplanet.com/hc/en-us" class="jsx-21109142f51e79b3 block mt-6 text-sm text-black-400 md:mt-2">Contact Us</a>
                                            </li>
                                            <li class="jsx-21109142f51e79b3">
                                                <a href="/press-trade-advertising" class="jsx-21109142f51e79b3 block mt-6 text-sm text-black-400 md:mt-2">Trade and Advertising</a>
                                            </li>
                                            <li class="jsx-21109142f51e79b3">
                                                <a href="/legal/privacy-policy" class="jsx-21109142f51e79b3 block mt-6 text-sm text-black-400 md:mt-2">Privacy Policy</a>
                                            </li>
                                            <li class="jsx-21109142f51e79b3">
                                                <a href="/legal/website-terms" class="jsx-21109142f51e79b3 block mt-6 text-sm text-black-400 md:mt-2">Terms and Conditions</a>
                                            </li>
                                            <li class="jsx-21109142f51e79b3">
                                                <a href="https://www.redventures.com/careers/brands/lonely-planet" class="jsx-21109142f51e79b3 block mt-6 text-sm text-black-400 md:mt-2">Work For Us</a>
                                            </li>
                                            <li class="jsx-21109142f51e79b3">
                                                <a href="/about/contribute" class="jsx-21109142f51e79b3 block mt-6 text-sm text-black-400 md:mt-2">Write For Us</a>
                                            </li>
                                            <li class="jsx-21109142f51e79b3">
                                                <a href="/sitemaps" class="jsx-21109142f51e79b3 block mt-6 text-sm text-black-400 md:mt-2">Sitemap</a>
                                            </li>
                                            <li class="jsx-21109142f51e79b3">
                                                <a href="/consumer-health-data-privacy-policy" class="jsx-21109142f51e79b3 block mt-6 text-sm text-black-400 md:mt-2">Consumer Health Data Privacy Policy</a>
                                            </li>
                                            <li class="jsx-21109142f51e79b3">
                                                <button type="button" class="jsx-21109142f51e79b3 hidden mt-6 font-sans text-sm font-light text-left show-cookie-settings text-black-400 md:mt-2">Cookie Settings</button>
                                            </li>
                                            <li class="jsx-21109142f51e79b3">
                                                <a href="/do-not-sell-or-share-my-personal-information" target="_blank" rel="noopener noreferrer" class="jsx-21109142f51e79b3 block mt-6 text-sm text-black-400 md:mt-2">Do Not Sell or Share My Personal Information</a>
                                            </li>
                                        </ul>
                                    </nav>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
                <section class="py-8 bg-black">
                    <div class="container flex flex-col-reverse items-center justify-between md:flex-row">
                        <p class="text-sm text-white">©
                        <!-- -->
                        2025
                        <!-- -->
                        Lonely Planet, a Red Ventures company. All rights reserved. No part of this site may be reproduced without our written permission.</p>
                        <div class="inline-flex items-center w-full mb-6 md:w-96 md:mb-0">
                            <label for="language-dropdown" class="mr-6 font-semibold text-white uppercase">Language</label>
                            <div id="language-dropdown" class="relative h-auto flex-auto">
                                <div class="relative">
                                    <button class="relative flex justify-between items-center w-full rounded-lg px-4 py-2 md:px-5 md:py-4 text-left font-semibold focus:outline-none border z-10 border-white border-black-200" type="button">
                                        <p class="leading-none text-white">English</p>
                                        <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" class="text-xs ml-4 text-white" viewBox="0 0 11 6" fill="currentColor" aria-hidden="true">
                                            <path d="M5.60504 5.5L0 0H11.2101L5.60504 5.5Z"></path>
                                        </svg>
                                    </button>
                                    <ul class="absolute bottom-full w-full bg-white border border-b-0 border-black-100 rounded-tr-lg rounded-tl-lg py-2 shadow-lg-inverted hidden">
                                        <li class="mx-1 md:mx-3">
                                            <button class="w-full p-3 my-2 text-left rounded hover:bg-black-100 hover:font-semibold" type="button">Deutsch</button>
                                        </li>
                                        <li class="mx-1 md:mx-3">
                                            <button class="w-full p-3 my-2 text-left rounded hover:bg-black-100 hover:font-semibold" type="button">Français</button>
                                        </li>
                                        <li class="mx-1 md:mx-3">
                                            <button class="w-full p-3 my-2 text-left rounded hover:bg-black-100 hover:font-semibold" type="button">Español</button>
                                        </li>
                                        <li class="mx-1 md:mx-3">
                                            <button class="w-full p-3 my-2 text-left rounded hover:bg-black-100 hover:font-semibold" type="button">Italiano</button>
                                        </li>
                                        <li class="mx-1 md:mx-3">
                                            <button class="w-full p-3 my-2 text-left rounded hover:bg-black-100 hover:font-semibold" type="button">Česky</button>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            </footer>
        </div>
        <script id="__NEXT_DATA__" type="application/json">
            {
                "props": {
                    "pageProps": {
                        "breadcrumb": [
                        ],
                        "placeSlug": null,
                        "placeTitle": "",
                        "placeType": "",
                        "places": {
                            "pages": 238,
                            "page": 2,
                            "items": [
                                {
                                    "esid": "b36f6ed7-a53c-4db4-af37-d9e4c58113c9",
                                    "eyebrow": "Europe",
                                    "title": "Scandinavia",
                                    "slug": "scandinavia",
                                    "featuredImage": {
                                        "__typename": "Image",
                                        "alt": "Fjallbacka, Sweden",
                                        "caption": "Fjallbacka, Sweden",
                                        "credit": "©RolfSt/Getty Images",
                                        "height": 2333,
                                        "name": "GettyRF_483631496.jpg",
                                        "title": "GettyRF_483631496.jpg",
                                        "url": "https://lp-cms-production.imgix.net/2023-03/GettyRF_483631496.jpg",
                                        "width": 3500
                                    }
                                },
                                {
                                    "esid": "12aa0891-460c-4148-a167-bc34f0af8628",
                                    "eyebrow": "Mediterranean Europe",
                                    "title": "Spain",
                                    "slug": "spain",
                                    "featuredImage": {
                                        "__typename": "Image",
                                        "alt": "Plaza de Espana in Seville.",
                                        "caption": "Plaza de Espana in Seville.",
                                        "credit": "Getty Images",
                                        "height": 3543,
                                        "name": "GettyImages-521901580.jpeg",
                                        "title": "GettyImages-521901580.jpeg",
                                        "url": "https://lp-cms-production.imgix.net/2022-12/GettyImages-521901580.jpeg",
                                        "width": 5315
                                    }
                                },
                                {
                                    "esid": "208ddba9-9c0c-48c1-b5ee-a80de91fb6ba",
                                    "eyebrow": "Asia",
                                    "title": "India",
                                    "slug": "india",
                                    "featuredImage": {
                                        "__typename": "Image",
                                        "alt": "The Taj Mahal as viewed through the doorway of the main gate, which frames the building neatly. Crowds walk towards the white marble structure, which has a domed roof and four pillars surrounding it.",
                                        "caption": "The Taj Mahal as viewed through the doorway of the main gate, which frames the building neatly. Crowds walk towards the white marble structure, which has a domed roof and four pillars surrounding it.",
                                        "credit": "© Sylwia Bartyzel",
                                        "height": 3181,
                                        "name": "sylwia-bartyzel-eU4pipU_8HA.jpg",
                                        "title": "sylwia-bartyzel-eU4pipU_8HA.jpg",
                                        "url": "https://lp-cms-production.imgix.net/2022-12/sylwia-bartyzel-eU4pipU_8HA.jpg",
                                        "width": 4803
                                    }
                                },
                                {
                                    "esid": "d4294889-21b0-4df4-9280-f360a708f933",
                                    "eyebrow": "Western Europe",
                                    "title": "France",
                                    "slug": "france",
                                    "featuredImage": {
                                        "__typename": "Image",
                                        "alt": "Woman enjoying the lavender fields in Provence. France. Aerial view.",
                                        "caption": "Woman enjoying the lavender fields in Provence. France. Aerial view.",
                                        "credit": "CACTUS Creative Studio / Stocksy United",
                                        "height": 1060,
                                        "name": "Woman In Lavender Field",
                                        "title": "Woman In Lavender Field",
                                        "url": "https://lp-cms-production.imgix.net/2021-10/Woman%20in%20lavender%20field%20By%20CACTUS%20Creative%20Studio%20Stocksy_txp95a12c14B4D300_Medium_2725600.jpg",
                                        "width": 1887
                                    }
                                },
                                {
                                    "esid": "03513c35-05dc-4ced-991a-5d4c30691c00",
                                    "eyebrow": "Americas",
                                    "title": "Central America",
                                    "slug": "central-america",
                                    "featuredImage": {
                                        "__typename": "Image",
                                        "alt": "Costa Rica mountains and hillside was amazing.  view into valley was unbelievable. It was so much green for miles, and miles. cloud formation.",
                                        "caption": "Costa Rica mountains and hillside was amazing.  view into valley was unbelievable. It was so much green for miles, and miles. cloud formation.",
                                        "credit": "© David Thompson/Getty Images",
                                        "height": 2592,
                                        "name": "Costa Rica David Thompson GettyImages-120472128 RFC.jpg",
                                        "title": "Costa Rica David Thompson GettyImages-120472128 RFC.jpg",
                                        "url": "https://lp-cms-production.imgix.net/2023-02/Costa%20Rica%20David%20Thompson%20GettyImages-120472128%20RFC.jpg",
                                        "width": 3888
                                    }
                                },
                                {
                                    "esid": "ddd01cef-8359-45a9-a7e5-a4f5e5f53d13",
                                    "eyebrow": "Americas",
                                    "title": "Caribbean",
                                    "slug": "caribbean",
                                    "featuredImage": {
                                        "__typename": "Image",
                                        "alt": "Kayaking in the Caribbean Sea.",
                                        "caption": "Kayaking in the Caribbean Sea.",
                                        "credit": "© Antonio Busiello/Getty Images",
                                        "height": 4096,
                                        "name": "Kayaking in the Caribbean Sea Antionio Busiello GettyImages-1167011568 rfc.jpg",
                                        "title": "Kayaking in the Caribbean Sea Antionio Busiello GettyImages-1167011568 rfc.jpg",
                                        "url": "https://lp-cms-production.imgix.net/2021-10/Kayaking%20in%20the%20Caribbean%20Sea%20Antionio%20Busiello%20GettyImages-1167011568%20rfc.jpg",
                                        "width": 6144
                                    }
                                },
                                {
                                    "esid": "7d17d387-fb10-4f04-acf3-74c7100d811a",
                                    "eyebrow": "Asia",
                                    "title": "China",
                                    "slug": "china",
                                    "featuredImage": {
                                        "__typename": "Image",
                                        "alt": "Tiananmen under sky and cloud.\n*********\nancient, beijing, blue, building, china, chinese, city, communism, famous, forbidden, gate, historic, history, landmark, landscape, mao, monument, nature, ornate, palace, peoples, prc, red, sky, square, summer, temple, tiananmen, tourism, travel, white",
                                        "caption": "Tiananmen under sky and cloud.\n*********\nancient, beijing, blue, building, china, chinese, city, communism, famous, forbidden, gate, historic, history, landmark, landscape, mao, monument, nature, ornate, palace, peoples, prc, red, sky, square, summer, temple, tiananmen, tourism, travel, white",
                                        "credit": "©Haines/Shutterstock",
                                        "height": 1880,
                                        "name": "shutterstockRF_*********.jpg",
                                        "title": "shutterstockRF_*********.jpg",
                                        "url": "https://lp-cms-production.imgix.net/2023-03/shutterstockRF_*********.jpg",
                                        "width": 3123
                                    }
                                },
                                {
                                    "esid": "1945b8d8-c691-4400-bf97-16ec14d54b93",
                                    "eyebrow": "Africa",
                                    "title": "Southern Africa",
                                    "slug": "southern-africa",
                                    "featuredImage": {
                                        "__typename": "Image",
                                        "alt": "Herd of elephants walking through grassland.",
                                        "caption": "Herd of elephants walking through grassland.",
                                        "credit": "Michael Heffernan",
                                        "height": 3840,
                                        "name": "7639de4149312929192b15efb4cdfd2d8220ee244320b176aaf65f341d3bc9cb.jpg",
                                        "title": "7639de4149312929192b15efb4cdfd2d8220ee244320b176aaf65f341d3bc9cb.jpg",
                                        "url": "https://lp-cms-production.imgix.net/2019-06/7639de4149312929192b15efb4cdfd2d8220ee244320b176aaf65f341d3bc9cb.jpg",
                                        "width": 5760
                                    }},
                                    {
                                        "esid": "7640a362-7876-4bb1-8e0a-8845e9f53ce1",
                                        "eyebrow": "Great Britain",
                                        "title": "England",
                                        "slug": "england",
                                        "featuredImage": {
                                            "__typename": "Image",
                                            "alt": "The Long Walk to Windsor Castle.",
                                            "caption": "The Long Walk to Windsor Castle.",
                                            "credit": "© Chris Jackson/Getty Images",
                                            "height": 3634,
                                            "name": "The Long Walk to Windsor Castle Chris Jackson GettyImages-1223590078 RM.jpg",
                                            "title": "The Long Walk to Windsor Castle Chris Jackson GettyImages-1223590078 RM.jpg",
                                            "url": "https://lp-cms-production.imgix.net/2021-10/The%20Long%20Walk%20to%20Windsor%20Castle%20Chris%20Jackson%20GettyImages-1223590078%20RM.jpg",
                                            "width": 5451
                                        }
                                    },
                                    {
                                        "esid": "5b647b83-f674-4d56-90bb-bd2a47344f88",
                                        "eyebrow": "Western USA",
                                        "title": "California",
                                        "slug": "usa/california",
                                        "featuredImage": {
                                            "__typename": "Image",
                                            "alt": "Hiker stands amidst a field of wildflowers on the rolling Carrizo Hills",
                                            "caption": "Hiker stands amidst a field of wildflowers on the rolling Carrizo Hills",
                                            "credit": "Nathan Yan / Stocksy United",
                                            "height": 1115,
                                            "name": "Carrizo Hills Superbloom",
                                            "title": "Carrizo Hills Superbloom",
                                            "url": "https://lp-cms-production.imgix.net/2021-10/Carrizo%20Hills%20Superbloom%20By%20Nathan%20Yan%20Stocksy_txp95a12c14B4D300_Medium_1715586.jpg",
                                            "width": 1795
                                        }
                                    },
                                    {
                                        "esid": "baf93efe-ab41-4837-bc13-1c23a7796eb6",
                                        "eyebrow": "North America",
                                        "title": "Canada",
                                        "slug": "canada",
                                        "featuredImage": {
                                            "__typename": "Image",
                                            "alt": "Bow Valley Provincial Park (part of the Kananaskis Country park system) is established east of the Canadian Rockies in the arch of the valley, while the upper course of the Bow River flows through the Banff National Park. The Canmore Nordic Centre Provincial Park is located between the Banff National Park and Canmore in the Bow River Valley.",
                                            "caption": "Bow Valley Provincial Park (part of the Kananaskis Country park system) is established east of the Canadian Rockies in the arch of the valley, while the upper course of the Bow River flows through the Banff National Park. The Canmore Nordic Centre Provincial Park is located between the Banff National Park and Canmore in the Bow River Valley.",
                                            "credit": "Gavin Hellier / Stocksy United",
                                            "height": 1155,
                                            "name": "Morants Curve, Bow River, Canadian Pacific Railway, Near Lake Louise, Banff National Park, Unesco World Heritage Site, Alberta, Rocky Mountains, Canada, North America",
                                            "title": "Morants Curve, Bow River, Canadian Pacific Railway, Near Lake Louise, Banff National Park, Unesco World Heritage Site, Alberta, Rocky Mountains, Canada, North America",
                                            "url": "https://lp-cms-production.imgix.net/2021-10/Morants%20Curve%2C%20Bow%20River%2C%20Canadian%20Pacific%20Railway%2C%20near%20Lake%20Louise%2C%20Banff%20National%20Park%2C%20UNESCO%20World%20Heritage%20Site%2C%20Alberta%2C%20Rocky%20Mountains%2C%20Canada%2C%20North%20America%20Gavin%20Hellier%20Stocksy_txp95a12c14B4D300_Medium_35616.jpg",
                                            "width": 1733
                                        }
                                    },
                                    {
                                        "esid": "a0a4a3f0-8278-4e62-9471-4939b065a133",
                                        "eyebrow": "",
                                        "title": "Middle East",
                                        "slug": "middle-east",
                                        "featuredImage": {
                                            "__typename": "Image",
                                            "alt": "Herbs and spices for sale in spice suq.",
                                            "caption": "Herbs and spices for sale in spice suq.",
                                            "credit": null,
                                            "height": 2606,
                                            "name": "f071e0ebfc7ebca7c08346085152c1d924a6d8b41a753e5fb720e5aad7a0f81e.jpg",
                                            "title": "f071e0ebfc7ebca7c08346085152c1d924a6d8b41a753e5fb720e5aad7a0f81e.jpg",
                                            "url": "https://lp-cms-production.imgix.net/2019-06/f071e0ebfc7ebca7c08346085152c1d924a6d8b41a753e5fb720e5aad7a0f81e.jpg",
                                            "width": 4028
                                        }
                                    }
                                ]
                            },
                            "queryParams": {
                                "type": "All",
                                "sort": "DESC"
                            }
                        },
                        "__N_SSP": true
                    },
                    "page": "/places/[[...slug]]",
                    "query": {
                        "type": "All",
                        "sort": "DESC",
                        "page": "2"
                    },
                    "buildId": "895dd9511351f106f935bf8baadf4d8ccf502075",
                    "isFallback": false,
                    "isExperimentalCompile": false,
                    "gssp": true,
                    "scriptLoader": [
                    ]
                }</script>
        </body>
    </html>
