#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LonelyPlanet “best time to visit …” 批量抓取 + 单 URL 测试 + 缺失补抓
"""

import asyncio, csv, re, sys
from pathlib import Path
from typing import List, Dict, Optional, Set, Tuple

import aiohttp
import pandas as pd
from bs4 import BeautifulSoup
from tqdm.asyncio import tqdm

# ─── 全局可配置参数 ─────────────────────────────────────────────────────
CONFIG = {
    # === ① 批量抓取（CSV → Excel） ==========================
    "INPUT_CSV": "lonelyplanet_places.csv",
    "OUTPUT_XLSX": "lonelyplanet_besttime.xlsx",
    "START_ROW": 1,          # 1‑based；None = 从头
    "END_ROW": 5,            # 包含；None = 到文件末尾

    # === ② 单 URL 测试 =====================================
    # 填入完整 URL（如 https://www.lonelyplanet.com/articles/best-time-to-visit-spain）
    # 若留空 None，则跳过测试流程
    "TEST_URL": "https://www.lonelyplanet.com/articles/best-time-to-visit-anguilla",  # e.g. "https://www.lonelyplanet.com/articles/best-time-to-visit-spain"

    # === ③ 补抓缺失内容 ====================================
    # 指定已有 Excel：若 Content 为空或只有一行标题，则重新抓取
    # 输出写到 PATCH_OUTPUT_XLSX。若留空 None，跳过补抓流程
    "PATCH_SOURCE_XLSX": "lonelyplanet_besttime.xlsx",           # e.g. "lonelyplanet_besttime.xlsx"
    "PATCH_OUTPUT_XLSX": "lonelyplanet_besttime_v1.0.xlsx",

    # 抓取策略
    "CONCURRENCY": 8,
    "MAX_RETRIES": 3,
    "BACKOFF_BASE": 2,
}
# ───────────────────────────────────────────────────────────

HEADERS = {
    "User-Agent": (
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) "
        "AppleWebKit/537.36 (KHTML, like Gecko) "
        "Chrome/138.0.0.0 Safari/537.36"
    ),
    "Accept-Language": "en-US,en;q=0.9",
}

TITLE_TEMPLATE = "When is the best time to visit {place}?"

PROMO_RE = re.compile(
    r"(Get a book\. Get inspired\. Get exploring\.)|"
    r"(Subscribe to Lonely Planet.+)|"
    r"(Sign up for our newsletter.+)",
    re.IGNORECASE,
)

# ─── 通用工具函数 ───────────────────────────────────────────


def clean_text(text: str) -> str:
    text = PROMO_RE.sub("", text)
    text = re.sub(r"\s+\n", "\n", text)
    text = re.sub(r"\n{2,}", "\n\n", text)
    return text.strip()


def extract_article_text(html: str, place_name: str) -> str:
    """解析新版 + 旧版模板；抓不到正文则返回空串。"""
    if not html:
        return ""

    soup = BeautifulSoup(html, "lxml")

    # 删除杂项
    for tag in soup(
        ["script", "style", "nav", "header", "footer", "aside", "figure"]
    ):
        tag.decompose()
    for sel in [".gpt-ad", "astro-ad", '[id^="gpt-ad"]']:
        for t in soup.select(sel):
            t.decompose()

    blocks: List[str] = [
        div.get_text("\n", strip=True)
        for div in soup.select("div.content-block")
        if div.get_text(strip=True)
    ]

    if not blocks:
        body = soup.find(attrs={"data-testid": "article-body"})
        if body and body.get_text(strip=True):
            blocks = [body.get_text("\n", strip=True)]

    if not blocks:
        art = soup.find("article")
        if art and art.get_text(strip=True):
            blocks = [art.get_text("\n", strip=True)]

    if not blocks:
        main_tag = soup.main
        if main_tag:
            for tag in main_tag.find_all(["h1", "h2", "h3", "p"]):
                if tag.find_parent(attrs={"data-container-id": True}):
                    continue
                txt = tag.get_text(" ", strip=True)
                if txt:
                    blocks.append(txt)

    if not blocks:
        return ""

    title = TITLE_TEMPLATE.format(place=place_name)
    return clean_text(title + "\n\n" + "\n".join(blocks))


async def fetch_html(session: aiohttp.ClientSession, url: str) -> Tuple[Optional[str], Optional[str]]:
    for attempt in range(1, CONFIG["MAX_RETRIES"] + 1):
        try:
            async with session.get(url, timeout=20) as resp:
                if resp.status == 404:
                    return None, "HTTP 404"
                resp.raise_for_status()
                html = await resp.text()
                if "error-page" in html or "Page not found" in html:
                    return None, "LP error page"
                return html, None
        except Exception as e:
            if attempt == CONFIG["MAX_RETRIES"]:
                return None, f"NetErr: {e}"
            await asyncio.sleep(CONFIG["BACKOFF_BASE"] ** (attempt - 1))


# ─── ① 批量抓取 CSV → Excel ───────────────────────────────────────────

def slice_rows(rows: List[Dict[str, str]]) -> List[Dict[str, str]]:
    start = max(0, (CONFIG["START_ROW"] or 1) - 1)
    end = CONFIG["END_ROW"] if CONFIG["END_ROW"] is not None else len(rows)
    return rows[start:end]


def load_places_from_csv() -> List[Dict]:
    csv_path = Path(CONFIG["INPUT_CSV"])
    if not csv_path.exists():
        sys.exit(f"❌ CSV 不存在: {csv_path.resolve()}")

    with csv_path.open(newline="", encoding="utf-8") as f:
        reader = csv.DictReader(f)
        rows = slice_rows(list(reader))

    out = []
    for r in rows:
        slug = r["place_url"].rstrip("/").split("/")[-1]
        out.append({"Place": r["title"], "PlaceURL": r["place_url"], "Slug": slug})
    return out


def load_done_urls(xlsx_path: Path) -> Set[str]:
    if not xlsx_path.exists():
        return set()
    df = pd.read_excel(xlsx_path)
    return set(
        df.loc[df["BestTimeURL"].notna() & df["BestTimeURL"].str.len() > 0, "PlaceURL"]
    )


async def batch_crawl():
    places = load_places_from_csv()
    done_urls = load_done_urls(Path(CONFIG["OUTPUT_XLSX"]))
    todo = [p for p in places if p["PlaceURL"] not in done_urls]

    print(
        f"Batch crawl | selected: {len(places)} | done: {len(done_urls)} | to fetch: {len(todo)}"
    )

    sem = asyncio.Semaphore(CONFIG["CONCURRENCY"])
    timeout = aiohttp.ClientTimeout(total=30)
    results: List[Dict] = []

    async with aiohttp.ClientSession(headers=HEADERS, timeout=timeout) as sess:
        tasks = []
        for p in todo:
            url = f"https://www.lonelyplanet.com/articles/best-time-to-visit-{p['Slug']}"
            tasks.append(fetch_and_pack(sem, sess, p, url, results))
        for fut in tqdm(asyncio.as_completed(tasks), total=len(tasks)):
            await fut

    merge_and_save(Path(CONFIG["OUTPUT_XLSX"]), results)


async def fetch_and_pack(sem, sess, place, url, collector):
    async with sem:
        html, err = await fetch_html(sess, url)
        if html and not err:
            content = extract_article_text(html, place["Place"])
            if content:
                collector.append(
                    {
                        "Place": place["Place"],
                        "PlaceURL": place["PlaceURL"],
                        "BestTimeURL": url,
                        "Content": content,
                        "Result": "Success",
                    }
                )
                return
            err = "No content"

        # 失败路径
        collector.append(
            {
                "Place": place["Place"],
                "PlaceURL": place["PlaceURL"],
                "BestTimeURL": "" if err else url,
                "Content": "",
                "Result": f"Failed: {err}",
            }
        )


def merge_and_save(out_path: Path, new_rows: List[Dict]):
    if out_path.exists():
        df_old = pd.read_excel(out_path)
        df = pd.concat([df_old, pd.DataFrame(new_rows)], ignore_index=True)
    else:
        df = pd.DataFrame(new_rows)
    df.to_excel(out_path, index=False)
    print(f"✅ Saved to {out_path.resolve()}")


# ─── ② 单 URL 测试 ────────────────────────────────────────────────────
async def single_url():
    url = CONFIG["TEST_URL"]
    if not url:
        return
    async with aiohttp.ClientSession(headers=HEADERS) as sess:
        html, err = await fetch_html(sess, url)   # ← 拆包
        if err or not html:
            print(f"❌ 抓取失败：{err}")
            return

        place = url.rstrip("/").split("-")[-1].replace("-", " ").title()
        content = extract_article_text(html, place)
        print("------ 抓取结果 ------")
        # print(content[:1000] + ("\n..." if len(content) > 1000 else ""))
        print(content)
        print("------ 结束 ------")


# ─── ③ 补抓缺失内容 ───────────────────────────────────────────────────
async def patch_missing_content():
    src = CONFIG["PATCH_SOURCE_XLSX"]
    if not src:
        return
    src_path = Path(src)
    if not src_path.exists():
        print(f"❌ PATCH_SOURCE_XLSX 不存在: {src_path.resolve()}")
        return

    df = pd.read_excel(src_path)
    mask_need = df["Content"].isna() | df["Content"].str.fullmatch(
        r"When is the best time to visit .+\?", na=False
    )
    todo_rows = df[mask_need].to_dict(orient="records")
    if not todo_rows:
        print("补抓：所有行内容完整，无需补抓")
        return

    print(f"补抓缺失内容 | 待重试行数: {len(todo_rows)}")

    sem = asyncio.Semaphore(CONFIG["CONCURRENCY"])
    timeout = aiohttp.ClientTimeout(total=30)
    patched: List[Dict] = []

    async with aiohttp.ClientSession(headers=HEADERS, timeout=timeout) as sess:
        tasks = []
        for r in todo_rows:
            slug = r["PlaceURL"].rstrip("/").split("/")[-1]
            url = f"https://www.lonelyplanet.com/articles/best-time-to-visit-{slug}"
            tasks.append(fetch_and_pack(sem, sess, r, url, patched))
        for fut in tqdm(asyncio.as_completed(tasks), total=len(tasks)):
            await fut

    # 用 patched 替换原 df 中对应行
    patched_df = pd.DataFrame(patched)
    df_updated = df.copy()
    for _, row in patched_df.iterrows():
        idx = df_updated["PlaceURL"] == row["PlaceURL"]
        df_updated.loc[idx, ["BestTimeURL", "Content"]] = row[
            ["BestTimeURL", "Content"]
        ].values

    out_path = Path(CONFIG["PATCH_OUTPUT_XLSX"])
    df_updated.to_excel(out_path, index=False)
    print(f"✅ Patched Excel saved to {out_path.resolve()}")


# ─── 主入口 ───────────────────────────────────────────────────────────
async def main():
    # ② 单 URL 测试
    # if CONFIG["TEST_URL"]:
    #     await single_url()

    # ③ 补抓模式
    if CONFIG["PATCH_SOURCE_XLSX"]:
        await patch_missing_content()
    #
    # # ① 常规批量抓取
    # if CONFIG["INPUT_CSV"] and CONFIG["OUTPUT_XLSX"]:
    #     await batch_crawl()


if __name__ == "__main__":
    import aiohttp

    asyncio.run(main())
