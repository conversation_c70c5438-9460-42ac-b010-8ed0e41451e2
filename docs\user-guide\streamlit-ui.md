# Streamlit UI 使用指南

## 📋 概述

iICrawlerMCP Streamlit UI 是一个基于 Streamlit 框架的 Web 前端界面，为用户提供直观、易用的图形化操作界面。通过该界面，用户可以轻松执行网页爬虫任务，实时查看 Agent 思考过程，并管理执行结果。

### ✨ 核心特性

- 🤔 **实时思考过程显示** - 通过 StreamlitCallbackHandler 展示 Agent 的推理和决策过程
- 📊 **Agent 调用过程可视化** - 详细显示工具调用、参数传递和执行结果
- 📝 **任务模板系统** - 预设常用任务模板，支持参数化配置
- 📸 **智能截图展示** - 自动识别和显示任务执行过程中的截图
- ⚙️ **配置管理** - 读取和显示 .env 配置信息
- 📚 **执行历史** - 保存和查看历史执行记录
- 🔄 **独立运行** - 作为独立前端界面，默认运行在 8501 端口

## 🚀 快速开始

### 安装依赖

```bash
# 安装 Streamlit 相关依赖
pip install streamlit>=1.28.0
pip install langchain-community>=0.0.20

# 或者更新 requirements.txt
echo "streamlit>=1.28.0" >> requirements.txt
echo "langchain-community>=0.0.20" >> requirements.txt
pip install -r requirements.txt
```

### 启动界面

```bash
# 方法1: 直接启动
streamlit run src/iicrawlermcp/ui/app.py

# 方法2: 指定端口
streamlit run src/iicrawlermcp/ui/app.py --server.port 8501

# 方法3: 允许外部访问
streamlit run src/iicrawlermcp/ui/app.py --server.address 0.0.0.0
```

### 访问界面

打开浏览器访问: `http://localhost:8501`

## 🎯 用户指南

### 界面布局

#### 主标题区域
- 显示项目名称和简介
- 提供功能概述

#### 侧边栏 - 系统配置
- **配置信息显示**: 显示从 .env 文件读取的配置
  - API Key 状态（已配置/未配置）
  - API Base URL
  - 使用的模型名称
  - 浏览器模式（有头/无头）
  - 日志级别
- **配置验证**: 实时验证配置是否正确

#### 左侧列 - 任务输入
- **任务描述输入框**: 支持自然语言任务描述
- **任务模板选择**: 预设常用模板
- **高级选项**: 最大执行步数、超时时间等
- **执行按钮**: 启动任务执行

#### 右侧列 - 执行结果
- **思考过程显示**: 实时展示 Agent 推理过程
- **执行结果**: 显示任务输出和截图
- **执行历史**: 查看历史记录

### 使用流程

#### 1. 配置验证
启动界面后，首先检查侧边栏的配置信息：
- 确保 API Key 已配置
- 验证配置状态为"✅ 配置验证通过"

#### 2. 输入任务
在任务输入区域：
```
示例任务描述：
"打开google.com，搜索迪士尼乐园，点击第一个非广告的结果，然后给我一个迪士尼乐园的页面截图"
```

#### 3. 执行任务
点击"🚀 执行任务"按钮，系统将：
- 创建 CrawlerAgent 实例
- 设置 StreamlitCallbackHandler
- 开始执行任务
- 实时显示思考过程

#### 4. 查看结果
任务执行完成后：
- 查看执行结果和输出
- 下载生成的截图
- 查看详细执行日志

## 🔧 技术实现

### StreamlitCallbackHandler 实现原理

StreamlitCallbackHandler 是连接 LangChain Agent 和 Streamlit UI 的核心组件，实现了 Agent 执行过程的实时可视化。

#### 核心机制

```python
class StreamlitCallbackHandler(BaseCallbackHandler):
    """Streamlit回调处理器，用于显示Agent思考过程"""
    
    def __init__(self, container):
        self.container = container  # Streamlit 容器
        self.step_count = 0
        
    def on_llm_start(self, serialized, prompts, **kwargs):
        """LLM开始推理时触发"""
        with self.container:
            st.write("🤔 **AI正在思考...**")
    
    def on_agent_action(self, action, **kwargs):
        """Agent执行动作时触发"""
        self.step_count += 1
        with self.container:
            with st.expander(f"🔧 步骤 {self.step_count}: {action.tool}"):
                st.write(f"**工具**: {action.tool}")
                st.write(f"**输入**: {action.tool_input}")
                st.write(f"**思考过程**: {action.log}")
```

#### 回调事件流程

1. **on_llm_start**: LLM 开始推理
2. **on_agent_action**: Agent 决定执行某个工具
3. **on_tool_start**: 工具开始执行
4. **on_tool_end**: 工具执行完成
5. **on_agent_finish**: Agent 完成整个任务

#### 实时显示机制

- 使用 Streamlit 的 `container` 和 `expander` 组件
- 通过回调函数实时更新界面内容
- 支持折叠/展开详细信息

### 与现有 MCP 服务器的关系

#### 架构关系

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Streamlit UI  │    │  MCP Server     │    │  Agent System   │
│   (Port 8501)   │    │  (Port 8000)    │    │                 │
├─────────────────┤    ├─────────────────┤    ├─────────────────┤
│ - 用户界面      │    │ - HTTP/SSE API  │    │ - CrawlerAgent  │
│ - 任务输入      │    │ - 标准化接口    │    │ - BrowserAgent  │
│ - 结果显示      │    │ - 外部调用支持  │    │ - ElementAgent  │
│ - 实时监控      │    │ - 协议兼容      │    │ - 工具系统      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
        │                       │                       │
        └───────────────────────┼───────────────────────┘
                               │
                    直接调用 Agent 系统
```

#### 独立运行模式

Streamlit UI 采用独立运行模式：
- **直接集成**: 直接导入和调用 Agent 系统
- **无需 MCP**: 不依赖 MCP 服务器运行
- **配置共享**: 共享相同的 .env 配置文件
- **功能互补**: 提供不同的访问方式

#### 使用场景对比

| 特性 | Streamlit UI | MCP Server |
|------|-------------|------------|
| 用户界面 | ✅ 图形化界面 | ❌ 仅 API |
| 实时监控 | ✅ 思考过程可视化 | ❌ 无界面 |
| 外部集成 | ❌ 仅本地使用 | ✅ 标准协议 |
| 开发调试 | ✅ 便于调试 | ✅ 便于集成 |
| 部署复杂度 | 🟡 中等 | 🟢 简单 |

## 🛠️ 开发指南

### 自定义组件开发

#### 创建新的 UI 组件

1. **在 `src/iicrawlermcp/ui/components/` 下创建新组件**:

```python
# src/iicrawlermcp/ui/components/custom_component.py
import streamlit as st
from typing import Dict, Any

def render_custom_component() -> Dict[str, Any]:
    """渲染自定义组件"""
    
    st.subheader("🔧 自定义功能")
    
    # 组件逻辑
    user_input = st.text_input("输入参数")
    
    if st.button("执行自定义功能"):
        # 处理逻辑
        result = process_custom_logic(user_input)
        st.success(f"执行完成: {result}")
    
    return {"input": user_input}

def process_custom_logic(input_data: str) -> str:
    """自定义处理逻辑"""
    return f"处理结果: {input_data}"
```

2. **在主应用中集成组件**:

```python
# src/iicrawlermcp/ui/app.py
from .components.custom_component import render_custom_component

# 在适当位置添加
with st.expander("🔧 自定义功能"):
    custom_result = render_custom_component()
```

#### 扩展回调处理器

```python
class ExtendedStreamlitCallbackHandler(StreamlitCallbackHandler):
    """扩展的回调处理器"""
    
    def __init__(self, container, enable_metrics=True):
        super().__init__(container)
        self.enable_metrics = enable_metrics
        self.metrics = {
            "total_steps": 0,
            "total_time": 0,
            "tool_usage": {}
        }
    
    def on_tool_start(self, serialized, input_str, **kwargs):
        """扩展工具开始处理"""
        super().on_tool_start(serialized, input_str, **kwargs)
        
        if self.enable_metrics:
            tool_name = serialized.get('name', '未知工具')
            self.metrics["tool_usage"][tool_name] = \
                self.metrics["tool_usage"].get(tool_name, 0) + 1
            
            # 显示使用统计
            with self.container:
                st.metric("工具调用次数", 
                         self.metrics["tool_usage"][tool_name])
```

### 配置扩展

#### 添加新的配置选项

1. **在 config.py 中添加配置**:

```python
# src/iicrawlermcp/core/config.py
class Config:
    # 新增 UI 相关配置
    UI_THEME: str = os.getenv("UI_THEME", "light")
    UI_LANGUAGE: str = os.getenv("UI_LANGUAGE", "zh")
    UI_AUTO_REFRESH: bool = os.getenv("UI_AUTO_REFRESH", "true").lower() == "true"
```

2. **在 UI 中使用配置**:

```python
# src/iicrawlermcp/ui/app.py
from src.iicrawlermcp.core import config

# 应用主题配置
if config.UI_THEME == "dark":
    st.markdown("""
    <style>
    .stApp { background-color: #1e1e1e; }
    </style>
    """, unsafe_allow_html=True)
```

### 部署配置

#### Docker 部署

```dockerfile
# Dockerfile.streamlit
FROM python:3.11-slim

WORKDIR /app
COPY . .

RUN pip install -r requirements.txt
RUN playwright install chromium

EXPOSE 8501

CMD ["streamlit", "run", "src/iicrawlermcp/ui/app.py", "--server.address", "0.0.0.0"]
```

#### 生产环境配置

```bash
# 生产环境启动脚本
#!/bin/bash
export STREAMLIT_SERVER_PORT=8501
export STREAMLIT_SERVER_ADDRESS=0.0.0.0
export STREAMLIT_SERVER_HEADLESS=true

streamlit run src/iicrawlermcp/ui/app.py \
    --server.port $STREAMLIT_SERVER_PORT \
    --server.address $STREAMLIT_SERVER_ADDRESS \
    --server.headless $STREAMLIT_SERVER_HEADLESS
```

## 🔍 故障排除

### 常见问题

#### 1. 配置验证失败
**问题**: 侧边栏显示"❌ 配置验证失败"
**解决方案**:
- 检查 .env 文件是否存在
- 确认 OPENAI_API_KEY 已正确配置
- 验证 API Base URL 是否可访问

#### 2. 思考过程不显示
**问题**: 执行任务时看不到 Agent 思考过程
**解决方案**:
- 确认 StreamlitCallbackHandler 已正确设置
- 检查 Agent 是否支持回调机制
- 验证容器组件是否正确初始化

#### 3. 截图无法显示
**问题**: 任务完成后截图不显示
**解决方案**:
- 检查截图文件路径是否正确
- 确认文件权限设置
- 验证图片格式是否支持

### 性能优化

#### 1. 界面响应优化
```python
# 使用 session_state 缓存数据
if 'agent_cache' not in st.session_state:
    st.session_state.agent_cache = build_agent()

# 使用 @st.cache_data 缓存计算结果
@st.cache_data
def load_task_templates():
    return get_predefined_templates()
```

#### 2. 内存管理
```python
# 及时清理资源
def cleanup_resources():
    if hasattr(st.session_state, 'agent_cache'):
        st.session_state.agent_cache.cleanup()
        del st.session_state.agent_cache
```

## 📚 相关文档

- [快速上手指南](quick-start.md) - 基础使用说明
- [API 参考](api-reference.md) - Agent 和工具 API
- [配置指南](configuration.md) - 系统配置说明
- [故障排除](troubleshooting.md) - 常见问题解决

---

*最后更新: 2025-01-30*
