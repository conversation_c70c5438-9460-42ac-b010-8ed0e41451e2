"""
iICrawlerMCP Streamlit UI 主应用

提供图形化界面，支持：
- 任务输入和执行
- 实时思考过程显示
- Agent调用过程可视化
- 配置管理和验证
- 执行历史记录
"""

import asyncio
import sys

# Windows 平台事件循环策略修复 - 解决 NotImplementedError: _make_subprocess_transport
if sys.platform.lower().startswith("win"):
    asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())

import streamlit as st
import os
from datetime import datetime
from typing import Dict, Any, Optional
import traceback

# 添加项目根目录到 Python 路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), "../../.."))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

try:
    from src.iicrawlermcp.agents import build_agent
    from src.iicrawlermcp.core.config import Config
    from src.iicrawlermcp.ui.utils.callbacks import StreamlitCallbackHandler
    from src.iicrawlermcp.ui.utils.helpers import (
        validate_config, 
        load_task_templates,
        save_execution_history,
        load_execution_history
    )
except ImportError as e:
    st.error(f"导入模块失败: {e}")
    st.error("请确保已正确安装项目依赖")
    st.stop()

# 页面配置
st.set_page_config(
    page_title="iICrawlerMCP - 智能网页爬虫",
    page_icon="🕷️",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 自定义CSS样式
st.markdown("""
<style>
.main-header {
    font-size: 2.5rem;
    font-weight: bold;
    color: #1f77b4;
    text-align: center;
    margin-bottom: 1rem;
}
.sub-header {
    font-size: 1.2rem;
    color: #666;
    text-align: center;
    margin-bottom: 2rem;
}
.status-success {
    color: #28a745;
    font-weight: bold;
}
.status-error {
    color: #dc3545;
    font-weight: bold;
}
.thinking-container {
    border: 1px solid #ddd;
    border-radius: 5px;
    padding: 1rem;
    margin: 1rem 0;
    background-color: #f8f9fa;
}
</style>
""", unsafe_allow_html=True)

def render_header():
    """渲染页面头部"""
    st.markdown('<h1 class="main-header">🕷️ iICrawlerMCP</h1>', unsafe_allow_html=True)
    st.markdown('<p class="sub-header">智能网页爬虫系统 - 基于LangGraph和Playwright</p>', unsafe_allow_html=True)
    
    # 功能特性展示
    col1, col2, col3, col4 = st.columns(4)
    with col1:
        st.info("🤔 **实时思考过程**\n显示Agent推理过程")
    with col2:
        st.info("📊 **工具调用可视化**\n展示工具执行详情")
    with col3:
        st.info("📸 **智能截图展示**\n自动显示执行结果")
    with col4:
        st.info("⚙️ **配置管理**\n读取.env配置信息")

def render_sidebar():
    """渲染侧边栏 - 系统配置"""
    st.sidebar.header("🔧 系统配置")
    
    # 配置验证
    config_status = validate_config()
    
    if config_status["valid"]:
        st.sidebar.markdown('<p class="status-success">✅ 配置验证通过</p>', unsafe_allow_html=True)
    else:
        st.sidebar.markdown('<p class="status-error">❌ 配置验证失败</p>', unsafe_allow_html=True)
        st.sidebar.error(config_status["error"])
    
    # 显示配置信息
    st.sidebar.subheader("📋 配置信息")
    
    # API Key 状态
    api_key = getattr(Config, 'OPENAI_API_KEY', '')
    if api_key:
        st.sidebar.text(f"🔑 API Key: {'*' * 20}...{api_key[-4:]}")
    else:
        st.sidebar.text("🔑 API Key: 未配置")
    
    # API Base URL
    api_base = getattr(Config, 'OPENAI_API_BASE', '默认')
    st.sidebar.text(f"🌐 API Base: {api_base}")
    
    # 模型名称
    model = getattr(Config, 'OPENAI_MODEL', 'gpt-3.5-turbo')
    st.sidebar.text(f"🤖 模型: {model}")
    
    # 浏览器模式
    headless = getattr(Config, 'HEADLESS', True)
    mode = "无头模式" if headless else "有头模式"
    st.sidebar.text(f"🌐 浏览器: {mode}")
    
    # 日志级别
    log_level = getattr(Config, 'LOG_LEVEL', 'INFO')
    st.sidebar.text(f"📝 日志级别: {log_level}")
    
    return config_status["valid"]

def render_task_input():
    """渲染任务输入区域"""
    st.subheader("📝 任务输入")
    
    # 任务模板选择
    templates = load_task_templates()
    template_names = ["自定义任务"] + list(templates.keys())
    selected_template = st.selectbox("选择任务模板", template_names)
    
    # 任务描述输入
    if selected_template == "自定义任务":
        default_task = "打开trip.com，拿到深圳精选酒店的全部信息，用列表形式返回"
    else:
        default_task = templates[selected_template]["description"]
    
    task_description = st.text_area(
        "任务描述",
        value=default_task,
        height=100,
        help="使用自然语言描述您想要执行的网页操作任务"
    )
    
    # 高级选项
    with st.expander("🔧 高级选项"):
        col1, col2 = st.columns(2)
        with col1:
            max_iterations = st.number_input("最大执行步数", min_value=1, max_value=100, value=50)
        with col2:
            timeout = st.number_input("超时时间(秒)", min_value=30, max_value=600, value=300)
        
        verbose = st.checkbox("详细输出", value=True, help="显示详细的执行过程")
    
    return {
        "task_description": task_description,
        "max_iterations": max_iterations,
        "timeout": timeout,
        "verbose": verbose,
        "template": selected_template
    }

def render_execution_area(task_config: Dict[str, Any]):
    """渲染执行区域"""
    st.subheader("🚀 任务执行")
    
    # 执行按钮
    if st.button("🚀 执行任务", type="primary", disabled=not task_config["task_description"].strip()):
        execute_task(task_config)

def execute_task(task_config: Dict[str, Any]):
    """执行任务"""
    task_description = task_config["task_description"]

    # 清理旧截图 - 确保只显示当前任务的截图
    screenshots_dir = "screenshots"
    if os.path.exists(screenshots_dir):
        for file in os.listdir(screenshots_dir):
            if file.endswith(('.png', '.jpg', '.jpeg')):
                try:
                    os.remove(os.path.join(screenshots_dir, file))
                except Exception:
                    pass  # 忽略删除失败的文件

    # 创建思考过程显示容器
    thinking_container = st.container()
    result_container = st.container()
    
    with thinking_container:
        st.markdown('<div class="thinking-container">', unsafe_allow_html=True)
        st.subheader("🤔 Agent 思考过程")
        thinking_placeholder = st.empty()
        st.markdown('</div>', unsafe_allow_html=True)
    
    try:
        # 显示开始执行
        with thinking_placeholder.container():
            st.info("🚀 开始执行任务...")
            st.write(f"**任务描述**: {task_description}")
        
        # 创建Agent（会自动读取.env配置）
        with st.spinner("正在初始化Agent..."):
            agent = build_agent()
        
        # 创建StreamlitCallbackHandler
        st_callback = StreamlitCallbackHandler(thinking_placeholder)

        # 执行任务
        with st.spinner("正在执行任务..."):
            start_time = datetime.now()

            # 使用executor.invoke()和callback
            executor = agent._create_executor()
            result = executor.invoke(
                {"input": task_description},
                {"callbacks": [st_callback]}
            )
            
            end_time = datetime.now()
            execution_time = (end_time - start_time).total_seconds()
        
        # 显示执行结果
        with result_container:
            st.subheader("✅ 执行结果")
            
            # 基本信息
            col1, col2, col3 = st.columns(3)
            with col1:
                st.metric("执行时间", f"{execution_time:.2f}秒")
            with col2:
                st.metric("执行状态", "成功")
            with col3:
                # 从callback获取步骤数
                step_count = getattr(st_callback, 'step_count', 0)
                st.metric("步骤数", step_count)
            
            # 输出结果
            if "output" in result:
                st.text_area("输出内容", result["output"], height=200)
            
            # 显示截图（如果有）- 修复截图显示逻辑
            screenshots_dir = "screenshots"
            if os.path.exists(screenshots_dir):
                screenshot_files = [f for f in os.listdir(screenshots_dir) if f.endswith(('.png', '.jpg', '.jpeg'))]
                if screenshot_files:
                    st.subheader("📸 生成的截图")

                    # 按修改时间排序，获取最新的截图
                    screenshot_files.sort(
                        key=lambda x: os.path.getmtime(os.path.join(screenshots_dir, x)),
                        reverse=True
                    )

                    # 显示最新的3张截图
                    for i, screenshot_file in enumerate(screenshot_files[:3]):
                        screenshot_path = os.path.join(screenshots_dir, screenshot_file)

                        # 获取文件修改时间作为标识
                        file_time = datetime.fromtimestamp(os.path.getmtime(screenshot_path))

                        # 显示截图（移除不支持的key参数）
                        st.image(
                            screenshot_path,
                            caption=f"截图 {i+1}: {screenshot_file} ({file_time.strftime('%H:%M:%S')})",
                            use_container_width=True
                        )

                        # 为第一张截图提供下载链接
                        if i == 0:
                            with open(screenshot_path, "rb") as file:
                                st.download_button(
                                    label="📥 下载最新截图",
                                    data=file.read(),
                                    file_name=screenshot_file,
                                    mime="image/png"
                                )
        
        # 保存执行历史
        step_count = getattr(st_callback, 'step_count', 0)
        save_execution_history({
            "timestamp": start_time.isoformat(),
            "task_description": task_description,
            "execution_time": execution_time,
            "status": "success",
            "result": result.get("output", ""),
            "step_count": step_count
        })
        
        st.success(f"✅ 任务执行完成！耗时 {execution_time:.2f} 秒")
        
    except Exception as e:
        error_msg = str(e)
        st.error(f"❌ 任务执行失败: {error_msg}")
        
        # 显示详细错误信息
        with st.expander("🔍 详细错误信息"):
            st.code(traceback.format_exc())
        
        # 保存错误历史
        save_execution_history({
            "timestamp": datetime.now().isoformat(),
            "task_description": task_description,
            "status": "error",
            "error": error_msg
        })

def render_history():
    """渲染执行历史"""
    st.subheader("📚 执行历史")
    
    history = load_execution_history()
    if not history:
        st.info("暂无执行历史")
        return
    
    # 显示最近的执行记录
    for i, record in enumerate(history[-10:]):  # 显示最近10条
        with st.expander(f"📋 {record['timestamp']} - {record['status']}"):
            st.write(f"**任务**: {record['task_description']}")
            if record['status'] == 'success':
                st.write(f"**执行时间**: {record.get('execution_time', 'N/A')}秒")
                st.write(f"**步骤数**: {record.get('step_count', 'N/A')}")
                if record.get('result'):
                    st.write(f"**结果**: {record['result'][:200]}...")
            else:
                st.error(f"**错误**: {record.get('error', 'Unknown error')}")

def main():
    """主函数"""
    # 渲染页面头部
    render_header()
    
    # 渲染侧边栏
    config_valid = render_sidebar()
    
    if not config_valid:
        st.error("⚠️ 请先配置正确的系统参数才能使用")
        st.info("请检查 .env 文件中的配置项")
        return
    
    # 主要内容区域
    col1, col2 = st.columns([1, 1])
    
    with col1:
        # 任务输入
        task_config = render_task_input()
        
        # 执行区域
        render_execution_area(task_config)
    
    with col2:
        # 执行历史
        render_history()

if __name__ == "__main__":
    main()
