"""
Core module for iICrawlerMCP.

This module contains the core infrastructure components including
browser control, configuration management, intelligent optimization
triggers, and monitoring systems.
"""

from .browser import Browser, get_global_browser, close_global_browser
from .config import Config, config
from .handoff_engine import HandoffEngine, HandoffResponse
from .optimization_trigger import (
    SmartOptimizationTrigger,
    FailurePatternDetector,
    ComplexityDetector,
    ContextAwareTrigger
)
from .optimization_monitor import OptimizationEffectivenessMonitor

__all__ = [
    'Browser',
    'get_global_browser',
    'close_global_browser',
    'Config',
    'config',
    'HandoffEngine',
    'HandoffResponse',
    'SmartOptimizationTrigger',
    'FailurePatternDetector',
    'ComplexityDetector',
    'ContextAwareTrigger',
    'OptimizationEffectivenessMonitor'
]
