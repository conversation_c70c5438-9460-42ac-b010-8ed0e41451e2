"""
智能优化触发机制

提供多层次的提示词优化触发条件检测，包括失败模式检测、
复杂度分析、上下文感知等智能触发策略。
"""

import logging
from typing import Dict, List, Tuple, Any
from datetime import datetime, timedelta
import re

logger = logging.getLogger(__name__)


class FailurePatternDetector:
    """
    失败模式检测器
    
    检测任务执行中的各种失败模式，为优化触发提供依据。
    """
    
    def __init__(self):
        self.failure_patterns = {
            "execution_failure": [
                "找不到元素", "操作失败", "超时", "错误", "异常", "失败", "无法执行"
            ],
            "ambiguous_output": [
                "不确定", "可能", "也许", "大概", "似乎", "不清楚", "模糊"
            ],
            "incomplete_task": [
                "部分完成", "未完全", "需要更多信息", "无法确定", "不完整", "缺少"
            ],
            "contradiction": [
                "矛盾", "冲突", "不一致", "相反", "互相矛盾", "自相矛盾"
            ]
        }
    
    def detect_failure(self, task_result: str, execution_log: str = "") -> Dict[str, float]:
        """
        检测失败模式并返回置信度
        
        Args:
            task_result: 任务执行结果
            execution_log: 执行日志
            
        Returns:
            各种失败模式的置信度字典
        """
        failure_scores = {}
        
        for pattern_type, keywords in self.failure_patterns.items():
            score = 0
            total_text = f"{task_result} {execution_log}".lower()
            
            for keyword in keywords:
                if keyword in total_text:
                    score += 1
            
            # 计算置信度（0-1之间）
            failure_scores[pattern_type] = min(score / len(keywords), 1.0)
        
        return failure_scores
    
    def should_optimize(self, failure_scores: Dict[str, float], threshold: float = 0.3) -> bool:
        """
        判断是否需要优化
        
        Args:
            failure_scores: 失败模式置信度
            threshold: 触发阈值
            
        Returns:
            是否需要优化
        """
        # 任何失败模式超过阈值就触发优化
        return any(score > threshold for score in failure_scores.values())


class ComplexityDetector:
    """
    任务复杂度检测器
    
    分析用户输入的复杂度，为优化触发提供依据。
    """
    
    def analyze_complexity(self, user_input: str) -> Dict[str, float]:
        """
        分析用户输入的复杂度
        
        Args:
            user_input: 用户输入文本
            
        Returns:
            复杂度分析结果
        """
        complexity_indicators = {
            "length": min(len(user_input.split()) / 50, 1.0),  # 标准化长度
            "ambiguity": self._count_ambiguous_words(user_input),
            "multi_step": self._count_step_indicators(user_input),
            "technical_terms": self._count_technical_terms(user_input),
            "conditional_logic": self._count_conditionals(user_input)
        }
        
        overall_complexity = sum(complexity_indicators.values()) / len(complexity_indicators)
        
        return {
            **complexity_indicators,
            "overall": overall_complexity,
            "needs_optimization": overall_complexity > 0.6
        }
    
    def _count_ambiguous_words(self, text: str) -> float:
        """计算模糊词汇比例"""
        ambiguous_words = ["一些", "几个", "大概", "差不多", "随便", "什么的", "帮我", "搞一下"]
        count = sum(1 for word in ambiguous_words if word in text)
        return min(count / 3, 1.0)  # 最多3个模糊词就算高复杂度
    
    def _count_step_indicators(self, text: str) -> float:
        """计算多步骤指示词比例"""
        step_indicators = ["然后", "接着", "之后", "同时", "并且", "以及", "还要", "再"]
        count = sum(1 for indicator in step_indicators if indicator in text)
        return min(count / 5, 1.0)
    
    def _count_technical_terms(self, text: str) -> float:
        """计算技术术语比例"""
        technical_terms = ["API", "JSON", "CSS", "XPath", "DOM", "HTTP", "URL", "HTML"]
        count = sum(1 for term in technical_terms if term.lower() in text.lower())
        return min(count / 3, 1.0)
    
    def _count_conditionals(self, text: str) -> float:
        """计算条件逻辑比例"""
        conditionals = ["如果", "假如", "当", "要是", "倘若", "除非", "若是"]
        count = sum(1 for cond in conditionals if cond in text)
        return min(count / 2, 1.0)


class ContextAwareTrigger:
    """
    上下文感知触发器
    
    基于对话历史和上下文信息决定是否触发优化。
    """
    
    def __init__(self):
        self.conversation_history = []
        self.failure_count = 0
        self.last_optimization_time = None
        self.optimization_count = 0
    
    def should_trigger_optimization(self, user_input: str, context: Dict) -> Tuple[bool, List[str]]:
        """
        基于上下文决定是否触发优化
        
        Args:
            user_input: 用户输入
            context: 上下文信息
            
        Returns:
            (是否触发, 触发原因列表)
        """
        triggers = []
        
        # 1. 连续失败触发
        if self.failure_count >= 2:
            triggers.append("consecutive_failures")
        
        # 2. 用户表达不满
        dissatisfaction_keywords = ["不对", "错了", "不是这样", "重新", "再试", "不行", "有问题"]
        if any(keyword in user_input for keyword in dissatisfaction_keywords):
            triggers.append("user_dissatisfaction")
        
        # 3. 任务类型变化
        if self._task_type_changed(user_input):
            triggers.append("task_type_change")
        
        # 4. 时间间隔触发（避免过度优化）
        if self._should_periodic_optimize():
            triggers.append("periodic_optimization")
        
        # 5. 首次复杂任务
        if not self.conversation_history and len(user_input.split()) > 20:
            triggers.append("first_complex_task")
        
        # 6. 重复类似请求
        if self._is_repetitive_request(user_input):
            triggers.append("repetitive_request")
        
        return len(triggers) > 0, triggers
    
    def _task_type_changed(self, user_input: str) -> bool:
        """检测任务类型是否发生变化"""
        if not self.conversation_history:
            return False
        
        current_type = self._extract_task_type(user_input)
        last_type = self._extract_task_type(self.conversation_history[-1])
        
        return current_type != last_type
    
    def _extract_task_type(self, text: str) -> str:
        """提取任务类型"""
        if any(word in text for word in ["爬取", "抓取", "提取", "采集"]):
            return "crawling"
        elif any(word in text for word in ["点击", "输入", "选择", "操作"]):
            return "interaction"
        elif any(word in text for word in ["导航", "打开", "访问", "跳转"]):
            return "navigation"
        elif any(word in text for word in ["截图", "保存", "下载", "导出"]):
            return "capture"
        else:
            return "general"
    
    def _should_periodic_optimize(self) -> bool:
        """检查是否应该进行周期性优化"""
        if self.last_optimization_time is None:
            return False
        
        # 每10次对话或30分钟进行一次优化
        time_threshold = datetime.now() - timedelta(minutes=30)
        conversation_threshold = len(self.conversation_history) % 10 == 0
        
        return (self.last_optimization_time < time_threshold or 
                conversation_threshold)
    
    def _is_repetitive_request(self, user_input: str) -> bool:
        """检测是否为重复类似请求"""
        if len(self.conversation_history) < 2:
            return False
        
        # 检查最近3次请求的相似度
        recent_requests = self.conversation_history[-3:]
        similarity_count = 0
        
        for request in recent_requests:
            if self._calculate_similarity(user_input, request) > 0.7:
                similarity_count += 1
        
        return similarity_count >= 2
    
    def _calculate_similarity(self, text1: str, text2: str) -> float:
        """计算文本相似度（简单实现）"""
        words1 = set(text1.lower().split())
        words2 = set(text2.lower().split())
        
        if not words1 or not words2:
            return 0.0
        
        intersection = words1.intersection(words2)
        union = words1.union(words2)
        
        return len(intersection) / len(union)
    
    def update_context(self, user_input: str, result: str, success: bool):
        """
        更新上下文信息
        
        Args:
            user_input: 用户输入
            result: 执行结果
            success: 是否成功
        """
        self.conversation_history.append(user_input)
        
        if not success:
            self.failure_count += 1
        else:
            self.failure_count = 0  # 重置失败计数
        
        # 保持历史记录在合理范围内
        if len(self.conversation_history) > 50:
            self.conversation_history = self.conversation_history[-25:]
    
    def mark_optimization(self):
        """标记进行了优化"""
        self.last_optimization_time = datetime.now()
        self.optimization_count += 1


class SmartOptimizationTrigger:
    """
    智能优化触发器 - 综合所有检测机制

    整合失败检测、复杂度分析、上下文感知等多种触发机制，
    提供智能的优化决策。
    """

    def __init__(self):
        self.failure_detector = FailurePatternDetector()
        self.complexity_detector = ComplexityDetector()
        self.context_trigger = ContextAwareTrigger()

    def should_optimize(self, user_input: str, task_result: str = "",
                       context: Dict = None) -> Tuple[bool, List[str], str]:
        """
        综合判断是否需要优化

        Args:
            user_input: 用户输入
            task_result: 任务执行结果
            context: 上下文信息

        Returns:
            (should_optimize, reasons, optimization_type)
        """
        if context is None:
            context = {}

        optimization_reasons = []
        optimization_type = "auto"

        # 1. 失败检测
        if task_result:
            failure_scores = self.failure_detector.detect_failure(
                task_result, context.get("execution_log", "")
            )
            if self.failure_detector.should_optimize(failure_scores):
                optimization_reasons.append("failure_detected")
                optimization_type = "failure_recovery"

        # 2. 复杂度检测
        complexity = self.complexity_detector.analyze_complexity(user_input)
        if complexity["needs_optimization"]:
            optimization_reasons.append("high_complexity")
            if optimization_type == "auto":
                optimization_type = "complexity_reduction"

        # 3. 上下文触发
        context_trigger, context_reasons = self.context_trigger.should_trigger_optimization(
            user_input, context
        )
        if context_trigger:
            optimization_reasons.extend(context_reasons)
            if optimization_type == "auto":
                optimization_type = "context_adaptive"

        should_optimize = len(optimization_reasons) > 0

        return should_optimize, optimization_reasons, optimization_type

    def update_context(self, user_input: str, result: str, success: bool):
        """更新上下文信息"""
        self.context_trigger.update_context(user_input, result, success)

    def mark_optimization(self):
        """标记进行了优化"""
        self.context_trigger.mark_optimization()

    def get_stats(self) -> Dict[str, Any]:
        """获取触发器统计信息"""
        return {
            "conversation_count": len(self.context_trigger.conversation_history),
            "failure_count": self.context_trigger.failure_count,
            "optimization_count": self.context_trigger.optimization_count,
            "last_optimization": self.context_trigger.last_optimization_time.isoformat()
                                if self.context_trigger.last_optimization_time else None
        }
