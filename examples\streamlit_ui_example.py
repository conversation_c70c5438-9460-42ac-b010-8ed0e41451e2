#!/usr/bin/env python3
"""
iICrawlerMCP Streamlit UI 使用示例

演示如何使用 Streamlit UI 的各种功能
"""

import os
import sys
import time
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def demo_callback_handler():
    """演示 StreamlitCallbackHandler 的使用"""
    print("=== StreamlitCallbackHandler 演示 ===")
    
    try:
        from src.iicrawlermcp.ui.utils.callbacks import StreamlitCallbackHandler
        from src.iicrawlermcp.agents import build_agent
        
        # 创建一个模拟的容器（在实际使用中这会是 Streamlit 容器）
        class MockContainer:
            def container(self):
                return self
            
            def write(self, text):
                print(f"[UI] {text}")
            
            def info(self, text):
                print(f"[INFO] {text}")
            
            def success(self, text):
                print(f"[SUCCESS] {text}")
            
            def error(self, text):
                print(f"[ERROR] {text}")
            
            def expander(self, title, expanded=False):
                print(f"[EXPANDER] {title}")
                return self
            
            def text_area(self, label, value="", height=100, key=None):
                print(f"[TEXT_AREA] {label}: {value[:100]}...")
            
            def json(self, data):
                print(f"[JSON] {data}")
            
            def code(self, text):
                print(f"[CODE] {text}")
        
        mock_container = MockContainer()
        callback = StreamlitCallbackHandler(mock_container)
        
        print("✅ StreamlitCallbackHandler 创建成功")
        print("📝 在实际使用中，这个回调处理器会显示 Agent 的思考过程")
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        print("请确保已安装所有依赖")


def demo_config_validation():
    """演示配置验证功能"""
    print("\n=== 配置验证演示 ===")
    
    try:
        from src.iicrawlermcp.ui.utils.helpers import validate_config
        
        result = validate_config()
        
        if result["valid"]:
            print("✅ 配置验证通过")
            print("📋 配置信息:")
            for key, value in result.get("config", {}).items():
                if "KEY" in key:
                    # 隐藏敏感信息
                    masked_value = f"{'*' * 20}...{value[-4:]}" if value else "未配置"
                    print(f"  {key}: {masked_value}")
                else:
                    print(f"  {key}: {value}")
        else:
            print("❌ 配置验证失败")
            print(f"错误: {result['error']}")
            
    except ImportError as e:
        print(f"❌ 导入失败: {e}")


def demo_task_templates():
    """演示任务模板功能"""
    print("\n=== 任务模板演示 ===")
    
    try:
        from src.iicrawlermcp.ui.utils.helpers import load_task_templates, save_task_template
        
        # 加载模板
        templates = load_task_templates()
        print(f"📚 加载了 {len(templates)} 个任务模板:")
        
        for name, template in templates.items():
            print(f"  📝 {name}: {template['description'][:50]}...")
            print(f"     分类: {template.get('category', '未分类')}, 难度: {template.get('difficulty', '未知')}")
        
        # 演示保存新模板
        new_template = {
            "description": "这是一个演示模板，用于测试模板保存功能",
            "category": "测试",
            "difficulty": "简单"
        }
        
        if save_task_template("演示模板", new_template):
            print("✅ 新模板保存成功")
        else:
            print("❌ 新模板保存失败")
            
    except ImportError as e:
        print(f"❌ 导入失败: {e}")


def demo_execution_history():
    """演示执行历史功能"""
    print("\n=== 执行历史演示 ===")
    
    try:
        from src.iicrawlermcp.ui.utils.helpers import (
            save_execution_history, 
            load_execution_history,
            format_execution_time
        )
        from datetime import datetime
        
        # 创建示例执行记录
        sample_record = {
            "timestamp": datetime.now().isoformat(),
            "task_description": "演示任务：访问示例网站并截图",
            "execution_time": 15.67,
            "status": "success",
            "result": "任务执行成功，已保存截图到 screenshots/demo.png",
            "step_count": 3
        }
        
        # 保存记录
        if save_execution_history(sample_record):
            print("✅ 执行记录保存成功")
        
        # 加载历史
        history = load_execution_history()
        print(f"📚 加载了 {len(history)} 条执行历史:")
        
        for i, record in enumerate(history[-3:]):  # 显示最近3条
            print(f"  📋 记录 {i+1}:")
            print(f"     时间: {record['timestamp']}")
            print(f"     任务: {record['task_description'][:50]}...")
            print(f"     状态: {record['status']}")
            if record['status'] == 'success':
                exec_time = format_execution_time(record.get('execution_time', 0))
                print(f"     耗时: {exec_time}")
                print(f"     步骤: {record.get('step_count', 'N/A')}")
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")


def demo_system_info():
    """演示系统信息功能"""
    print("\n=== 系统信息演示 ===")
    
    try:
        from src.iicrawlermcp.ui.utils.helpers import get_system_info
        
        info = get_system_info()
        
        if info:
            print("💻 系统信息:")
            print(f"  操作系统: {info.get('platform', 'Unknown')}")
            print(f"  Python 版本: {info.get('python_version', 'Unknown')}")
            print(f"  CPU 核心数: {info.get('cpu_count', 'Unknown')}")
            print(f"  内存总量: {info.get('memory_total', 'Unknown')} GB")
            print(f"  磁盘可用: {info.get('disk_free', 'Unknown')} GB")
        else:
            print("❌ 无法获取系统信息")
            
    except ImportError as e:
        print(f"❌ 导入失败: {e}")


def main():
    """主函数"""
    print("🕷️ iICrawlerMCP Streamlit UI 功能演示")
    print("=" * 50)
    
    # 检查项目结构
    ui_app_path = project_root / "src" / "iicrawlermcp" / "ui" / "app.py"
    if ui_app_path.exists():
        print("✅ 找到 Streamlit UI 应用文件")
    else:
        print("❌ 未找到 Streamlit UI 应用文件")
        print(f"预期路径: {ui_app_path}")
    
    # 检查配置文件
    env_path = project_root / ".env"
    if env_path.exists():
        print("✅ 找到配置文件 .env")
    else:
        print("⚠️  未找到配置文件 .env")
    
    # 运行各种演示
    demo_callback_handler()
    demo_config_validation()
    demo_task_templates()
    demo_execution_history()
    demo_system_info()
    
    print("\n" + "=" * 50)
    print("🚀 演示完成！")
    print("\n要启动 Streamlit UI，请运行:")
    print("  python run_streamlit.py")
    print("或者:")
    print("  streamlit run src/iicrawlermcp/ui/app.py")


if __name__ == "__main__":
    main()
