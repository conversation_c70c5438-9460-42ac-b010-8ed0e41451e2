"""
Delegation tools for CrawlerAgent to delegate tasks to specialized agents.

This module provides LangChain tools that allow CrawlerAgent to intelligently
delegate tasks to BrowserAgent and ElementAgent when needed.
"""

import logging
from langchain_core.tools import tool

logger = logging.getLogger(__name__)








@tool
def smart_element_finder(element_description: str) -> str:
    """
    Smart element finder with precision-first strategy and screenshot fallback.

    Uses a layered approach to find elements with maximum precision:
    1. First tries the most specific tool for the element type
    2. Falls back to small-scope specialized tools if needed
    3. Only uses broad-scope tools as last resort
    4. If DOM tools fail to find the target, uses screenshot analysis as fallback

    Principle: Precise selection > Small-scope selection > Avoid broad-scope selection > Screenshot analysis

    Args:
        element_description: Natural language description of the element to find

    Returns:
        Information about found elements with xpath selectors for precise targeting,
        or screenshot-based analysis if DOM tools fail

    Example:
        smart_element_finder("搜索框")
        smart_element_finder("提交按钮")
        smart_element_finder("登录表单")
        smart_element_finder("获取页面主要文字内容")
    """
    try:
        from ..agents.element_agent import build_element_agent

        logger.info(f"Smart element search (precision-first with screenshot fallback): {element_description}")

        # 强制精确优先的任务指令
        task = f"""
        查找元素: "{element_description}"
        """

        element_agent = build_element_agent()
        result = element_agent.invoke(task)

        output = result.get('output', str(result))
        logger.info(f"Smart element search (precision-first with screenshot fallback) completed")
        return output

    except Exception as e:
        error_msg = f"Smart element search failed: {str(e)}"
        logger.error(error_msg)
        return f"❌ {error_msg}"


# quick_page_scan removed - use smart_element_finder with dom_get_interactive_elements_smart instead


@tool
def smart_browser_action_finder(action_description: str) -> str:
    """
    智能浏览器操作查找器，职责分离策略。

    根据操作描述智能选择最合适的浏览器工具执行操作。
    专注于浏览器操作，对于需要元素查找的操作会指导用户先使用DOM工具。

    Args:
        action_description: 浏览器操作的描述，如"点击按钮"、"截图保存"、"输入文本"等

    Returns:
        执行结果或操作指导信息

    Example:
        smart_browser_action_finder("截图保存")  # 直接执行
        smart_browser_action_finder("点击登录按钮")  # 返回指导信息
        smart_browser_action_finder("导航到google.com")  # 直接执行
    """
    try:
        from ..agents.browser_agent import build_browser_agent

        logger.info(f"Smart browser action search (DOM-first strategy): {action_description}")

        task = f"""
        执行浏览器操作: "{action_description}"
        """

        browser_agent = build_browser_agent()
        result = browser_agent.invoke(task)

        output = result.get('output', str(result))
        logger.info(f"Smart browser action search (DOM-first strategy) completed")
        return output

    except Exception as e:
        error_msg = f"Smart browser action search failed: {str(e)}"
        logger.error(error_msg)
        return f"❌ {error_msg}"


# Transfer functions removed - use smart delegation functions instead to avoid circular delegation and maintain clean supervisor pattern


# 委托工具列表 - 使用纯委托模式，避免循环移交
DELEGATION_TOOLS = [
    smart_element_finder,             # 智能元素查找 → ElementAgent
    smart_browser_action_finder,      # 智能浏览器操作查找 → BrowserAgent
    # 未来扩展预留接口:
    # smart_code_generator,           # 代码生成 → CodeAgent
    # smart_code_executor,            # 代码执行 → ExecutionAgent
    # smart_data_processor,           # 数据处理 → DataAgent
    # smart_file_manager,             # 文件管理 → FileAgent
]


def get_delegation_tools():
    """
    Get delegation tools for CrawlerAgent.

    CrawlerAgent作为纯协调者，只使用委托工具与专门Agent通信。
    这种设计支持未来功能扩展，新功能通过添加新的委托工具和专门Agent实现。

    Returns:
        List of delegation tools that can be added to CrawlerAgent's tool list
    """
    return DELEGATION_TOOLS.copy()
