"""
Tools module for iICrawlerMCP.

This module contains <PERSON><PERSON>hain tools for browser automation and web interaction.
"""

from .browser_tools import (
    BrowserToolkit, get_tools, get_browser_specific_tools, cleanup_tools,
    # Basic tools
    navigate_browser, take_screenshot, get_page_info, click_element, type_text, hover_element,
    # Advanced tools
    navigate_browser_advanced, take_screenshot_advanced, browser_snapshot,
    click_element_advanced, type_text_advanced, browser_wait_for, browser_evaluate,
    browser_hover, browser_press_key, browser_select_option
)
# dom_tools removed - use dom_tools_enhanced instead
from .dom_tools_enhanced import (
    dom_get_buttons_enhanced, dom_get_inputs_enhanced, dom_get_links_enhanced,
    dom_get_form_elements, dom_get_navigation_elements, dom_get_media_elements,
    dom_get_content_elements, dom_get_clickable_elements, dom_get_focusable_elements,
    dom_get_interactive_elements_smart, get_enhanced_dom_tools, ENHANCED_DOM_TOOLS
)
from .delegation_tools import (
    smart_element_finder, smart_browser_action_finder, smart_prompt_optimizer,
    get_delegation_tools, DELEGATION_TOOLS
)
from .prompt_optimization_tools import (
    analyze_prompt_clarity, detect_prompt_contradictions,
    generate_clarifying_questions, optimize_prompt_structure,
    llm_reflection_optimizer, llm_iterative_optimizer,
    llm_quality_assessor, llm_expert_panel_optimizer,
    get_prompt_optimization_tools, PROMPT_OPTIMIZATION_TOOLS
)

__all__ = [
    # Browser toolkit
    'BrowserToolkit',
    'get_tools',
    'get_browser_specific_tools',
    'cleanup_tools',
    # Basic browser tools
    'navigate_browser',
    'take_screenshot',
    'get_page_info',
    'click_element',
    'type_text',
    'hover_element',
    # Advanced browser tools
    'navigate_browser_advanced',
    'take_screenshot_advanced',
    'browser_snapshot',
    'click_element_advanced',
    'type_text_advanced',
    'browser_wait_for',
    'browser_evaluate',
    'browser_hover',
    'browser_press_key',
    'browser_select_option',
    # Enhanced DOM tools
    'dom_get_buttons_enhanced',
    'dom_get_inputs_enhanced',
    'dom_get_links_enhanced',
    'dom_get_form_elements',
    'dom_get_navigation_elements',
    'dom_get_media_elements',
    'dom_get_content_elements',
    'dom_get_clickable_elements',
    'dom_get_focusable_elements',
    'dom_get_interactive_elements_smart',
    'get_enhanced_dom_tools',
    'ENHANCED_DOM_TOOLS',
    # Delegation tools
    'smart_element_finder',
    'smart_browser_action_finder',
    'get_delegation_tools',
    'DELEGATION_TOOLS',
]
