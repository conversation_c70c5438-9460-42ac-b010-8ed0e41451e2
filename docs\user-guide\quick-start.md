# 快速上手指南

## 🎯 5分钟快速体验

本指南将帮助您在5分钟内体验iICrawlerMCP的核心功能。

## 📋 前置条件

确保您已经完成了[安装指南](installation.md)中的基础安装步骤。

## 🚀 使用方式选择

iICrawlerMCP 提供多种使用方式，选择最适合您的方式：

### 🎨 方式1: Streamlit UI (推荐新用户)
- ✅ 图形化界面，易于使用
- ✅ 实时查看 Agent 思考过程
- ✅ 可视化执行结果和截图
- ✅ 适合学习和调试

### 💻 方式2: 命令行 (适合开发者)
- ✅ 直接调用 Agent API
- ✅ 适合脚本化和自动化
- ✅ 性能更高

### 🌐 方式3: MCP 服务器 (适合集成)
- ✅ 标准化 API 接口
- ✅ 支持外部系统集成
- ✅ 符合 MCP 协议规范

## 🎨 方式1: Streamlit UI 快速体验

### 步骤1: 安装 Streamlit 依赖

```bash
# 安装 Streamlit 相关依赖
pip install streamlit>=1.28.0 langchain-community>=0.0.20
```

### 步骤2: 启动 Streamlit 界面

```bash
# 激活虚拟环境
source .venv/bin/activate  # macOS/Linux
.venv\Scripts\activate     # Windows

# 启动 Streamlit UI
streamlit run src/iicrawlermcp/ui/app.py
```

启动后，您将看到类似输出：
```
You can now view your Streamlit app in your browser.

Local URL: http://localhost:8501
Network URL: http://*************:8501
```

### 步骤3: 访问 Web 界面

打开浏览器，访问 `http://localhost:8501`

您将看到 iICrawlerMCP 的 Streamlit 界面，包含：
- **侧边栏**: 系统配置信息和验证状态
- **任务输入区**: 自然语言任务描述输入
- **执行结果区**: 实时思考过程和结果展示

### 步骤4: 执行第一个任务

1. **检查配置**: 确保侧边栏显示"✅ 配置验证通过"
2. **输入任务**: 在任务描述框中输入：
   ```
   打开google.com，搜索迪士尼乐园，点击第一个非广告的结果，然后给我一个迪士尼乐园的页面截图
   ```
3. **执行任务**: 点击"🚀 执行任务"按钮
4. **观察过程**: 实时查看 Agent 的思考过程和工具调用
5. **查看结果**: 任务完成后查看输出结果和截图

## 💻 方式2: 命令行快速体验

### 步骤1: 启动命令行程序

```bash
# 激活虚拟环境
source .venv/bin/activate  # macOS/Linux
.venv\Scripts\activate     # Windows

# 运行示例程序
python main.py
```

### 步骤2: 使用 Python API

```python
from src.iicrawlermcp.agents import build_agent

# 创建 Agent
agent = build_agent()

# 执行任务
task = "打开google.com，搜索迪士尼乐园，点击第一个非广告的结果，然后给我一个迪士尼乐园的页面截图"
result = agent.invoke(task)

# 查看结果
print(f"执行结果: {result['output']}")

# 清理资源
agent.cleanup()
```

## 🌐 方式3: MCP 服务器快速体验

### 步骤1: 启动 MCP 服务器

```bash
# 启动 SSE 传输模式的 MCP 服务器
python src/iicrawlermcp/mcp/run_server.py sse --host 127.0.0.1 --port 8000
```

服务启动后，您将看到：
```
Starting iICrawlerMCP MCP Server with SSE transport...
Server running on http://127.0.0.1:8000/sse
```

### 步骤2: 使用 MCP 客户端调用

```python
import asyncio
from fastmcp import Client

async def call_mcp_task():
    async with Client("http://127.0.0.1:8000/sse") as client:
        result = await client.call_tool(
            "intelligent_web_task",
            {"task_description": "访问百度首页，搜索迪士尼乐园，截图保存"}
        )
        return result.content

# 执行调用
result = asyncio.run(call_mcp_task())
print(f"MCP 执行结果: {result}")
```

## 🎯 功能对比

| 功能特性 | Streamlit UI | 命令行 | MCP 服务器 |
|---------|-------------|--------|-----------|
| 易用性 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐ |
| 思考过程可视化 | ✅ | ❌ | ❌ |
| 实时监控 | ✅ | ❌ | ❌ |
| 外部集成 | ❌ | ✅ | ✅ |
| 性能 | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ |
| 调试友好 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐ |

## 📝 下一步

### 深入学习
- [Streamlit UI 详细指南](streamlit-ui.md) - 完整的 UI 使用说明
- [API 参考](api-reference.md) - Agent 和工具 API 文档
- [配置指南](configuration.md) - 系统配置详细说明

### 高级功能
- [用户手册](user-manual.md) - 完整功能说明
- [开发指南](../development/setup.md) - 开发环境搭建
- [架构文档](../architecture/system-architecture.md) - 系统架构理解

### 示例和模板

```bash
curl -X POST "http://localhost:8000/api/tasks" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "我的第一个爬虫",
    "target_url": "https://example.com",
    "data_fields": ["title", "content"],
    "max_items": 10
  }'
```

#### 方法3: 使用Python客户端

```python
import requests

# 创建任务
task_data = {
    "name": "我的第一个爬虫",
    "target_url": "https://example.com",
    "data_fields": ["title", "content"],
    "max_items": 10
}

response = requests.post(
    "http://localhost:8000/api/tasks",
    json=task_data
)

task_id = response.json()["task_id"]
print(f"任务创建成功，ID: {task_id}")
```

### 步骤4: 监控任务执行

#### Web界面监控

在Web界面中，您可以实时看到：
- 任务执行进度
- 当前执行阶段
- 已爬取的数据数量
- 错误和警告信息

#### API监控

```bash
# 查询任务状态
curl "http://localhost:8000/api/tasks/{task_id}/status"

# 获取实时日志
curl "http://localhost:8000/api/tasks/{task_id}/logs"
```

#### Python监控

```python
import time
import requests

def monitor_task(task_id):
    while True:
        response = requests.get(f"http://localhost:8000/api/tasks/{task_id}/status")
        status = response.json()
        
        print(f"状态: {status['phase']}")
        print(f"进度: {status['progress']}%")
        
        if status['phase'] in ['completed', 'failed']:
            break
            
        time.sleep(2)

monitor_task(task_id)
```

### 步骤5: 获取结果

任务完成后，您可以通过多种方式获取结果：

#### 下载结果文件

```bash
# 下载JSON格式
curl "http://localhost:8000/api/tasks/{task_id}/results?format=json" -o results.json

# 下载CSV格式
curl "http://localhost:8000/api/tasks/{task_id}/results?format=csv" -o results.csv

# 下载Excel格式
curl "http://localhost:8000/api/tasks/{task_id}/results?format=xlsx" -o results.xlsx
```

#### 查看结果预览

```python
import requests
import json

# 获取结果预览
response = requests.get(f"http://localhost:8000/api/tasks/{task_id}/results?limit=5")
results = response.json()

print("爬取结果预览:")
for item in results['data']:
    print(f"标题: {item['title']}")
    print(f"内容: {item['content'][:100]}...")
    print("-" * 50)
```

## 🎨 进阶示例

### 示例1: 电商商品信息爬取

```python
# 创建电商爬取任务
ecommerce_task = {
    "name": "淘宝iPhone价格监控",
    "target_url": "https://www.taobao.com",
    "data_fields": ["title", "price", "sales", "rating", "shop_name"],
    "search_keywords": ["iPhone 15"],
    "max_items": 50,
    "filters": {
        "min_price": 1000,
        "max_price": 10000
    }
}

response = requests.post("http://localhost:8000/api/tasks", json=ecommerce_task)
```

### 示例2: 新闻文章批量爬取

```python
# 创建新闻爬取任务
news_task = {
    "name": "科技新闻聚合",
    "target_urls": [
        "https://tech.sina.com.cn",
        "https://tech.163.com",
        "https://www.36kr.com"
    ],
    "data_fields": ["title", "content", "author", "publish_time", "tags"],
    "max_items": 100,
    "parallel_agents": 3
}

response = requests.post("http://localhost:8000/api/tasks", json=news_task)
```

### 示例3: 定时任务设置

```python
# 创建定时爬取任务
scheduled_task = {
    "name": "每日价格监控",
    "target_url": "https://example-shop.com",
    "data_fields": ["product_name", "price", "stock"],
    "schedule": {
        "type": "daily",
        "time": "09:00",
        "timezone": "Asia/Shanghai"
    },
    "notifications": {
        "email": "<EMAIL>",
        "webhook": "https://your-webhook-url.com"
    }
}

response = requests.post("http://localhost:8000/api/scheduled-tasks", json=scheduled_task)
```

## 🔧 常用配置

### 浏览器设置

```python
# 自定义浏览器配置
browser_config = {
    "headless": False,  # 显示浏览器窗口
    "timeout": 30000,   # 30秒超时
    "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
    "viewport": {"width": 1920, "height": 1080},
    "proxy": "http://proxy-server:8080"
}
```

### 数据处理设置

```python
# 数据清洗配置
data_processing = {
    "remove_duplicates": True,
    "clean_text": True,
    "validate_urls": True,
    "extract_images": False,
    "max_text_length": 1000
}
```

### 性能优化设置

```python
# 性能配置
performance_config = {
    "concurrent_agents": 5,
    "request_delay": 1.0,  # 请求间隔1秒
    "retry_attempts": 3,
    "cache_enabled": True,
    "batch_size": 20
}
```

## 📊 监控和调试

### 查看系统状态

```bash
# 系统健康检查
curl "http://localhost:8000/health"

# 系统指标
curl "http://localhost:8000/metrics"

# 活跃任务列表
curl "http://localhost:8000/api/tasks?status=running"
```

### 日志查看

```bash
# 查看应用日志
tail -f logs/app.log

# 查看特定任务日志
curl "http://localhost:8000/api/tasks/{task_id}/logs?level=INFO"
```

## 🎯 下一步

恭喜！您已经成功完成了iICrawlerMCP的快速上手。接下来您可以：

1. 阅读[用户手册](user-manual.md)了解更多功能
2. 查看[API参考](api-reference.md)学习完整的API
3. 探索[示例项目](../../examples/README.md)获取更多灵感
4. 了解[架构设计](../architecture/system-architecture.md)深入理解系统

## 🆘 遇到问题？

如果在快速上手过程中遇到问题：

1. 查看[故障排除指南](troubleshooting.md)
2. 检查[常见问题FAQ](faq.md)
3. 提交[GitHub Issue](https://github.com/your-org/iicrawlermcp/issues)
4. 加入社区讨论

---

*最后更新: 2025-01-29*
