# iICrawlerMCP 多Agent系统架构详解

## 📋 系统概述

iICrawlerMCP采用多Agent协作架构，基于LangChain框架实现智能网页自动化。系统遵循"纯协调者模式"(Pure Orchestrator Pattern)，确保职责分离和高效协作。

## 🏗️ 架构设计原则

### 1. 职责分离 (Separation of Concerns)
- **CrawlerAgent**: 纯协调者，负责任务分解和Agent间协调
- **BrowserAgent**: 浏览器操作专家，专注页面交互
- **ElementAgent**: DOM分析专家，专注元素发现和结构分析

### 2. 委托模式 (Delegation Pattern)
- 通过智能委托工具实现Agent间通信
- 避免直接依赖，提高系统解耦性
- 支持动态扩展新的专门Agent

### 3. 工具无重叠 (No Tool Overlap)
- 每个Agent拥有独特的工具集
- 消除功能重复，提高系统效率
- 基于多Agent系统最佳实践设计

## 🤖 Agent详细分析

### CrawlerAgent (主协调Agent)

**角色定位**: 纯协调者 (Pure Orchestrator)

**工具配置**:

```python
# 浏览器工具: 0个 (已移除所有直接浏览器工具)
# 委托工具: 2个
tools = [
    smart_element_finder,        # 委托给ElementAgent
    smart_browser_action_finder  # 委托给BrowserAgent
]
```

**核心功能**:
- 🎯 **任务分解和协调** - 理解复杂任务并分解为子任务
- 🔄 **智能委托** - 根据任务类型选择合适的专门Agent
- 📊 **结果整合** - 汇总各Agent的执行结果
- 🚀 **工作流管理** - 管理多步骤任务的执行流程

**设计实现**:
```python
def _get_curated_browser_tools(self) -> List[BaseTool]:
    """CrawlerAgent作为纯协调者，不直接使用浏览器工具"""
    return []  # 返回空列表 - 纯协调者模式
```

### BrowserAgent (浏览器专家)

**角色定位**: 浏览器操作专家

**工具配置**:
```python
# 基础浏览器工具: 7个
tools = [
    navigate_browser,     # 页面导航
    take_screenshot,      # 截图功能
    analyze_screenshot,   # 截图分析
    get_page_info,        # 页面信息获取
    click_element,        # 元素点击
    type_text,           # 文本输入
    hover_element        # 鼠标悬停
]
```

**核心功能**:
- 🌐 **页面导航** - URL跳转、前进后退
- 📸 **截图捕获** - 全页面、元素截图
- 🖱️ **基础交互** - 点击、输入、悬停
- 📄 **页面信息** - 标题、URL、状态获取
- 🔍 **视觉分析** - 基于截图的AI分析

### ElementAgent (DOM分析专家)

**角色定位**: DOM结构分析和元素发现专家

**工具配置**:
```python
# DOM分析工具: 11个
tools = [
    dom_get_buttons_enhanced,           # 按钮元素分析
    dom_get_inputs_enhanced,            # 输入框分析
    dom_get_links_enhanced,             # 链接分析
    dom_get_form_elements,              # 表单元素分析
    dom_get_navigation_elements,        # 导航元素分析
    dom_get_media_elements,             # 媒体元素分析
    dom_get_content_elements,           # 内容元素分析
    dom_get_clickable_elements,         # 可点击元素分析
    dom_get_focusable_elements,         # 可聚焦元素分析
    dom_get_interactive_elements_smart, # 智能交互元素分析
    dom_assess_information_completeness # 信息完整性评估
]
```

**核心功能**:
- 🔍 **元素发现** - 智能元素定位和识别
- 📊 **结构分析** - DOM结构和层次分析
- 🎯 **精确定位** - 提供xpath等精确选择器
- 📋 **数据提取** - 结构化内容提取
- 🧠 **语义理解** - 基于AI的元素语义分析

## 🔄 Agent间协作机制

### 委托工具实现

**smart_element_finder (元素查找委托)**:

```python
@tool
def smart_element_finder(element_description: str) -> str:
    """委托ElementAgent进行元素查找和分析"""
    element_agent = build_element_agent()
    task = f'查找元素: "{element_description}"'
    result = element_agent.invoke(task)
    return result.get('output', str(result))
```

**smart_browser_action_finder (浏览器操作委托)**:
```python
@tool
def smart_browser_action_finder(action_description: str) -> str:
    """委托BrowserAgent进行浏览器操作"""
    browser_agent = build_browser_agent()
    task = f'执行浏览器操作: "{action_description}"'
    result = browser_agent.invoke(task)
    return result.get('output', str(result))
```

### 工作流程示例

**任务**: "在google.com搜索'AI'"

```
CrawlerAgent (协调者):
├── 1. 分析任务 → 需要导航 + 元素查找 + 交互
├── 2. smart_browser_action_finder("导航到google.com") 
│   └── BrowserAgent: navigate_browser("https://google.com")
├── 3. smart_element_finder("搜索框")
│   └── ElementAgent: dom_get_inputs_enhanced() → 找到搜索框xpath
├── 4. smart_browser_action_finder("在搜索框输入'AI'")
│   └── BrowserAgent: type_text(xpath="//*[@name='q']", "AI")
├── 5. smart_element_finder("搜索按钮")
│   └── ElementAgent: dom_get_buttons_enhanced() → 找到搜索按钮
├── 6. smart_browser_action_finder("点击搜索按钮")
│   └── BrowserAgent: click_element(xpath="//*[@name='btnK']")
└── 7. 整合结果并返回执行报告
```

## 🎯 架构优势

### 1. 无工具重叠
- 每个Agent有明确的工具边界
- 消除了功能重复，提高系统效率
- 符合多Agent系统最佳实践

### 2. 高度可扩展
- 新功能通过添加新的委托工具和专门Agent实现
- 预留了未来扩展接口：
  ```python
  # 未来扩展预留接口:
  # smart_code_generator,    # 代码生成 → CodeAgent
  # smart_code_executor,     # 代码执行 → ExecutionAgent
  # smart_data_processor,    # 数据处理 → DataAgent
  # smart_file_manager,      # 文件管理 → FileAgent
  ```

### 3. 职责清晰
- **CrawlerAgent**: 纯协调者，不直接操作
- **BrowserAgent**: 浏览器操作专家
- **ElementAgent**: DOM分析专家

### 4. 符合最佳实践
- 基于Anthropic、Microsoft Azure的多Agent系统研究
- 实现了"Orchestrator Pattern"纯协调者模式
- 避免了工具语义重叠问题

## 📊 性能指标

### Agent评估指标
- **工具选择质量**: 选择最合适工具的准确率
- **工具错误率**: 工具执行失败的频率
- **行动推进度**: 每次行动对任务完成的贡献度
- **重路由计数**: 因工具重叠导致的重定向次数

### 系统优化效果
- ✅ **工具重叠**: 从3个重叠工具减少到0个
- ✅ **Agent专业化**: 每个Agent专注核心职责
- ✅ **扩展性**: 支持无缝添加新Agent类型
- ✅ **维护性**: 清晰的职责边界便于维护

## 🚀 使用示例

### 基本使用
```python
from iicrawlermcp.agents import build_agent

# 创建主协调Agent
agent = build_agent()

# 执行复杂任务
result = agent.invoke("在淘宝搜索iPhone并截图保存")
print(result["output"])

# 清理资源
agent.cleanup()
```

### 专门Agent使用
```python
from iicrawlermcp.agents import build_browser_agent, build_element_agent

# 浏览器控制专家
browser_agent = build_browser_agent()
browser_agent.invoke("导航到https://google.com并截图")

# DOM分析专家
element_agent = build_element_agent()
element_agent.invoke("分析页面上的所有表单元素")
```

## � 完整执行示例

### 任务：在Google搜索"AI"

**执行流程详解**：

#### 1. 任务接收和分解
```python
# 用户输入
user_input = "在google.com搜索'AI'"

# CrawlerAgent分析任务
# 📋 任务分解：
# 1. 导航到google.com
# 2. 找到搜索框
# 3. 输入'AI'
# 4. 点击搜索按钮
```

#### 2. 导航操作 (CrawlerAgent → BrowserAgent)
```python
# CrawlerAgent委托浏览器操作
result = smart_browser_action_finder("导航到google.com")

# BrowserAgent执行导航
# └── navigate_browser("https://google.com")
# └── ✅ 页面已加载
```

#### 3. 查找搜索框 (CrawlerAgent → ElementAgent)
```python
# CrawlerAgent委托元素查找
result = smart_element_finder("搜索框")

# ElementAgent执行DOM分析
# └── dom_get_inputs_enhanced()
# └── ✅ 找到搜索框: xpath="//*[@name='q']"
```

#### 4. 输入搜索内容 (CrawlerAgent → BrowserAgent)
```python
# CrawlerAgent委托浏览器操作
result = smart_browser_action_finder("在搜索框输入'AI'")

# BrowserAgent执行输入操作
# └── type_text(xpath="//*[@name='q']", "AI")
# └── ✅ 文本已输入
```

#### 5. 查找搜索按钮 (CrawlerAgent → ElementAgent)
```python
# CrawlerAgent委托元素查找
result = smart_element_finder("搜索按钮")

# ElementAgent执行按钮分析
# └── dom_get_buttons_enhanced()
# └── ✅ 找到搜索按钮: xpath="//*[@name='btnK']"
```

#### 6. 点击搜索按钮 (CrawlerAgent → BrowserAgent)
```python
# CrawlerAgent委托浏览器操作
result = smart_browser_action_finder("点击搜索按钮")

# BrowserAgent执行点击操作
# └── click_element(xpath="//*[@name='btnK']")
# └── ✅ 搜索完成，结果页面已显示
```

#### 7. 结果整合和返回
```python
# CrawlerAgent整合所有执行结果
final_result = {
    "output": "✅ 任务完成：已在Google搜索'AI'，搜索结果页面已显示",
    "steps_completed": 6,
    "agents_used": ["BrowserAgent", "ElementAgent"],
    "execution_time": "3.2s"
}
```

### 实际代码示例
```python
from iicrawlermcp.agents import build_agent
import logging

# 启用详细日志
logging.basicConfig(level=logging.INFO)

def main():
    # 创建主协调Agent
    agent = build_agent(verbose=True)

    try:
        # 执行复杂搜索任务
        print("🚀 开始执行任务...")
        result = agent.invoke("在google.com搜索'AI'")

        # 显示执行结果
        print("📊 执行结果:")
        print(result["output"])

        # 可选：继续执行相关任务
        print("\n🔄 继续执行相关任务...")
        result2 = agent.invoke("截图保存搜索结果页面")
        print(result2["output"])

    except Exception as e:
        print(f"❌ 任务执行失败: {e}")

    finally:
        # 清理资源
        agent.cleanup()
        print("🧹 资源清理完成")

if __name__ == "__main__":
    main()
```

### 执行日志示例
```
🚀 开始执行任务...
INFO:CrawlerAgent:Executing task: 在google.com搜索'AI'
INFO:delegation_tools:Smart browser action search: 导航到google.com
INFO:BrowserAgent:BrowserAgent executing task: 导航到google.com
INFO:browser_tools:BrowserTool navigate: https://google.com
INFO:delegation_tools:Smart element search: 搜索框
INFO:ElementAgent:ElementAgent executing task: 查找搜索框
INFO:dom_tools_enhanced:DOM analysis: 找到1个输入框元素
INFO:delegation_tools:Smart browser action search: 在搜索框输入'AI'
INFO:BrowserAgent:BrowserAgent executing task: 在搜索框输入'AI'
INFO:browser_tools:BrowserTool type_text: AI
INFO:delegation_tools:Smart element search: 搜索按钮
INFO:ElementAgent:ElementAgent executing task: 查找搜索按钮
INFO:dom_tools_enhanced:DOM analysis: 找到1个按钮元素
INFO:delegation_tools:Smart browser action search: 点击搜索按钮
INFO:BrowserAgent:BrowserAgent executing task: 点击搜索按钮
INFO:browser_tools:BrowserTool click_element: 点击成功
INFO:CrawlerAgent:Task completed successfully

📊 执行结果:
✅ 任务完成：已在Google搜索'AI'，搜索结果页面已显示
- 导航到Google首页 ✅
- 找到并定位搜索框 ✅
- 输入搜索关键词'AI' ✅
- 找到并点击搜索按钮 ✅
- 搜索结果页面已加载 ✅

🔄 继续执行相关任务...
INFO:delegation_tools:Smart browser action search: 截图保存搜索结果页面
INFO:BrowserAgent:BrowserAgent executing task: 截图保存搜索结果页面
INFO:browser_tools:BrowserTool take_screenshot: Screenshot saved: screenshots/google_search_AI_20241201_143022.png

✅ 截图已保存：screenshots/google_search_AI_20241201_143022.png

🧹 资源清理完成
```

## �🔮 未来发展

### v2.0 规划 - LangGraph迁移
- **TaskAgent**: 任务理解专家，自然语言解析和计划生成
- **RecordAgent**: 操作录制专家，实时操作捕获和序列化
- **CodeGenAgent**: 代码生成专家，基于模板的代码自动生成
- **ExecAgent**: 执行验证专家，代码执行和结果验证

### 技术升级
- 从LangChain AgentExecutor迁移到LangGraph工作流
- 增强状态管理和对话历史保持
- 实现更智能的任务分解和执行策略

## 🚀 v2.0 完整数据采集工作流架构

### 🎯 **业务需求分析**

基于实际数据采集业务场景，系统需要支持：
- **输入格式**: 任意格式的需求描述
- **提示词优化**: 针对GPT模型进行智能优化
- **数据存储**: 云端MySQL数据库，通过API调用
- **数据验证**: 生成示例供用户确认后再保存
- **消息通知**: 飞书通知支持阶段完成、异常、成功等场景
- **智能处理**: 大模型自动判断流程，失败自动处理
- **人工干预**: 关键核心步骤设置人工确认点
- **团队规模**: 3-5人团队，技术栈Python + MySQL

### 🏗️ **推荐Agent架构 (6个Agent)**

#### **1. TaskAgent (任务理解和流程编排)**
- **职责**:
  - 解析用户需求描述（任意格式输入）
  - 智能询问关键信息补全需求
  - 生成完整的数据采集计划
  - 流程编排和状态管理
- **工具**: 需求分析、信息询问、计划生成
- **人工干预点**: 需求确认、计划审核

#### **2. CrawlerAgent (爬虫协调 - 现有)**
- **职责**:
  - 网页数据采集协调
  - 浏览器操作编排
  - 数据提取流程管理
- **工具**: 现有的委托工具 (smart_element_finder, smart_browser_action_finder)
- **人工干预点**: 复杂页面处理确认

#### **3. DataAgent (数据处理和验证)**
- **职责**:
  - 数据清洗和格式化
  - MySQL字段映射
  - 数据质量验证
  - 生成数据示例供用户确认
- **工具**: 数据处理、格式转换、示例生成
- **人工干预点**: 数据示例确认、质量验证

#### **4. StorageAgent (数据存储管理)**
- **职责**:
  - 云端数据库连接管理
  - 数据保存执行
  - 存储状态监控
  - 数据备份和恢复
- **工具**: 数据库操作、API调用、状态监控
- **人工干预点**: 存储异常处理

#### **5. NotificationAgent (消息通知管理)**
- **职责**:
  - 飞书消息发送
  - 阶段完成通知
  - 异常告警
  - 任务成功通知
- **工具**: 飞书API、消息模板、通知规则
- **人工干预点**: 关键异常确认

#### **6. MonitorAgent (监控和异常处理)**
- **职责**:
  - 全流程监控
  - 异常检测和自动处理
  - 重试机制管理
  - 性能指标收集
- **工具**: 监控工具、异常处理、重试逻辑
- **人工干预点**: 严重异常升级

### 🔄 **完整工作流程设计**

```python
# 完整数据采集流程
用户输入需求
    ↓
TaskAgent (需求理解 + 信息询问)
    ↓ [人工确认]
TaskAgent (生成采集计划)
    ↓ [人工审核]
CrawlerAgent (执行数据采集)
    ↓ [复杂页面人工确认]
DataAgent (数据处理 + 生成示例)
    ↓ [人工确认数据示例]
StorageAgent (保存到云端数据库)
    ↓
NotificationAgent (发送完成通知)
    ↓
MonitorAgent (全程监控 + 异常处理)
```

### 🛠️ **技术实现规划**

#### **扩展委托工具**
```python
# 扩展现有的委托工具
DELEGATION_TOOLS = [
    # 现有工具
    smart_element_finder,        # → ElementAgent
    smart_browser_action_finder, # → BrowserAgent

    # 新增工具
    smart_task_planner,         # → TaskAgent
    smart_data_processor,       # → DataAgent
    smart_storage_manager,      # → StorageAgent
    smart_notification_sender,  # → NotificationAgent
    smart_monitor_checker,      # → MonitorAgent
]
```

#### **数据库集成配置**
```python
# 扩展配置类
class Config:
    # 现有配置...

    # 数据库配置
    MYSQL_HOST: str = os.getenv("MYSQL_HOST", "")
    MYSQL_PORT: int = int(os.getenv("MYSQL_PORT", "3306"))
    MYSQL_USER: str = os.getenv("MYSQL_USER", "")
    MYSQL_PASSWORD: str = os.getenv("MYSQL_PASSWORD", "")
    MYSQL_DATABASE: str = os.getenv("MYSQL_DATABASE", "")

    # 飞书配置
    FEISHU_APP_ID: str = os.getenv("FEISHU_APP_ID", "")
    FEISHU_APP_SECRET: str = os.getenv("FEISHU_APP_SECRET", "")
    FEISHU_WEBHOOK_URL: str = os.getenv("FEISHU_WEBHOOK_URL", "")
```

#### **并发处理能力**
```python
# 异步处理提高并发能力
import asyncio
from concurrent.futures import ThreadPoolExecutor

class ConcurrentTaskManager:
    def __init__(self, max_workers=5):
        self.executor = ThreadPoolExecutor(max_workers=max_workers)

    async def execute_parallel_tasks(self, tasks):
        loop = asyncio.get_event_loop()
        futures = [
            loop.run_in_executor(self.executor, task)
            for task in tasks
        ]
        return await asyncio.gather(*futures)
```

### 🎯 **架构优势**

1. **简单高效**: 6个Agent分工明确，避免过度复杂化
2. **扩展性强**: 委托模式支持无缝添加新Agent
3. **并发能力**: 异步处理 + 线程池支持高并发
4. **人工干预**: 关键节点设置确认点，保证质量
5. **异常处理**: MonitorAgent专门负责异常检测和自动处理
6. **团队友好**: 3-5人团队可以轻松维护，每人负责1-2个Agent

### 📊 **完整执行示例 - 数据采集任务**

#### **任务**: "采集京东手机商品信息并保存到数据库"

**执行流程详解**：

```python
# 1. 任务理解阶段
TaskAgent.invoke("采集京东手机商品信息并保存到数据库")
# → 分析需求，询问关键信息：
#   - 需要采集哪些字段？(商品名、价格、评价数等)
#   - 采集多少页数据？
#   - 数据库表结构是什么？
# → [人工确认] 用户确认需求细节

# 2. 计划生成阶段
TaskAgent.generate_plan()
# → 生成详细采集计划：
#   1. 导航到京东手机分类页
#   2. 循环采集每页商品信息
#   3. 数据清洗和格式化
#   4. 保存到MySQL数据库
# → [人工审核] 用户确认采集计划

# 3. 数据采集阶段
CrawlerAgent.execute_crawling()
# → smart_browser_action_finder("导航到京东手机页面")
# → smart_element_finder("商品列表容器")
# → smart_element_finder("商品名称、价格、评价等元素")
# → [复杂页面人工确认] 确认元素定位准确

# 4. 数据处理阶段
DataAgent.process_data()
# → 数据清洗：去除特殊字符、统一格式
# → MySQL字段映射：product_name, price, rating_count
# → 生成数据示例：
#   {
#     "product_name": "iPhone 15 Pro Max 256GB",
#     "price": 9999.00,
#     "rating_count": 12580
#   }
# → [人工确认] 用户确认数据示例格式正确

# 5. 数据存储阶段
StorageAgent.save_to_database()
# → 连接云端MySQL数据库
# → 批量插入数据到products表
# → 验证数据完整性
# → [存储异常处理] 自动重试或人工干预

# 6. 通知发送阶段
NotificationAgent.send_notification()
# → 发送飞书消息：
#   "✅ 数据采集任务完成
#    - 采集商品数量：1250个
#    - 数据保存成功：1248个
#    - 异常数据：2个
#    - 执行时间：15分钟"

# 7. 全程监控
MonitorAgent.monitor_process()
# → 实时监控各阶段执行状态
# → 异常自动处理和重试
# → 性能指标收集
```

### 🔧 **实际代码示例**
```python
from iicrawlermcp.agents import build_task_agent, build_crawler_agent
import asyncio

async def main():
    # 创建任务协调Agent
    task_agent = build_task_agent()

    try:
        # 1. 任务理解和计划生成
        print("🚀 开始数据采集任务...")
        task_plan = await task_agent.invoke(
            "采集京东手机商品信息并保存到数据库"
        )

        # 2. 执行数据采集
        crawler_agent = build_crawler_agent()
        crawl_result = await crawler_agent.invoke(task_plan["crawl_plan"])

        # 3. 数据处理和存储
        data_agent = build_data_agent()
        processed_data = await data_agent.invoke(crawl_result["raw_data"])

        # 4. 保存到数据库
        storage_agent = build_storage_agent()
        save_result = await storage_agent.invoke(processed_data["clean_data"])

        # 5. 发送通知
        notification_agent = build_notification_agent()
        await notification_agent.invoke({
            "type": "task_completed",
            "result": save_result,
            "execution_time": "15分钟"
        })

        print("✅ 数据采集任务完成")

    except Exception as e:
        # 异常处理和通知
        monitor_agent = build_monitor_agent()
        await monitor_agent.handle_exception(e)

    finally:
        # 清理资源
        await task_agent.cleanup()

if __name__ == "__main__":
    asyncio.run(main())
```

---

## 🔧 **详细工具配置设计**

### 📋 **1. TaskAgent - 任务理解和流程编排**

**核心职责**: 任务理解、计划生成、流程编排、全局协调

**工具配置** (8个工具):

#### **任务分析工具**
```python
@tool
def analyze_task_requirements(task_description: str) -> str:
     
        analyze_task_requirements("采集京东手机商品信息")
    """

@tool
def generate_collection_plan(requirements: str) -> str:
    """
    基于需求分析生成详细的数据采集计划。

    Args:
        requirements: 完整的需求信息

    Returns:
        详细的采集计划，包括步骤、字段、页面等

    Example:
        generate_collection_plan("采集京东手机：名称、价格、评价数，前10页")
    """

@tool
def ask_user_confirmation(question: str, options: list = None) -> str:
    """
    向用户询问确认信息或选择。

    Args:
        question: 需要确认的问题
        options: 可选的选项列表

    Returns:
        用户的确认结果

    Example:
        ask_user_confirmation("是否确认采集计划？", ["确认", "修改", "取消"])
    """
```

#### **委托工具** (5个)
```python
# 现有委托工具
smart_element_finder          # → ElementAgent
smart_browser_action_finder   # → BrowserAgent

# 新增委托工具
smart_data_processor         # → DataAgent
smart_storage_manager        # → StorageAgent
smart_notification_sender    # → NotificationAgent
smart_monitor_checker        # → MonitorAgent
```

### 📊 **2. DataAgent - 数据处理和验证**

**核心职责**: 数据清洗、格式化、验证、示例生成

**工具配置** (6个工具):

```python
@tool
def clean_raw_data(raw_data: str, data_type: str = "product") -> str:
    """
    清洗原始采集数据，去除特殊字符和无效内容。

    Args:
        raw_data: 原始采集的数据
        data_type: 数据类型 (product, article, contact等)

    Returns:
        清洗后的结构化数据

    Example:
        clean_raw_data("iPhone 15 Pro Max\n\n¥9999.00\n12580评价")
    """

@tool
def validate_data_quality(data: str, validation_rules: str) -> str:
    """
    验证数据质量，检查必填字段和数据格式。

    Args:
        data: 待验证的数据
        validation_rules: 验证规则描述

    Returns:
        验证结果和质量报告

    Example:
        validate_data_quality(data, "必填：商品名、价格；价格必须为数字")
    """

@tool
def map_to_mysql_fields(data: str, table_schema: str) -> str:
    """
    将数据映射到MySQL表字段格式。

    Args:
        data: 清洗后的数据
        table_schema: MySQL表结构描述

    Returns:
        映射后的MySQL插入格式数据

    Example:
        map_to_mysql_fields(data, "products(name, price, rating_count)")
    """

@tool
def generate_data_sample(mapped_data: str, sample_count: int = 3) -> str:
    """
    生成数据示例供用户确认。

    Args:
        mapped_data: 映射后的数据
        sample_count: 示例数量

    Returns:
        格式化的数据示例

    Example:
        generate_data_sample(mapped_data, 3)
    """

@tool
def convert_data_format(data: str, target_format: str) -> str:
    """
    转换数据格式 (JSON, CSV, XML等)。

    Args:
        data: 源数据
        target_format: 目标格式 (json, csv, xml)

    Returns:
        转换后的数据

    Example:
        convert_data_format(data, "json")
    """

@tool
def calculate_data_statistics(data: str) -> str:
    """
    计算数据统计信息。

    Args:
        data: 数据集

    Returns:
        统计报告（数量、完整性、异常值等）

    Example:
        calculate_data_statistics(product_data)
    """
```

### 💾 **3. StorageAgent - 数据存储管理**

**核心职责**: 数据库连接、数据保存、查询、备份

**工具配置** (5个工具):

```python
@tool
def connect_mysql_database(connection_config: str) -> str:
    """
    连接MySQL云端数据库。

    Args:
        connection_config: 连接配置 (host, port, user, password, database)

    Returns:
        连接状态和数据库信息

    Example:
        connect_mysql_database("host=xxx.com,port=3306,user=admin,db=crawler")
    """

@tool
def save_data_to_mysql(data: str, table_name: str, operation: str = "insert") -> str:
    """
    保存数据到MySQL数据库。

    Args:
        data: 格式化的数据
        table_name: 目标表名
        operation: 操作类型 (insert, update, upsert)

    Returns:
        保存结果和影响行数

    Example:
        save_data_to_mysql(product_data, "products", "insert")
    """

@tool
def query_mysql_data(query: str, limit: int = 100) -> str:
    """
    查询MySQL数据库数据。

    Args:
        query: SQL查询语句
        limit: 结果限制数量

    Returns:
        查询结果

    Example:
        query_mysql_data("SELECT * FROM products WHERE price > 1000", 50)
    """

@tool
def backup_mysql_data(table_name: str, backup_path: str) -> str:
    """
    备份MySQL数据。

    Args:
        table_name: 要备份的表名
        backup_path: 备份文件路径

    Returns:
        备份结果和文件信息

    Example:
        backup_mysql_data("products", "/backup/products_20250131.sql")
    """

@tool
def verify_data_integrity(table_name: str, expected_count: int) -> str:
    """
    验证数据完整性。

    Args:
        table_name: 表名
        expected_count: 期望的数据条数

    Returns:
        完整性验证结果

    Example:
        verify_data_integrity("products", 1250)
    """
```

### 📢 **4. NotificationAgent - 消息通知管理**

**核心职责**: 飞书消息发送、通知模板、状态管理

**工具配置** (5个工具):

```python
@tool
def send_feishu_message(message: str, message_type: str = "text", webhook_url: str = None) -> str:
    """
    发送飞书消息通知。

    Args:
        message: 消息内容
        message_type: 消息类型 (text, card, image)
        webhook_url: 可选的webhook地址

    Returns:
        发送结果和消息ID

    Example:
        send_feishu_message("数据采集任务完成", "text")
    """

@tool
def create_notification_template(template_type: str, data: str) -> str:
    """
    创建通知消息模板。

    Args:
        template_type: 模板类型 (task_start, task_complete, error, progress)
        data: 模板数据

    Returns:
        格式化的消息模板

    Example:
        create_notification_template("task_complete", "采集1250条商品数据")
    """

@tool
def send_progress_notification(stage: str, progress: str, details: str = "") -> str:
    """
    发送进度通知。

    Args:
        stage: 当前阶段
        progress: 进度信息
        details: 详细信息

    Returns:
        通知发送结果

    Example:
        send_progress_notification("数据采集", "50%", "已采集625条数据")
    """

@tool
def send_error_alert(error_type: str, error_message: str, severity: str = "medium") -> str:
    """
    发送错误告警。

    Args:
        error_type: 错误类型
        error_message: 错误信息
        severity: 严重程度 (low, medium, high, critical)

    Returns:
        告警发送结果

    Example:
        send_error_alert("数据库连接", "连接超时", "high")
    """

@tool
def manage_notification_status(action: str, notification_id: str = None) -> str:
    """
    管理通知状态。

    Args:
        action: 操作类型 (enable, disable, check_status)
        notification_id: 通知ID

    Returns:
        状态管理结果

    Example:
        manage_notification_status("check_status", "msg_123456")
    """
```

### 📈 **5. MonitorAgent - 监控和异常处理**

**核心职责**: 性能监控、异常检测、重试机制、日志记录

**工具配置** (6个工具):

```python
@tool
def monitor_system_performance(metrics: str = "all") -> str:
    """
    监控系统性能指标。

    Args:
        metrics: 监控指标 (all, cpu, memory, network, response_time)

    Returns:
        性能监控报告

    Example:
        monitor_system_performance("response_time")
    """

@tool
def detect_system_anomalies(threshold_config: str) -> str:
    """
    检测系统异常。

    Args:
        threshold_config: 异常阈值配置

    Returns:
        异常检测结果

    Example:
        detect_system_anomalies("response_time>5s,error_rate>5%")
    """

@tool
def execute_retry_mechanism(failed_operation: str, max_retries: int = 3, retry_delay: int = 5) -> str:
    """
    执行重试机制。

    Args:
        failed_operation: 失败的操作描述
        max_retries: 最大重试次数
        retry_delay: 重试延迟(秒)

    Returns:
        重试执行结果

    Example:
        execute_retry_mechanism("数据库连接失败", 3, 5)
    """

@tool
def log_system_events(event_type: str, event_data: str, log_level: str = "info") -> str:
    """
    记录系统事件日志。

    Args:
        event_type: 事件类型 (task_start, task_end, error, warning)
        event_data: 事件数据
        log_level: 日志级别 (debug, info, warning, error, critical)

    Returns:
        日志记录结果

    Example:
        log_system_events("task_start", "开始采集京东商品数据", "info")
    """

@tool
def generate_monitoring_report(time_range: str, report_type: str = "summary") -> str:
    """
    生成监控报告。

    Args:
        time_range: 时间范围 (1h, 24h, 7d, 30d)
        report_type: 报告类型 (summary, detailed, performance, errors)

    Returns:
        监控报告

    Example:
        generate_monitoring_report("24h", "summary")
    """

@tool
def handle_critical_exception(exception_info: str, escalation_level: str = "auto") -> str:
    """
    处理严重异常。

    Args:
        exception_info: 异常信息
        escalation_level: 升级级别 (auto, manual, immediate)

    Returns:
        异常处理结果

    Example:
        handle_critical_exception("数据库连接完全失败", "immediate")
    """
```

### 🤖 **6. CrawlerAgent - 爬虫协调专家** (现有)

**核心职责**: 数据采集协调、浏览器操作编排

**工具配置** (2个委托工具):

```python
# 现有委托工具 - 保持不变
smart_element_finder          # → ElementAgent (DOM分析)
smart_browser_action_finder   # → BrowserAgent (浏览器操作)
```

### 🌐 **7. BrowserAgent - 浏览器专家** (现有)

**核心职责**: 浏览器操作、页面导航、截图

**工具配置** (7个浏览器工具):

```python
# 现有浏览器工具 - 保持不变
navigate_browser             # 页面导航
take_screenshot             # 截图捕获
click_element              # 元素点击
type_text                  # 文本输入
hover_element              # 鼠标悬停
get_page_info              # 页面信息
analyze_page_visual        # 视觉分析
```

### 🔍 **8. ElementAgent - DOM分析专家** (现有)

**核心职责**: DOM分析、元素发现、精确定位

**工具配置** (11个DOM工具):

```python
# 现有DOM工具 - 保持不变
dom_get_interactive_elements_smart    # 智能交互元素获取
dom_get_all_elements_basic           # 基础元素获取
dom_get_forms_and_inputs            # 表单和输入元素
dom_get_clickable_elements          # 可点击元素
dom_get_text_content               # 文本内容提取
dom_get_links_and_navigation       # 链接和导航
dom_get_media_elements             # 媒体元素
dom_get_data_elements              # 数据元素
dom_get_semantic_structure         # 语义结构
dom_get_page_metadata              # 页面元数据
dom_analyze_element_similarity     # 元素相似性分析
```

---

## 📊 **工具配置统计表**

| Agent | 工具数量 | 主要工具类型 | 委托关系 |
|-------|----------|-------------|----------|
| **TaskAgent** | 8个 | 任务分析(3) + 委托工具(5) | 协调所有Agent |
| **DataAgent** | 6个 | 数据处理专用工具 | 被TaskAgent委托 |
| **StorageAgent** | 5个 | 数据库操作专用工具 | 被TaskAgent委托 |
| **NotificationAgent** | 5个 | 飞书通知专用工具 | 被TaskAgent委托 |
| **MonitorAgent** | 6个 | 监控异常专用工具 | 被TaskAgent委托 |
| **CrawlerAgent** | 2个 | 委托工具 | 协调Browser+Element |
| **BrowserAgent** | 7个 | 浏览器操作工具 | 被Crawler委托 |
| **ElementAgent** | 11个 | DOM分析工具 | 被Crawler委托 |
| **总计** | **50个** | **8个Agent** | **2层委托架构** |

## 🔄 **扩展委托工具设计**

### **新增委托工具实现**

```python
# 扩展 src/iicrawlermcp/tools/delegation_tools.py

@tool
def smart_data_processor(data_operation: str) -> str:
    """
    智能数据处理委托工具。

    Args:
        data_operation: 数据处理操作描述

    Returns:
        数据处理结果

    Example:
        smart_data_processor("清洗商品数据并映射到MySQL格式")
    """
    try:
        from ..agents.data_agent import build_data_agent

        logger.info(f"Smart data processing: {data_operation}")

        data_agent = build_data_agent()
        result = data_agent.invoke(data_operation)

        output = result.get('output', str(result))
        logger.info(f"Smart data processing completed")
        return output

    except Exception as e:
        error_msg = f"Smart data processing failed: {str(e)}"
        logger.error(error_msg)
        return f"❌ {error_msg}"

@tool
def smart_storage_manager(storage_operation: str) -> str:
    """
    智能存储管理委托工具。

    Args:
        storage_operation: 存储操作描述

    Returns:
        存储操作结果

    Example:
        smart_storage_manager("保存商品数据到products表")
    """
    try:
        from ..agents.storage_agent import build_storage_agent

        logger.info(f"Smart storage management: {storage_operation}")

        storage_agent = build_storage_agent()
        result = storage_agent.invoke(storage_operation)

        output = result.get('output', str(result))
        logger.info(f"Smart storage management completed")
        return output

    except Exception as e:
        error_msg = f"Smart storage management failed: {str(e)}"
        logger.error(error_msg)
        return f"❌ {error_msg}"

@tool
def smart_notification_sender(notification_request: str) -> str:
    """
    智能通知发送委托工具。

    Args:
        notification_request: 通知请求描述

    Returns:
        通知发送结果

    Example:
        smart_notification_sender("发送任务完成通知到飞书")
    """
    try:
        from ..agents.notification_agent import build_notification_agent

        logger.info(f"Smart notification sending: {notification_request}")

        notification_agent = build_notification_agent()
        result = notification_agent.invoke(notification_request)

        output = result.get('output', str(result))
        logger.info(f"Smart notification sending completed")
        return output

    except Exception as e:
        error_msg = f"Smart notification sending failed: {str(e)}"
        logger.error(error_msg)
        return f"❌ {error_msg}"

@tool
def smart_monitor_checker(monitoring_request: str) -> str:
    """
    智能监控检查委托工具。

    Args:
        monitoring_request: 监控请求描述

    Returns:
        监控检查结果

    Example:
        smart_monitor_checker("检查系统性能并处理异常")
    """
    try:
        from ..agents.monitor_agent import build_monitor_agent

        logger.info(f"Smart monitoring check: {monitoring_request}")

        monitor_agent = build_monitor_agent()
        result = monitor_agent.invoke(monitoring_request)

        output = result.get('output', str(result))
        logger.info(f"Smart monitoring check completed")
        return output

    except Exception as e:
        error_msg = f"Smart monitoring check failed: {str(e)}"
        logger.error(error_msg)
        return f"❌ {error_msg}"

# 更新委托工具列表
DELEGATION_TOOLS = [
    # 现有工具
    smart_element_finder,             # → ElementAgent
    smart_browser_action_finder,      # → BrowserAgent

    # 新增工具
    smart_data_processor,             # → DataAgent
    smart_storage_manager,            # → StorageAgent
    smart_notification_sender,        # → NotificationAgent
    smart_monitor_checker,            # → MonitorAgent
]
```

---

## 🛠️ **工具实现要点**

### **1. 工具设计最佳实践**

基于LangChain官方文档和现有代码分析，所有工具应遵循以下设计原则：

```python
# 标准工具模板
@tool
def tool_name(param1: str, param2: int = 100) -> str:
    """
    工具功能描述 - 简洁明确。

    Args:
        param1: 参数1描述
        param2: 参数2描述，默认值100

    Returns:
        返回值描述

    Example:
        tool_name("示例参数", 200)
    """
    try:
        # 核心逻辑实现
        logger.info(f"Tool execution: {param1}")

        # 使用run_browser_call处理浏览器相关操作
        if needs_browser:
            result = run_browser_call(lambda: browser_operation())

        # 返回结构化结果
        return f"✅ 操作成功: {result}"

    except Exception as e:
        error_msg = f"Tool execution failed: {str(e)}"
        logger.error(error_msg)
        return f"❌ {error_msg}"
```

### **2. 异步支持和线程安全**

```python
# 对于需要异步支持的工具
from ..core.browser_thread import run_browser_call

@tool
async def async_tool_example(data: str) -> str:
    """异步工具示例"""
    try:
        # 使用run_browser_call确保线程安全
        result = await run_browser_call(lambda: sync_operation(data))
        return f"✅ 异步操作完成: {result}"
    except Exception as e:
        return f"❌ 异步操作失败: {str(e)}"
```

### **3. 错误处理和重试机制**

```python
@tool
def robust_tool_example(operation: str, max_retries: int = 3) -> str:
    """带重试机制的工具示例"""
    for attempt in range(max_retries):
        try:
            result = execute_operation(operation)
            return f"✅ 操作成功 (尝试 {attempt + 1}): {result}"
        except Exception as e:
            if attempt == max_retries - 1:
                return f"❌ 操作失败 (已重试 {max_retries} 次): {str(e)}"
            logger.warning(f"尝试 {attempt + 1} 失败，正在重试: {str(e)}")
            time.sleep(2 ** attempt)  # 指数退避
```

### **4. 配置管理集成**

```python
# 扩展配置类以支持新Agent
class Config:
    # 现有配置...

    # MySQL数据库配置
    MYSQL_HOST: str = os.getenv("MYSQL_HOST", "")
    MYSQL_PORT: int = int(os.getenv("MYSQL_PORT", "3306"))
    MYSQL_USER: str = os.getenv("MYSQL_USER", "")
    MYSQL_PASSWORD: str = os.getenv("MYSQL_PASSWORD", "")
    MYSQL_DATABASE: str = os.getenv("MYSQL_DATABASE", "")

    # 飞书配置
    FEISHU_APP_ID: str = os.getenv("FEISHU_APP_ID", "")
    FEISHU_APP_SECRET: str = os.getenv("FEISHU_APP_SECRET", "")
    FEISHU_WEBHOOK_URL: str = os.getenv("FEISHU_WEBHOOK_URL", "")

    # 监控配置
    MONITOR_INTERVAL: int = int(os.getenv("MONITOR_INTERVAL", "60"))
    LOG_LEVEL: str = os.getenv("LOG_LEVEL", "INFO")
    MAX_RETRY_ATTEMPTS: int = int(os.getenv("MAX_RETRY_ATTEMPTS", "3"))
```

### **5. 工具文件组织结构**

```
src/iicrawlermcp/tools/
├── __init__.py
├── delegation_tools.py          # 委托工具 (现有 + 扩展)
├── browser_tools.py             # 浏览器工具 (现有)
├── dom_tools_enhanced.py        # DOM工具 (现有)
├── task_tools.py               # TaskAgent专用工具 (新增)
├── data_tools.py               # DataAgent专用工具 (新增)
├── storage_tools.py            # StorageAgent专用工具 (新增)
├── notification_tools.py       # NotificationAgent专用工具 (新增)
├── monitor_tools.py            # MonitorAgent专用工具 (新增)
└── utils/
    ├── mysql_connector.py      # MySQL连接工具
    ├── feishu_client.py        # 飞书API客户端
    └── monitoring_utils.py     # 监控工具函数
```

### **6. 依赖包需求**

```python
# requirements.txt 新增依赖
pymysql>=1.0.2              # MySQL连接
requests>=2.28.0            # HTTP请求 (飞书API)
psutil>=5.9.0              # 系统监控
schedule>=1.2.0            # 定时任务
python-dotenv>=1.0.0       # 环境变量 (已有)
pydantic>=2.0.0            # 数据验证 (已有)
```

### **7. 实现优先级建议**

**Phase 1 - 核心扩展** (优先实现):
1. ✅ 扩展委托工具 (`delegation_tools.py`)
2. 🔧 TaskAgent工具 (`task_tools.py`)
3. 📊 DataAgent工具 (`data_tools.py`)

**Phase 2 - 存储和通知** (次优先):
4. 💾 StorageAgent工具 (`storage_tools.py`)
5. 📢 NotificationAgent工具 (`notification_tools.py`)

**Phase 3 - 监控和优化** (最后实现):
6. 📈 MonitorAgent工具 (`monitor_tools.py`)
7. 🔧 性能优化和监控集成

### **8. 测试策略**

```python
# 工具测试示例
import pytest
from unittest.mock import patch, MagicMock

def test_data_cleaning_tool():
    """测试数据清洗工具"""
    raw_data = "iPhone 15 Pro Max\n\n¥9999.00\n12580评价"
    result = clean_raw_data(raw_data, "product")

    assert "iPhone 15 Pro Max" in result
    assert "9999.00" in result
    assert "12580" in result

@patch('src.iicrawlermcp.tools.storage_tools.mysql_connection')
def test_mysql_save_tool(mock_mysql):
    """测试MySQL保存工具"""
    mock_mysql.return_value.execute.return_value = 1

    result = save_data_to_mysql("test_data", "products")
    assert "✅" in result
    assert "1" in result
```

### 💾 **3. StorageAgent - 数据存储管理**

**核心职责**: 数据库连接、数据保存、查询、备份

**工具配置** (5个工具):

```python
@tool
def connect_mysql_database(connection_config: str) -> str:
    """
    连接MySQL云端数据库。

    Args:
        connection_config: 连接配置 (host, port, user, password, database)

    Returns:
        连接状态和数据库信息

    Example:
        connect_mysql_database("host=xxx.com,port=3306,user=admin,db=crawler")
    """

@tool
def save_data_to_mysql(data: str, table_name: str, operation: str = "insert") -> str:
    """
    保存数据到MySQL数据库。

    Args:
        data: 格式化的数据
        table_name: 目标表名
        operation: 操作类型 (insert, update, upsert)

    Returns:
        保存结果和影响行数

    Example:
        save_data_to_mysql(product_data, "products", "insert")
    """

@tool
def query_mysql_data(query: str, limit: int = 100) -> str:
    """
    查询MySQL数据库数据。

    Args:
        query: SQL查询语句
        limit: 结果限制数量

    Returns:
        查询结果

    Example:
        query_mysql_data("SELECT * FROM products WHERE price > 1000", 50)
    """

@tool
def backup_mysql_data(table_name: str, backup_path: str) -> str:
    """
    备份MySQL数据。

    Args:
        table_name: 要备份的表名
        backup_path: 备份文件路径

    Returns:
        备份结果和文件信息

    Example:
        backup_mysql_data("products", "/backup/products_20250131.sql")
    """

@tool
def verify_data_integrity(table_name: str, expected_count: int) -> str:
    """
    验证数据完整性。

    Args:
        table_name: 表名
        expected_count: 期望的数据条数

    Returns:
        完整性验证结果

    Example:
        verify_data_integrity("products", 1250)
    """
```

### 📢 **4. NotificationAgent - 消息通知管理**

**核心职责**: 飞书消息发送、通知模板、状态管理

**工具配置** (5个工具):

```python
@tool
def send_feishu_message(message: str, message_type: str = "text", webhook_url: str = None) -> str:
    """
    发送飞书消息通知。

    Args:
        message: 消息内容
        message_type: 消息类型 (text, card, image)
        webhook_url: 可选的webhook地址

    Returns:
        发送结果和消息ID

    Example:
        send_feishu_message("数据采集任务完成", "text")
    """

@tool
def create_notification_template(template_type: str, data: str) -> str:
    """
    创建通知消息模板。

    Args:
        template_type: 模板类型 (task_start, task_complete, error, progress)
        data: 模板数据

    Returns:
        格式化的消息模板

    Example:
        create_notification_template("task_complete", "采集1250条商品数据")
    """

@tool
def send_progress_notification(stage: str, progress: str, details: str = "") -> str:
    """
    发送进度通知。

    Args:
        stage: 当前阶段
        progress: 进度信息
        details: 详细信息

    Returns:
        通知发送结果

    Example:
        send_progress_notification("数据采集", "50%", "已采集625条数据")
    """

@tool
def send_error_alert(error_type: str, error_message: str, severity: str = "medium") -> str:
    """
    发送错误告警。

    Args:
        error_type: 错误类型
        error_message: 错误信息
        severity: 严重程度 (low, medium, high, critical)

    Returns:
        告警发送结果

    Example:
        send_error_alert("数据库连接", "连接超时", "high")
    """

@tool
def manage_notification_status(action: str, notification_id: str = None) -> str:
    """
    管理通知状态。

    Args:
        action: 操作类型 (enable, disable, check_status)
        notification_id: 通知ID

    Returns:
        状态管理结果

    Example:
        manage_notification_status("check_status", "msg_123456")
    """
```
```

### 🔄 **完整工作流程设计**

​```python
# 完整数据采集流程
用户输入需求
    ↓
TaskAgent (需求理解 + 信息询问)
    ↓ [人工确认]
TaskAgent (生成采集计划)
    ↓ [人工审核]
CrawlerAgent (执行数据采集)
    ↓ [复杂页面人工确认]
DataAgent (数据处理 + 生成示例)
    ↓ [人工确认数据示例]
StorageAgent (保存到云端数据库)
    ↓
NotificationAgent (发送完成通知)
    ↓
MonitorAgent (全程监控 + 异常处理)
```

### 🛠️ **技术实现规划**

#### **扩展委托工具**
```python
# 扩展现有的委托工具
DELEGATION_TOOLS = [
    # 现有工具
    smart_element_finder,        # → ElementAgent
    smart_browser_action_finder, # → BrowserAgent

    # 新增工具
    smart_task_planner,         # → TaskAgent
    smart_data_processor,       # → DataAgent
    smart_storage_manager,      # → StorageAgent
    smart_notification_sender,  # → NotificationAgent
    smart_monitor_checker,      # → MonitorAgent
]
```

#### **数据库集成配置**
```python
# 扩展配置类
class Config:
    # 现有配置...

    # 数据库配置
    MYSQL_HOST: str = os.getenv("MYSQL_HOST", "")
    MYSQL_PORT: int = int(os.getenv("MYSQL_PORT", "3306"))
    MYSQL_USER: str = os.getenv("MYSQL_USER", "")
    MYSQL_PASSWORD: str = os.getenv("MYSQL_PASSWORD", "")
    MYSQL_DATABASE: str = os.getenv("MYSQL_DATABASE", "")

    # 飞书配置
    FEISHU_APP_ID: str = os.getenv("FEISHU_APP_ID", "")
    FEISHU_APP_SECRET: str = os.getenv("FEISHU_APP_SECRET", "")
    FEISHU_WEBHOOK_URL: str = os.getenv("FEISHU_WEBHOOK_URL", "")
```

#### **并发处理能力**
```python
# 异步处理提高并发能力
import asyncio
from concurrent.futures import ThreadPoolExecutor

class ConcurrentTaskManager:
    def __init__(self, max_workers=5):
        self.executor = ThreadPoolExecutor(max_workers=max_workers)

    async def execute_parallel_tasks(self, tasks):
        loop = asyncio.get_event_loop()
        futures = [
            loop.run_in_executor(self.executor, task)
            for task in tasks
        ]
        return await asyncio.gather(*futures)
```
