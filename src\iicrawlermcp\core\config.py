"""
Configuration module for iICrawlerMCP.

This module handles environment variables and application configuration.
"""

import os
from pathlib import Path
from typing import Optional
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()


class Config:
    """Configuration class for managing application settings."""

    # OpenAI/LLM Configuration
    # OPENAI_API_KEY: str = os.getenv("OPENAI_API_KEY", "")
    # OPENAI_API_BASE: Optional[str] = os.getenv("OPENAI_API_BASE")
    # OPENAI_MODEL: str = os.getenv("OPENAI_MODEL", "859-gpt-4_1-mini__2025-04-14")
    # DEEPSEEK/LLM Configuration
    OPENAI_API_KEY: str = os.getenv("DEEPSEEK_API_KEY", "")
    OPENAI_API_BASE: Optional[str] = os.getenv("DEEPSEEK_API_BASE")
    OPENAI_MODEL: str = os.getenv("DEEPSEEK_MODEL", "deepseek-chat")

    # Browser Configuration
    HEADLESS: bool = os.getenv("HEADLESS", "true").lower() == "true"
    BROWSER_TIMEOUT: int = int(os.getenv("BROWSER_TIMEOUT", "60000"))

    # Path Configuration
    _PROJECT_ROOT: Optional[Path] = None
    DEFAULT_SCREENSHOT_PATH: str = os.getenv("DEFAULT_SCREENSHOT_PATH", "screenshots/screenshot.png")
    VERBOSE: bool = os.getenv("VERBOSE", "true").lower() == "true"

    # DOM Highlight Configuration
    DOM_HIGHLIGHT_ENABLED: bool = os.getenv("DOM_HIGHLIGHT_ENABLED", "true").lower() == "true"
    DOM_HIGHLIGHT_BORDER_WIDTH: int = int(os.getenv("DOM_HIGHLIGHT_BORDER_WIDTH", "2"))
    DOM_HIGHLIGHT_OPACITY: float = float(os.getenv("DOM_HIGHLIGHT_OPACITY", "0.1"))
    DOM_HIGHLIGHT_Z_INDEX: int = int(os.getenv("DOM_HIGHLIGHT_Z_INDEX", "2147483647"))
    DOM_HIGHLIGHT_LABEL_FONT_SIZE_MIN: int = int(os.getenv("DOM_HIGHLIGHT_LABEL_FONT_SIZE_MIN", "8"))
    DOM_HIGHLIGHT_LABEL_FONT_SIZE_MAX: int = int(os.getenv("DOM_HIGHLIGHT_LABEL_FONT_SIZE_MAX", "12"))
    DOM_HIGHLIGHT_LABEL_PADDING: str = os.getenv("DOM_HIGHLIGHT_LABEL_PADDING", "1px 4px")
    DOM_HIGHLIGHT_LABEL_BORDER_RADIUS: str = os.getenv("DOM_HIGHLIGHT_LABEL_BORDER_RADIUS", "4px")
    DOM_HIGHLIGHT_COLORS: str = os.getenv("DOM_HIGHLIGHT_COLORS",
        "#FF6B6B,#4ECDC4,#45B7D1,#96CEB4,#FFEAA7,#DDA0DD,#98D8C8,#F7DC6F,#BB8FCE,#85C1E9,#F8C471,#82E0AA,#F1948A,#85C1E9,#D7BDE2,#A3E4D7,#DC143C,#4682B4")

    # DOM Extraction Configuration
    DOM_FOCUS_HIGHLIGHT_INDEX: int = int(os.getenv("DOM_FOCUS_HIGHLIGHT_INDEX", "-1"))
    DOM_VIEWPORT_EXPANSION: int = int(os.getenv("DOM_VIEWPORT_EXPANSION", "0"))
    DOM_DEBUG_MODE: bool = os.getenv("DOM_DEBUG_MODE", "false").lower() == "true"
    
    @classmethod
    def get_project_root(cls) -> Path:
        """
        Get the project root directory.

        Returns:
            Path to the project root directory
        """
        if cls._PROJECT_ROOT is None:
            # Start from current file and go up to find project root
            current_path = Path(__file__).resolve()

            # Look for project markers (pyproject.toml, setup.py, .git, etc.)
            for parent in current_path.parents:
                if any((parent / marker).exists() for marker in [
                    'pyproject.toml', 'setup.py', '.git', 'requirements.txt'
                ]):
                    cls._PROJECT_ROOT = parent
                    break
            else:
                # Fallback: assume project root is 3 levels up from this file
                # src/iicrawlermcp/core/config.py -> project_root
                cls._PROJECT_ROOT = current_path.parents[2]

        return cls._PROJECT_ROOT

    @classmethod
    def get_screenshots_dir(cls, subdir: str = "") -> Path:
        """
        Get the screenshots directory path.

        Args:
            subdir: Optional subdirectory within screenshots (e.g., 'examples', 'tests')

        Returns:
            Path to the screenshots directory
        """
        screenshots_dir = cls.get_project_root() / "screenshots"
        if subdir:
            screenshots_dir = screenshots_dir / subdir
        return screenshots_dir

    @classmethod
    def validate(cls) -> None:
        """Validate required configuration values."""
        if not cls.OPENAI_API_KEY:
            raise ValueError("OPENAI_API_KEY environment variable is required")

        # Validate DOM highlight configuration
        if cls.DOM_HIGHLIGHT_BORDER_WIDTH <= 0:
            raise ValueError("DOM_HIGHLIGHT_BORDER_WIDTH must be positive")

        if not (0.0 <= cls.DOM_HIGHLIGHT_OPACITY <= 1.0):
            raise ValueError("DOM_HIGHLIGHT_OPACITY must be between 0.0 and 1.0")

        if cls.DOM_HIGHLIGHT_LABEL_FONT_SIZE_MIN <= 0 or cls.DOM_HIGHLIGHT_LABEL_FONT_SIZE_MAX <= 0:
            raise ValueError("DOM highlight font sizes must be positive")

        if cls.DOM_HIGHLIGHT_LABEL_FONT_SIZE_MIN > cls.DOM_HIGHLIGHT_LABEL_FONT_SIZE_MAX:
            raise ValueError("DOM_HIGHLIGHT_LABEL_FONT_SIZE_MIN must be <= DOM_HIGHLIGHT_LABEL_FONT_SIZE_MAX")

        # Validate DOM extraction configuration
        if cls.DOM_VIEWPORT_EXPANSION < 0:
            raise ValueError("DOM_VIEWPORT_EXPANSION must be non-negative")

        # DOM_FOCUS_HIGHLIGHT_INDEX can be any integer (including negative for "no focus")
        # DOM_DEBUG_MODE is boolean, no validation needed

        print(f"[OK] Configuration validated successfully")
        print(f"   - Model: {cls.OPENAI_MODEL}")
        print(f"   - Headless: {cls.HEADLESS}")
        print(f"   - DOM Highlights: {cls.DOM_HIGHLIGHT_ENABLED}")
        print(f"   - Debug Mode: {cls.DOM_DEBUG_MODE}")
        print(f"   - Viewport Expansion: {cls.DOM_VIEWPORT_EXPANSION}px")
        if cls.OPENAI_API_BASE:
            print(f"   - API Base: {cls.OPENAI_API_BASE}")
    
    @classmethod
    def get_llm_config(cls) -> dict:
        """Get LLM configuration as a dictionary."""
        config = {
            "model": cls.OPENAI_MODEL,
            "temperature": 0,
        }
        
        if cls.OPENAI_API_BASE:
            config["base_url"] = cls.OPENAI_API_BASE
            
        return config
    
    @classmethod
    def get_browser_config(cls) -> dict:
        """Get browser configuration as a dictionary."""
        return {
            "headless": cls.HEADLESS,
            "timeout": cls.BROWSER_TIMEOUT,
        }

    @classmethod
    def get_highlight_config(cls) -> dict:
        """
        Get DOM highlight configuration as a dictionary for JavaScript.

        Returns:
            Dictionary containing highlight configuration parameters
        """
        # Parse colors from comma-separated string
        colors = [color.strip() for color in cls.DOM_HIGHLIGHT_COLORS.split(',')]

        return {
            'doHighlightElements': cls.DOM_HIGHLIGHT_ENABLED,
            'borderWidth': cls.DOM_HIGHLIGHT_BORDER_WIDTH,
            'opacity': cls.DOM_HIGHLIGHT_OPACITY,
            'zIndex': cls.DOM_HIGHLIGHT_Z_INDEX,
            'labelFontSizeMin': cls.DOM_HIGHLIGHT_LABEL_FONT_SIZE_MIN,
            'labelFontSizeMax': cls.DOM_HIGHLIGHT_LABEL_FONT_SIZE_MAX,
            'labelPadding': cls.DOM_HIGHLIGHT_LABEL_PADDING,
            'labelBorderRadius': cls.DOM_HIGHLIGHT_LABEL_BORDER_RADIUS,
            'colors': colors
        }

    @classmethod
    def get_dom_extraction_config(cls) -> dict:
        """
        Get DOM extraction configuration as a dictionary for JavaScript.

        Returns:
            Dictionary containing DOM extraction configuration parameters
        """
        return {
            'focusHighlightIndex': cls.DOM_FOCUS_HIGHLIGHT_INDEX,
            'viewportExpansion': cls.DOM_VIEWPORT_EXPANSION,
            'debugMode': cls.DOM_DEBUG_MODE
        }


# Global configuration instance
config = Config()
