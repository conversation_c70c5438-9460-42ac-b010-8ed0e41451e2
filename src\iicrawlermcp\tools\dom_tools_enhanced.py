"""
Enhanced DOM tools with layered architecture.

This module implements the layered DOM element extraction approach:
- Foundation Layer: Basic element extraction
- Classification Layer: Element type classification  
- Functional Layer: User intent-based classification

All functions maintain the existing return format using DOMFormatter.
"""

import logging
from typing import List, Optional
from langchain_core.tools import tool

from ..core.browser import get_global_browser
from ..dom.core.extractor import DOMExtractor
from ..dom.formatters.element_formatters import DOMFormatter
from ..core.browser_thread import run_browser_call

# --- Patch DOMExtractor.extract_all_elements to always execute on browser thread ---
if not hasattr(DOMExtractor, "_thread_safe_patch_applied"):

    _orig_extract = DOMExtractor.extract_all_elements

    def _safe_extract(self):  # type: ignore[override]
        return run_browser_call(lambda: _orig_extract(self))

    DOMExtractor.extract_all_elements = _safe_extract  # type: ignore[assignment]
    DOMExtractor._thread_safe_patch_applied = True  # type: ignore[attr-defined]


logger = logging.getLogger(__name__)


def _get_dom_extractor() -> DOMExtractor:
    """Get DOMExtractor created within the browser worker thread."""

    from ..core.browser_thread import run_browser_call

    def _factory():
        browser = get_global_browser()
        if not browser._is_initialized:
            browser._initialize()
        return DOMExtractor(browser)

    return run_browser_call(_factory)


# =============================================================================
# FOUNDATION LAYER - Basic element extraction
# =============================================================================


# =============================================================================
# CLASSIFICATION LAYER - By HTML tag type
# =============================================================================

@tool
def dom_get_buttons_enhanced(
        include_disabled: bool = False,
        include_hidden: bool = False,
        require_text: bool = True
) -> str:
    """
    Get all button elements with enhanced filtering options.
    
    Includes:
    - <button> tags
    - <input type="button|submit|reset">
    - role="button" elements
    
    Args:
        include_disabled: Whether to include disabled buttons
        include_hidden: Whether to include hidden buttons
        require_text: Whether to require text content or identifying attributes
        
    Returns:
        A formatted string listing all button elements.
    """
    try:
        extractor = _get_dom_extractor()
        all_elements = extractor.extract_all_elements()

        buttons = []
        for elem in all_elements:
            # Visibility filter
            if not include_hidden and not elem.is_visible:
                continue

            tag_name = elem.tag_name.lower()
            is_button = False

            # Standard button elements
            if tag_name == 'button':
                is_button = True
            elif tag_name == 'input' and elem.attributes.get('type', '').lower() in ['button', 'submit', 'reset']:
                is_button = True
            elif elem.attributes.get('role') == 'button':
                is_button = True

            if is_button:
                # Disabled filter
                is_disabled = elem.attributes.get('disabled') is not None
                if not include_disabled and is_disabled:
                    continue

                # Text requirement filter
                if require_text:
                    has_text = bool(elem.text_content and elem.text_content.strip())
                    has_value = bool(elem.attributes.get('value', '').strip())
                    has_title = bool(elem.attributes.get('title', '').strip())
                    has_aria_label = bool(elem.attributes.get('aria-label', '').strip())

                    if not (has_text or has_value or has_title or has_aria_label):
                        continue

                buttons.append(elem)

        result = DOMFormatter.format_buttons(buttons)
        logger.info(f"DOM get_buttons_enhanced: Found {len(buttons)} buttons")
        return result

    except Exception as e:
        error_msg = f"Failed to get buttons: {str(e)}"
        logger.error(f"DOM get_buttons_enhanced error: {error_msg}")
        return f"❌ {error_msg}"


@tool
def dom_get_inputs_enhanced(
        input_types: Optional[List[str]] = None,
        include_disabled: bool = False,
        include_readonly: bool = True
) -> str:
    """
    Get all input elements with enhanced filtering options.
    
    Includes:
    - <input> various types
    - <textarea>
    - <select>
    - contenteditable elements
    
    Args:
        input_types: List of input types to include (e.g., ['text', 'email', 'password'])
        include_disabled: Whether to include disabled inputs
        include_readonly: Whether to include readonly inputs
        
    Returns:
        A formatted string listing all input elements.
    """
    try:
        extractor = _get_dom_extractor()
        all_elements = extractor.extract_all_elements()

        inputs = []
        for elem in all_elements:
            if not elem.is_visible:
                continue

            tag_name = elem.tag_name.lower()
            is_input = False

            # Standard input elements
            if tag_name in ['input', 'textarea', 'select']:
                is_input = True
            elif elem.attributes.get('contenteditable') == 'true':
                is_input = True
            elif elem.attributes.get('role') in ['textbox', 'combobox', 'searchbox']:
                is_input = True

            if is_input:
                # Type filter
                if input_types and tag_name == 'input':
                    input_type = elem.attributes.get('type', 'text').lower()
                    if input_type not in input_types:
                        continue

                # Disabled filter
                if not include_disabled and elem.attributes.get('disabled') is not None:
                    continue

                # Readonly filter
                if not include_readonly and elem.attributes.get('readonly') is not None:
                    continue

                inputs.append(elem)

        result = DOMFormatter.format_inputs(inputs)
        logger.info(f"DOM get_inputs_enhanced: Found {len(inputs)} inputs")
        return result

    except Exception as e:
        error_msg = f"Failed to get inputs: {str(e)}"
        logger.error(f"DOM get_inputs_enhanced error: {error_msg}")
        return f"❌ {error_msg}"


@tool
def dom_get_links_enhanced(
        require_href: bool = True,
        include_anchors: bool = False,
        external_only: bool = False
) -> str:
    """
    Get all link elements with enhanced filtering options.
    
    Includes:
    - <a> tags
    - role="link" elements
    
    Args:
        require_href: Whether to require href attribute
        include_anchors: Whether to include page anchors (#section)
        external_only: Whether to include only external links
        
    Returns:
        A formatted string listing all link elements.
    """
    try:
        extractor = _get_dom_extractor()
        all_elements = extractor.extract_all_elements()

        links = []
        for elem in all_elements:
            if not elem.is_visible:
                continue

            tag_name = elem.tag_name.lower()
            is_link = False

            if tag_name == 'a':
                is_link = True
            elif elem.attributes.get('role') == 'link':
                is_link = True
            if is_link:
                href = elem.attributes.get('href', '')

                # Href requirement filter
                if require_href and not href:
                    continue

                # Anchor filter
                if not include_anchors and href.startswith('#'):
                    continue

                # External link filter
                if external_only:
                    if not href or href.startswith('#') or href.startswith('/') or 'javascript:' in href:
                        continue

                links.append(elem)

        result = DOMFormatter.format_links(links)
        logger.info(f"DOM get_links_enhanced: Found {len(links)} links")
        return result

    except Exception as e:
        error_msg = f"Failed to get links: {str(e)}"
        logger.error(f"DOM get_links_enhanced error: {error_msg}")
        return f"❌ {error_msg}"


# =============================================================================
# CLASSIFICATION LAYER - By functional role
# =============================================================================

@tool
def dom_get_form_elements() -> str:
    """
    Get all form-related elements from the current page.

    Includes:
    - Form containers
    - Form controls (inputs, selects, textareas)
    - Form labels
    - Form buttons

    Returns:
        A formatted string listing all form elements.
    """
    try:
        extractor = _get_dom_extractor()
        all_elements = extractor.extract_all_elements()

        form_elements = []
        for elem in all_elements:
            if not elem.is_visible:
                continue

            tag_name = elem.tag_name.lower()
            is_form_element = False

            # Form containers and controls
            if tag_name in ['form', 'fieldset', 'legend']:
                is_form_element = True
            elif tag_name in ['input', 'textarea', 'select', 'button', 'label']:
                is_form_element = True
            elif elem.attributes.get('role') in ['form', 'group']:
                is_form_element = True

            if is_form_element:
                form_elements.append(elem)

        result = DOMFormatter.format_form_elements(form_elements)
        logger.info(f"DOM get_form_elements: Found {len(form_elements)} form elements")
        return result

    except Exception as e:
        error_msg = f"Failed to get form elements: {str(e)}"
        logger.error(f"DOM get_form_elements error: {error_msg}")
        return f"❌ {error_msg}"


@tool
def dom_get_navigation_elements() -> str:
    """
    Get all navigation-related elements from the current page.

    Includes:
    - <nav> tags
    - role="navigation" elements
    - Menu-related elements
    - Breadcrumb navigation

    Returns:
        A formatted string listing all navigation elements.
    """
    try:
        extractor = _get_dom_extractor()
        all_elements = extractor.extract_all_elements()

        nav_elements = []
        for elem in all_elements:
            if not elem.is_visible:
                continue

            tag_name = elem.tag_name.lower()
            is_nav_element = False

            # Navigation tags
            if tag_name == 'nav':
                is_nav_element = True
            elif elem.attributes.get('role') in ['navigation', 'menubar', 'menu', 'menuitem']:
                is_nav_element = True
            else:
                # Check for navigation classes
                classes = elem.attributes.get('class', '').lower()
                if any(nav_class in classes for nav_class in ['nav', 'menu', 'navigation', 'breadcrumb']):
                    is_nav_element = True

            if is_nav_element:
                nav_elements.append(elem)

        result = DOMFormatter.format_navigation_elements(nav_elements)
        logger.info(f"DOM get_navigation_elements: Found {len(nav_elements)} navigation elements")
        return result

    except Exception as e:
        error_msg = f"Failed to get navigation elements: {str(e)}"
        logger.error(f"DOM get_navigation_elements error: {error_msg}")
        return f"❌ {error_msg}"


@tool
def dom_get_media_elements() -> str:
    """
    Get all media elements from the current page.

    Includes:
    - <img>, <video>, <audio>
    - <canvas>, <svg>
    - Media control buttons

    Returns:
        A formatted string listing all media elements.
    """
    try:
        extractor = _get_dom_extractor()
        all_elements = extractor.extract_all_elements()

        media_elements = []
        for elem in all_elements:
            if not elem.is_visible:
                continue

            tag_name = elem.tag_name.lower()
            is_media_element = False

            # Media tags
            if tag_name in ['img', 'video', 'audio', 'canvas', 'svg']:
                is_media_element = True
            elif tag_name == 'object' and elem.attributes.get('type', '').startswith(('image/', 'video/', 'audio/')):
                is_media_element = True

            if is_media_element:
                media_elements.append(elem)

        result = DOMFormatter.format_media_elements(media_elements)
        logger.info(f"DOM get_media_elements: Found {len(media_elements)} media elements")
        return result

    except Exception as e:
        error_msg = f"Failed to get media elements: {str(e)}"
        logger.error(f"DOM get_media_elements error: {error_msg}")
        return f"❌ {error_msg}"


@tool
def dom_get_content_elements(query_text: str = None, max_results: int = 20, similarity_threshold: float = 0.1) -> str:
    """
    Get all visible elements that contain meaningful text content.

    Uses AI model to directly judge similarity and rank elements when query_text is provided.

    Args:
        query_text: Optional query text for AI-based similarity ranking
        max_results: Maximum number of results to return (default: 20)
        similarity_threshold: Minimum similarity score to include elements (default: 0.1)

    Returns:
        A formatted string listing all visible text content, ranked by AI similarity if query provided.
    """

    def _get_element_basic_info(elem) -> str:
        """获取元素基本信息"""
        tag_name = elem.tag_name.lower() if elem.tag_name else 'unknown'
        info_parts = [tag_name]

        if elem.is_interactive:
            info_parts.append('interactive')

        if hasattr(elem, 'attributes') and elem.attributes:
            attrs = elem.attributes
            if 'href' in attrs or attrs.get('role') in ['button', 'link']:
                info_parts.append('clickable')
            if tag_name in ['input', 'select', 'textarea']:
                input_type = attrs.get('type', 'text')
                info_parts.append(f'type-{input_type}')

        return '|'.join(info_parts)

    def _extract_all_text_content(elem) -> str:
        """提取元素的所有文本内容"""
        texts = []

        # 1. 主要文本内容
        if elem.text_content and elem.text_content.strip():
            texts.append(elem.text_content.strip())

        # 2. 属性文本
        if hasattr(elem, 'attributes') and elem.attributes:
            attrs = elem.attributes
            for attr in ['value', 'placeholder', 'alt', 'title', 'aria-label', 'data-text']:
                if attr in attrs and attrs[attr].strip():
                    texts.append(attrs[attr].strip())

        # 合并所有文本，去重
        all_text = ' '.join(texts)
        # 规范化空白字符
        import re
        return re.sub(r'\s+', ' ', all_text).strip()

    def _ask_ai_for_similarity_ranking(query_text: str, elements_data: list) -> list:
        """让AI模型直接判断相似度并排序"""
        try:
            from langchain_openai import ChatOpenAI
            from ..core.config import config

            # 准备元素数据给AI
            elements_text = ""
            for i, (elem_id, xpath, text, element_info) in enumerate(elements_data):
                elements_text += f"{i + 1}. [ID:{elem_id}] [{element_info}] {text[:200]}...\n"

            prompt = f"""
你是一个智能元素相似度分析专家。请分析以下页面元素与用户查询的相似度。

用户查询: "{query_text}"

页面元素列表:
{elements_text}

请为每个元素评估与查询的相似度(0-1分)，并按相似度从高到低排序。
只返回相似度 >= {similarity_threshold} 的元素，最多返回 {max_results} 个。

输出格式(每行一个元素):
元素ID|相似度分数|简要理由
例如: 5|0.85|包含查询关键词且语义相关

请直接输出结果，不要其他解释:
"""

            llm_config = config.get_llm_config()
            llm_client = ChatOpenAI(**llm_config)
            response = llm_client.invoke(prompt)

            # 解析AI响应
            response_text = response.content if hasattr(response, 'content') else str(response)
            ranked_results = []

            for line in response_text.strip().split('\n'):
                if '|' in line:
                    parts = line.split('|')
                    if len(parts) >= 2:
                        try:
                            elem_id = parts[0].strip()
                            similarity = float(parts[1].strip())
                            reason = parts[2].strip() if len(parts) > 2 else ""
                            ranked_results.append((elem_id, similarity, reason))
                        except (ValueError, IndexError):
                            continue

            return ranked_results

        except Exception as e:
            logger.error(f"AI similarity ranking failed: {e}")
            return []

    try:
        extractor = _get_dom_extractor()
        all_elements = extractor.extract_all_elements()

        # 收集所有可见的有内容的元素
        elements_data = []
        seen_texts = set()

        for elem in all_elements:
            if not elem.is_visible:
                continue

            # 提取元素的所有文本内容
            text_content = _extract_all_text_content(elem)

            if text_content and text_content not in seen_texts:
                element_info = _get_element_basic_info(elem)
                elements_data.append((elem.id, elem.xpath, text_content, element_info))
                seen_texts.add(text_content)

        if not elements_data:
            return "❌ No visible text content found in any element."

        # 如果提供了查询文本，让AI进行相似度判断和排序
        if query_text and query_text.strip():
            logger.info(f"Using AI for similarity ranking with query: {query_text}")

            ai_ranked_results = _ask_ai_for_similarity_ranking(query_text, elements_data)

            if ai_ranked_results:
                # 根据AI排序结果格式化输出
                lines = []
                for elem_id, similarity, reason in ai_ranked_results:
                    # 找到对应的元素数据
                    for orig_id, xpath, text, element_info in elements_data:
                        if str(orig_id) == str(elem_id):
                            lines.append(
                                f"[AI-Ranked] [{element_info}] {text[:200]}... [Sim: {similarity:.3f}] [Reason: {reason}] [XPath: {xpath}]")
                            break

                if lines:
                    result = "\n".join(lines)
                    logger.info(
                        f"DOM get_content_elements: AI ranked {len(lines)} relevant items for query: '{query_text}'")
                    return result

            # AI排序失败时的降级处理
            logger.warning("AI ranking failed, falling back to simple text matching")

        # 没有查询文本或AI排序失败时，返回所有元素（按文本长度排序）
        elements_data.sort(key=lambda x: -len(x[2]))  # 按文本长度降序
        elements_data = elements_data[:max_results]

        lines = []
        for elem_id, xpath, text, element_info in elements_data:
            lines.append(f"[Content] [{element_info}] {text[:200]}... [XPath: {xpath}]")

        result = "\n".join(lines)
        logger.info(
            f"DOM get_content_elements: Found {len(lines)} visible text items from {len(all_elements)} total elements.")
        return result

    except Exception as e:
        error_msg = f"Failed to extract content elements: {str(e)}"
        logger.error(f"DOM get_content_elements error: {error_msg}")
        return f"❌ {error_msg}"


# =============================================================================
# FUNCTIONAL LAYER - By user intent
# =============================================================================

@tool
def dom_get_clickable_elements(
        exclude_links: bool = False
) -> str:
    """
    Get all clickable elements from the current page.

    Intelligently identifies:
    - Standard clickable elements
    - Elements with click event handlers
    - Clickable custom components

    Args:
        exclude_links: Whether to exclude link elements

    Returns:
        A formatted string listing all clickable elements.
    """
    try:
        extractor = _get_dom_extractor()
        all_elements = extractor.extract_all_elements()

        clickable_elements = []
        for elem in all_elements:
            if not elem.is_visible:
                continue

            tag_name = elem.tag_name.lower()
            is_clickable = False

            # Standard clickable elements
            if tag_name in ['button', 'a']:
                if not (exclude_links and tag_name == 'a'):
                    is_clickable = True
            elif tag_name == 'input' and elem.attributes.get('type', '').lower() in ['button', 'submit', 'reset',
                                                                                     'checkbox', 'radio']:
                is_clickable = True
            elif elem.attributes.get('role') in ['button', 'link', 'menuitem', 'tab', 'option']:
                is_clickable = True
            elif elem.attributes.get('onclick') or elem.attributes.get('data-action'):
                is_clickable = True
            elif elem.is_interactive:  # Trust JavaScript detection
                is_clickable = True

            # Additional heuristics for custom clickable elements
            if not is_clickable:
                classes = elem.attributes.get('class', '').lower()
                if any(clickable_class in classes for clickable_class in ['btn', 'button', 'clickable', 'click']):
                    is_clickable = True

            if is_clickable:
                clickable_elements.append(elem)

        result = DOMFormatter.format_clickable_elements(clickable_elements)
        logger.info(f"DOM get_clickable_elements: Found {len(clickable_elements)} clickable elements")
        return result

    except Exception as e:
        error_msg = f"Failed to get clickable elements: {str(e)}"
        logger.error(f"DOM get_clickable_elements error: {error_msg}")
        return f"❌ {error_msg}"


@tool
def dom_get_focusable_elements() -> str:
    """
    Get all focusable elements from the current page.

    Includes:
    - Elements with tabindex >= 0
    - Form controls
    - Links and buttons

    Returns:
        A formatted string listing all focusable elements.
    """
    try:
        extractor = _get_dom_extractor()
        all_elements = extractor.extract_all_elements()

        focusable_elements = []
        for elem in all_elements:
            if not elem.is_visible:
                continue

            tag_name = elem.tag_name.lower()
            is_focusable = False

            # Elements with explicit tabindex
            tabindex = elem.attributes.get('tabindex')
            if tabindex is not None:
                try:
                    if int(tabindex) >= 0:
                        is_focusable = True
                except ValueError:
                    pass

            # Naturally focusable elements
            if tag_name in ['input', 'textarea', 'select', 'button', 'a']:
                # Check if not disabled
                if elem.attributes.get('disabled') is None:
                    is_focusable = True
            elif elem.attributes.get('contenteditable') == 'true':
                is_focusable = True

            if is_focusable:
                focusable_elements.append(elem)

        result = DOMFormatter.format_focusable_elements(focusable_elements)
        logger.info(f"DOM get_focusable_elements: Found {len(focusable_elements)} focusable elements")
        return result

    except Exception as e:
        error_msg = f"Failed to get focusable elements: {str(e)}"
        logger.error(f"DOM get_focusable_elements error: {error_msg}")
        return f"❌ {error_msg}"


@tool
def dom_get_interactive_elements_smart() -> str:
    """
    Get all interactive elements using the most intelligent detection.

    This is the smartest function that combines multiple detection strategies:
    - HTML semantics
    - ARIA roles
    - Event listeners
    - Visual characteristics
    - Context analysis

    Returns:
        A formatted string listing all interactive elements sorted by priority.
    """
    try:
        extractor = _get_dom_extractor()
        all_elements = extractor.extract_all_elements()

        interactive_elements = []
        for elem in all_elements:
            if not elem.is_visible:
                continue

            # Use the JavaScript is_interactive as primary filter
            if elem.is_interactive:
                interactive_elements.append(elem)
                continue

            # Additional smart detection for elements that might be missed
            tag_name = elem.tag_name.lower()

            # High-confidence interactive elements
            if tag_name in ['a', 'button', 'input', 'select', 'textarea']:
                if elem.attributes.get('disabled') is None:
                    interactive_elements.append(elem)
                    continue

            # Elements with clear interactive attributes
            if (elem.attributes.get('onclick') or
                    elem.attributes.get('role') in ['button', 'link', 'menuitem', 'tab', 'option', 'checkbox',
                                                    'radio'] or
                    elem.attributes.get('tabindex') is not None or
                    elem.attributes.get('data-action') or
                    elem.attributes.get('data-toggle') or
                    elem.attributes.get('aria-haspopup')):
                interactive_elements.append(elem)
                continue

            # Context-based detection
            classes = elem.attributes.get('class', '').lower()
            if any(interactive_class in classes for interactive_class in
                   ['btn', 'button', 'clickable', 'menu-item', 'nav-link', 'dropdown-toggle']):
                interactive_elements.append(elem)

        result = DOMFormatter.format_interactive_elements(interactive_elements)
        logger.info(f"DOM get_interactive_elements_smart: Found {len(interactive_elements)} interactive elements")
        return result

    except Exception as e:
        error_msg = f"Failed to get interactive elements: {str(e)}"
        logger.error(f"DOM get_interactive_elements_smart error: {error_msg}")
        return f"❌ {error_msg}"


# =============================================================================
# ANALYSIS LAYER - Information quality assessment
# =============================================================================

@tool
def dom_assess_information_completeness(
        user_query: str,
        current_results_count: int,
        check_navigation: bool = True
) -> str:
    """
    使用LLM智能评估当前页面信息的完整性。

    采用直接LLM评估方法，能够：
    1. 理解用户查询的真实意图和期望
    2. 分析当前结果是否满足完整性要求
    3. 检测页面中可能存在更多数据的线索
    4. 提供具体的改进建议

    Args:
        user_query: 用户的原始查询文本
        current_results_count: 当前获取到的结果数量
        check_navigation: 是否检查导航元素（默认True）

    Returns:
        LLM评估的完整性分析结果
    """
    try:
        from langchain_openai import ChatOpenAI
        from ..core.config import config

        # 获取页面上下文信息
        page_context = ""
        if check_navigation:
            try:
                # 获取可能的导航线索
                extractor = _get_dom_extractor()
                all_elements = extractor.extract_all_elements()

                navigation_clues = []
                for elem in all_elements[:50]:  # 限制检查前50个元素以提高性能
                    if not elem.is_visible:
                        continue

                    text = elem.text_content.strip().lower() if elem.text_content else ""
                    if any(keyword in text for keyword in [
                        "更多", "下一页", "查看全部", "完整", "展开", "加载更多",
                        "next", "more", "load more", "show all", "view all", "expand"
                    ]):
                        navigation_clues.append(f"- {elem.tag_name}: '{elem.text_content.strip()[:50]}'")

                if navigation_clues:
                    page_context = f"页面中发现可能的导航线索：\n" + "\n".join(navigation_clues[:5])
                else:
                    page_context = "页面中未发现明显的导航线索"

            except Exception as e:
                page_context = f"无法获取页面上下文：{str(e)}"

        # 构建LLM评估提示
        prompt = f"""
你是一个数据完整性评估专家。请分析以下信息提取任务的完整性：

**用户查询**：{user_query}
**当前结果数量**：{current_results_count}
**页面上下文**：{page_context}

请从以下维度评估数据完整性：

1. **用户期望分析**：用户真正想要什么信息？期望多少数据？
2. **字段完整性**：用户要求的字段/属性是否都能从当前结果中获得？
3. **数量充分性**：当前数据量是否满足用户的"完整"要求？
4. **扩展可能性**：页面是否暗示还有更多相关数据？

**输出要求**：
- 如果信息完整，输出：COMPLETE - [简要说明原因]
- 如果信息不完整，输出：INCOMPLETE - [具体说明缺失什么] - [建议下一步操作]

**分析要点**：
- 注意"完整榜单"、"全部"、"所有"等关键词
- 考虑用户查询中明确或隐含的数量期望
- 分析页面线索是否暗示有更多数据
- 评估数据质量是否符合用户需求

请给出你的评估结果：
"""

        # 调用LLM进行评估
        llm_config = config.get_llm_config()
        llm_client = ChatOpenAI(**llm_config)
        response = llm_client.invoke(prompt)

        # 提取响应内容
        response_text = response.content if hasattr(response, 'content') else str(response)

        # 格式化返回结果
        if response_text.strip().upper().startswith('COMPLETE'):
            result = f"✅ 信息完整性评估：{response_text}"
        else:
            result = f"❌ 信息完整性评估：{response_text}"

        logger.info(
            f"DOM assess_information_completeness: Query='{user_query}', Count={current_results_count}, Result='{response_text[:100]}'")
        return result

    except Exception as e:
        error_msg = f"Failed to assess information completeness: {str(e)}"
        logger.error(f"DOM assess_information_completeness error: {error_msg}")
        return f"❌ {error_msg}"


# =============================================================================
# ENHANCED TOOLS LIST
# =============================================================================

ENHANCED_DOM_TOOLS = [
    # Classification Layer - By HTML tag
    dom_get_buttons_enhanced,
    dom_get_inputs_enhanced,
    dom_get_links_enhanced,

    # Classification Layer - By functional role
    dom_get_form_elements,
    dom_get_navigation_elements,
    dom_get_media_elements,
    dom_get_content_elements,

    # Functional Layer - By user intent
    dom_get_clickable_elements,
    dom_get_focusable_elements,
    dom_get_interactive_elements_smart,

    # Analysis Layer - Information quality assessment
    dom_assess_information_completeness,
]


def get_enhanced_dom_tools() -> List:
    """
    Get a list of enhanced DOM tools with layered architecture.

    Returns:
        A list of enhanced DOM tool functions.
    """
    return ENHANCED_DOM_TOOLS.copy()
