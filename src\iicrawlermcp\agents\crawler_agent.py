"""
Agent module for iICrawlerMCP.

This module provides functionality to build and configure LangChain agents
with browser automation tools.
"""

import logging
from typing import Optional, List
from langchain import hub
from langchain.agents import create_openai_functions_agent, AgentExecutor
from langchain_openai import ChatOpenAI
from langchain_core.tools import BaseTool

from ..core.config import config
from ..tools.delegation_tools import get_delegation_tools
from .browser_agent import BrowserAgent

logger = logging.getLogger(__name__)


class CrawlerAgent:
    """
    A LangChain agent configured for web crawling tasks.
    
    This class encapsulates the creation and management of a LangChain agent
    that can perform web automation tasks using browser tools.
    """
    
    def __init__(
        self, 
        tools: Optional[List[BaseTool]] = None,
        verbose: Optional[bool] = None,
        llm_config: Optional[dict] = None
    ):
        """
        Initialize the CrawlerAgent.
        
        Args:
            tools: List of LangChain tools to use. If None, uses default tools.
            verbose: Whether to enable verbose logging. If None, uses config default.
            llm_config: Custom LLM configuration. If None, uses config defaults.
        """
        # 使用精选工具集，避免功能重复，强制使用智能委托
        if tools is None:
            # 精选浏览器工具 + 智能委托工具 = 无重复的完整工具集
            curated_browser_tools = self._get_curated_browser_tools()
            delegation_tools = get_delegation_tools()
            self.tools = curated_browser_tools + delegation_tools

            logger.info(f"CrawlerAgent initialized with curated tools:")
            logger.info(f"  - Browser tools: {len(curated_browser_tools)}")
            logger.info(f"  - Delegation tools: {len(delegation_tools)}")
            logger.info(f"  - Total tools: {len(self.tools)}")
        else:
            self.tools = tools
        self.verbose = verbose if verbose is not None else config.VERBOSE
        self.llm_config = llm_config or config.get_llm_config()
        
        self._llm = None
        self._agent = None
        self._executor = None
        self._browser_agent = None
        self._element_agent = None

    def _get_curated_browser_tools(self) -> List[BaseTool]:
        """
        CrawlerAgent作为纯协调者，不直接使用浏览器工具。

        基于多Agent系统最佳实践（Anthropic、Microsoft Azure）：
        1. CrawlerAgent专注任务分解和协调
        2. 所有浏览器操作通过委托工具转发给专门Agent
        3. 实现"Orchestrator Pattern" - 纯协调者模式

        所有浏览器操作通过智能委托实现：
        - 浏览器操作 → smart_browser_action_finder → BrowserAgent
        - 元素操作 → smart_element_finder → ElementAgent
        - 未来扩展 → 新的委托工具 → 新的专门Agent

        Returns:
            空列表 - CrawlerAgent不直接使用浏览器工具
        """
        # CrawlerAgent作为纯协调者，不直接操作浏览器
        # 所有操作通过delegation tools委托给专门Agent
        return []
    
    def _create_llm(self) -> ChatOpenAI:
        """Create and configure the LLM instance."""
        if self._llm is None:
            try:
                self._llm = ChatOpenAI(**self.llm_config)
                logger.info(f"LLM created with model: {self.llm_config.get('model')}")
            except Exception as e:
                logger.error(f"Failed to create LLM: {e}")
                raise
        return self._llm
    
    def _create_agent(self) -> None:
        """Create the LangChain agent."""
        if self._agent is None:
            try:
                llm = self._create_llm()
                prompt = hub.pull("hwchase17/openai-functions-agent")
                self._agent = create_openai_functions_agent(llm, self.tools, prompt)
                logger.info("Agent created successfully")
            except Exception as e:
                logger.error(f"Failed to create agent: {e}")
                raise
    
    def _create_executor(self) -> AgentExecutor:
        """Create the agent executor."""
        if self._executor is None:
            self._create_agent()
            try:
                self._executor = AgentExecutor(
                    agent=self._agent,
                    tools=self.tools,
                    verbose=self.verbose,
                    max_iterations=50,  # 增加最大迭代次数，支持复杂任务
                    handle_parsing_errors=True
                )
                logger.info("Agent executor created successfully")
            except Exception as e:
                logger.error(f"Failed to create agent executor: {e}")
                raise
        return self._executor
    
    def invoke(self, input_text: str) -> dict:
        """
        Execute a task using the agent.
        
        Args:
            input_text: The task description or instruction for the agent.
            
        Returns:
            A dictionary containing the agent's response and output.
        """
        executor = self._create_executor()
        
        try:
            logger.info(f"Executing task: {input_text}")
            result = executor.invoke({"input": input_text})
            logger.info("Task completed successfully")
            return result
        except Exception as e:
            logger.error(f"Task execution failed: {e}")
            raise
    
    def get_browser_agent(self) -> BrowserAgent:
        """
        Get or create a BrowserAgent for browser-specific operations.

        Returns:
            A BrowserAgent instance for browser automation tasks.
        """
        if self._browser_agent is None:
            self._browser_agent = BrowserAgent(
                verbose=self.verbose,
                llm_config=self.llm_config
            )
        return self._browser_agent

    def delegate_browser_task(self, task: str) -> dict:
        """
        Delegate a browser-specific task to the BrowserAgent.

        Args:
            task: Browser automation task description

        Returns:
            Result from the BrowserAgent
        """
        try:
            browser_agent = self.get_browser_agent()
            result = browser_agent.invoke(task)
            logger.info(f"Browser task delegated successfully: {task}")
            return result
        except Exception as e:
            logger.error(f"Browser task delegation failed: {e}")
            raise

    def get_element_agent(self):
        """
        Get or create an ElementAgent for element-specific operations.

        Returns:
            An ElementAgent instance for DOM element analysis and manipulation.
        """
        if self._element_agent is None:
            # Import here to avoid circular imports
            from .element_agent import ElementAgent
            self._element_agent = ElementAgent(
                verbose=self.verbose,
                llm_config=self.llm_config
            )
        return self._element_agent

    def delegate_element_task(self, task: str) -> dict:
        """
        Delegate an element-specific task to the ElementAgent.

        Args:
            task: Element analysis or manipulation task description

        Returns:
            Result from the ElementAgent
        """
        try:
            element_agent = self.get_element_agent()
            result = element_agent.invoke(task)
            logger.info(f"Element task delegated successfully: {task}")
            return result
        except Exception as e:
            logger.error(f"Element task delegation failed: {e}")
            raise

    def cleanup(self) -> None:
        """Clean up resources used by the agent."""
        try:
            # Clean up browser agent first
            if self._browser_agent:
                self._browser_agent.cleanup()
                self._browser_agent = None

            # Clean up element agent
            if self._element_agent:
                self._element_agent.cleanup()
                self._element_agent = None

            # Clean up general tools
            from ..tools.browser_tools import cleanup_tools
            cleanup_tools()
            logger.info("Agent cleanup completed")
        except Exception as e:
            logger.error(f"Error during agent cleanup: {e}")


def build_agent(
    tools: Optional[List[BaseTool]] = None,
    verbose: Optional[bool] = None,
    llm_config: Optional[dict] = None
) -> CrawlerAgent:
    """
    Build and return a configured CrawlerAgent instance.
    
    Args:
        tools: List of LangChain tools to use. If None, uses default tools.
        verbose: Whether to enable verbose logging. If None, uses config default.
        llm_config: Custom LLM configuration. If None, uses config defaults.
        
    Returns:
        A configured CrawlerAgent instance.
        
    Example:
        agent = build_agent()
        result = agent.invoke("Navigate to https://google.com and take a screenshot")
    """
    # Validate configuration before creating agent
    config.validate()
    
    try:
        agent = CrawlerAgent(tools=tools, verbose=verbose, llm_config=llm_config)
        logger.info("Agent built successfully")
        return agent
    except Exception as e:
        logger.error(f"Failed to build agent: {e}")
        raise


def create_simple_agent() -> AgentExecutor:
    """
    Create a simple AgentExecutor for backward compatibility.
    
    Returns:
        An AgentExecutor instance configured with default settings.
        
    Note:
        This function is provided for backward compatibility.
        Consider using build_agent() for new code.
    """
    agent = build_agent()
    return agent._create_executor()
