# iICrawlerMCP

一个基于LangGraph和Playwright的智能网页爬虫系统，正在从多Agent协作架构升级为完整的智能工作流系统，提供从自然语言到可执行代码的端到端自动化能力。

## 🚀 项目状态

### 当前版本 (v1.0) - 多Agent协作系统
✅ **已完成**: 基于LangChain的多Agent协作架构，支持复杂网页自动化任务

### 目标版本 (v2.0) - LangGraph智能工作流系统
🔄 **开发中**: 基于LangGraph的完整智能爬虫工作流，实现从任务理解到代码生成的全流程自动化

```
用户输入: "帮我爬取淘宝上iPhone的价格信息"
    ↓
系统输出: 完整的Python爬虫代码 + 执行结果 + 数据文件
```

## ✨ 核心特性

### v1.0 特性 (当前)
- 🤖 **多Agent协作架构**: CrawlerAgent、BrowserAgent、ElementAgent智能协作
- 🌐 **智能浏览器控制**: 基于Playwright的强大浏览器自动化能力
- 🧠 **LLM驱动决策**: 使用OpenAI GPT进行智能任务理解和执行
- 🔧 **统一工具包**: 重构后的BrowserToolkit提供分层工具访问
- 📊 **增强DOM分析**: 精确的DOM元素提取和智能分析
- 🔄 **函数式移交**: 支持OpenAI Swarm风格的Agent间协作
- 🛡️ **类型安全**: 完整的类型提示，提供更好的开发体验
- 🧪 **全面测试**: 单元测试、集成测试和端到端测试覆盖
- 🎨 **Streamlit UI**: 图形化界面，实时显示Agent思考过程和执行结果

### v2.0 特性 (规划中)
- 🧠 **任务理解**: 自然语言任务解析和执行计划生成
- 📹 **操作录制**: 实时操作录制和智能序列生成
- 🏗️ **代码生成**: 基于录制自动生成Python爬虫代码
- ▶️ **执行验证**: 代码执行、结果验证和错误修复
- 🔄 **状态管理**: 基于LangGraph的统一状态管理
- ⚡ **并行执行**: 支持多节点并行处理
- 🎯 **智能路由**: 条件路由和动态工作流调整

## 🏗️ 系统架构

### 当前架构 (v1.0) - 多Agent协作系统
```
┌─────────────────────────────────────┐
│        CrawlerAgent (主协调)          │
│     - 任务分解和协调                   │
│     - Agent间智能移交                  │
│     - 高级策略决策                     │
└─────────────────────────────────────┘
           │
    ┌──────┼──────┬──────┐
    │      │      │      │
┌───▼───┐ ┌▼───┐ ┌▼───┐ ┌▼───┐
│Browser│ │Element│ │Handoff│ │...│
│Agent  │ │Agent │ │Engine │ │   │
│✅实现 │ │✅实现 │ │✅实现 │ │   │
└───────┘ └────┘ └────┘ └───┘
    │       │      │
┌───▼───────▼──────▼───────────┐
│         统一工具层             │
│  BrowserToolkit + DOM Tools  │
└─────────────────────────────┘
```

### 目标架构 (v2.0) - LangGraph智能工作流系统
```
用户需求 → TaskAgent → RecordAgent → CodeGenAgent → ExecAgent → 结果输出
    ↓         ↓           ↓            ↓           ↓         ↓
  任务理解   操作录制    代码生成      执行验证    数据入库   完整产品

┌─────────────────────────────────────────────────────────┐
│                 LangGraph工作流引擎                      │
│  状态管理 • 智能路由 • 并行执行 • 错误恢复               │
└─────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────┐
│                   智能Agent节点                          │
│  TaskAgent • RecordAgent • CodeGenAgent • ExecAgent    │
│  + 现有Agent包装 (CrawlerNode • BrowserNode • ElementNode) │
└─────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────┐
│                    工具服务层                            │
│  BrowserToolkit • DOM Tools • Code Templates           │
└─────────────────────────────────────────────────────────┘
```

### 核心组件

#### 🤖 当前Agent层 (v1.0)
- **CrawlerAgent**: 主协调Agent，处理复杂多步骤任务
- **BrowserAgent**: 浏览器控制专家，专注浏览器操作
- **ElementAgent**: DOM分析专家，智能元素发现和交互

#### 🧠 新Agent层 (v2.0 规划中)
- **TaskAgent**: 任务理解专家，自然语言解析和计划生成
- **RecordAgent**: 操作录制专家，实时操作捕获和序列化
- **CodeGenAgent**: 代码生成专家，基于模板的代码自动生成
- **ExecAgent**: 执行验证专家，代码执行和结果验证

#### 🔧 工具层
- **BrowserToolkit**: 统一的浏览器工具包
  - 基础工具: 6个简单可靠的工具（专门Agent使用）
  - 高级工具: 10个功能丰富的工具（通用Agent使用）
- **DOM Tools Enhanced**: 精确的DOM分析工具
- **Delegation Tools**: Agent间委托和移交工具
- **Code Templates**: 代码生成模板库 (v2.0新增)

#### 🏠 核心层
- **Browser**: Playwright浏览器封装
- **DOMExtractor**: DOM元素提取和分析
- **HandoffEngine**: Agent间移交引擎 (v1.0)
- **LangGraph Workflow**: 智能工作流引擎 (v2.0新增)
- **Config**: 统一配置管理

## 🚀 快速安装

### 📋 前置要求

- Python 3.11 或更高版本
- Node.js (用于Playwright浏览器安装)
- OpenAI API Key

### 📦 一键安装

```bash
# 1. 克隆项目
git clone https://git.dev.sh.ctripcorp.com/octopus/icrawlermcp.git
cd icrawlermcp

# 2. 安装依赖
pip install -r requirements.txt
playwright install

# 3. 配置环境
echo "OPENAI_API_KEY=your_openai_api_key_here" > .env

# 4. 验证安装
python -c "from iicrawlermcp.agents import build_agent; print('✅ 安装成功!')"
```

### ⚙️ 环境配置

在项目根目录创建 `.env` 文件:

```env
# 必需配置
OPENAI_API_KEY=your_openai_api_key_here

# 可选配置
OPENAI_MODEL=gpt-4-turbo          # 模型选择
HEADLESS=true                     # 无头模式
VERBOSE=true                      # 详细日志
DEFAULT_SCREENSHOT_PATH=screenshots/  # 截图路径
```

## ⚙️ 配置说明

在项目根目录创建 `.env` 文件进行配置:

```env
# 必需: OpenAI API配置
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_API_BASE=https://api.openai.com/v1  # 可选: 自定义API地址
OPENAI_MODEL=gpt-4-turbo  # 可选: 模型名称

# 可选: 浏览器配置
HEADLESS=true  # 无头模式运行浏览器
BROWSER_TIMEOUT=60000  # 浏览器超时时间(毫秒)

# 可选: 应用配置
DEFAULT_SCREENSHOT_PATH=screenshots/  # 默认截图路径
VERBOSE=true  # 启用详细日志
```

## 🎯 快速开始

### 🎨 方式1: Streamlit UI（推荐新用户）

```bash
# 安装 Streamlit 依赖
pip install streamlit>=1.28.0 langchain-community>=0.0.20

# 启动图形化界面
streamlit run src/iicrawlermcp/ui/app.py

# 访问界面: http://localhost:8501
```

**特性**:
- 🤔 实时显示 Agent 思考过程
- 📊 可视化工具调用和执行结果
- 📸 自动展示截图和输出
- ⚙️ 配置信息一目了然

### 🤖 方式2: Python API（适合开发者）

```python
from iicrawlermcp.agents import build_agent

# 创建主协调Agent
agent = build_agent()

# 执行复杂的智能任务 - 自动选择最合适的Agent
result = agent.invoke("""
    导航到google.com，搜索'迪士尼乐园'，
    点击第一个非广告结果，然后截图保存
""")

print(result["output"])
agent.cleanup()
```

### 🛰️ 方式3: MCP 服务器（适合集成）

```bash
# 启动 MCP 服务器
python src/iicrawlermcp/mcp/run_server.py sse --host 127.0.0.1 --port 8000

# 使用 MCP 客户端调用
python examples/mcp/call_mcp.py
```

### 🌐 专门Agent使用

```python
from iicrawlermcp.agents import build_browser_agent, build_element_agent

# 浏览器控制专家 - 专注浏览器操作
browser_agent = build_browser_agent()
browser_agent.invoke("导航到https://google.com并截图")

# DOM分析专家 - 专注元素分析
element_agent = build_element_agent()
element_agent.invoke("分析页面上的所有按钮和输入框")
```

### 🔧 工具包直接使用

```python
from iicrawlermcp.tools.browser_tools import BrowserToolkit
from iicrawlermcp.core.browser import get_global_browser

# 方式1: 使用工具包
basic_tools = BrowserToolkit.get_basic_tools()
advanced_tools = BrowserToolkit.get_advanced_tools()

# 方式2: 直接使用浏览器
browser = get_global_browser()
browser.navigate("https://example.com")
screenshot_path = browser.screenshot()
print(f"截图保存到: {screenshot_path}")
```

### 📱 示例和测试

```bash
# 运行基础示例
python examples/basic/browser_basics.py

# 运行高级示例
python examples/advanced/multi_agent_collaboration.py

# 运行测试
pytest tests/
```

### 🛰️ MCP 服务器启动与调用

> v1.0 起，本项目内置 **MCP (Model Context Protocol) Server**，
> 你可以把多-Agent 能力通过标准协议暴露为一个远程服务，
> 供外部应用或其它语言调用。

#### 1. 启动服务器

```bash
# 进入项目根目录
cd iICrawlerMCP

# （1）本机调试，仅监听 127.0.0.1:8000，SSE 传输
python src/iicrawlermcp/mcp/run_server.py sse --host 127.0.0.1 --port 8000

# （2）局域网 / 公网可访问，监听全部网卡
python src/iicrawlermcp/mcp/run_server.py sse --host 0.0.0.0 --port 8000

# （3）Streamable-HTTP 方式（可选）
python src/iicrawlermcp/mcp/run_server.py http --host 0.0.0.0 --port 8000
```

启动后：

* SSE 地址: `http://<host>:8000/sse`
* HTTP 地址: `http://<host>:8000/mcp`

#### 2. 使用示例脚本调用 MCP

项目已提供 `examples/mcp/call_mcp.py`，默认连接 `http://127.0.0.1:8000/sse`，并调用公开的 `intelligent_web_task`。

```bash
python examples/mcp/call_mcp.py
# 预期输出：
# [MCP 返回结果]
#  ... 智能任务执行结果 ...
```

#### 3. 可视化调试 (MCP Inspector)

如果你想在浏览器里实时查看流式输出，可以使用官方 GUI：

```bash
npx @modelcontextprotocol/inspector
# 传入 SSE URL → Connect
```

#### 4. 停止服务

按 `Ctrl + C` 即可安全关闭服务器，浏览器实例会自动在后台线程中清理。

## 📁 项目结构

### 当前结构 (v1.0)
```
iICrawlerMCP/
├── src/iicrawlermcp/           # 🏠 主包
│   ├── agents/                 # 🤖 Agent实现
│   │   ├── crawler_agent.py    #   主协调Agent
│   │   ├── browser_agent.py    #   浏览器专家Agent
│   │   └── element_agent.py    #   DOM分析专家Agent
│   ├── tools/                  # 🔧 工具层
│   │   ├── browser_tools.py    #   统一浏览器工具包
│   │   ├── dom_tools_enhanced.py #   增强DOM工具
│   │   └── delegation_tools.py #   Agent委托工具
│   ├── core/                   # ⚙️ 核心层
│   │   ├── browser.py          #   浏览器封装
│   │   ├── handoff_engine.py   #   Agent移交引擎
│   │   └── config.py           #   配置管理
│   └── dom/                    # 📄 DOM处理
│       └── extractor.py        #   DOM提取器
├── examples/                   # 💡 示例代码
├── tests/                      # 🧪 测试代码
├── screenshots/                # 📸 截图输出
├── ARCHITECTURE.md             # 🏗️ 架构设计文档
├── API_REFERENCE.md            # 📋 API参考文档
├── DEVELOPMENT.md              # 🛠️ 开发指南
├── IMPLEMENTATION_ROADMAP.md   # 🚀 实施路线图
└── main.py                     # 🚀 主程序入口
```

### 目标结构 (v2.0)
```
iICrawlerMCP/
├── src/iicrawlermcp/
│   ├── agents/                 # 🤖 Agent实现
│   │   ├── crawler_agent.py    #   主协调Agent (现有)
│   │   ├── browser_agent.py    #   浏览器专家Agent (现有)
│   │   ├── element_agent.py    #   DOM分析专家Agent (现有)
│   │   ├── task_agent.py       #   任务理解专家 (新增)
│   │   ├── record_agent.py     #   操作录制专家 (新增)
│   │   ├── codegen_agent.py    #   代码生成专家 (新增)
│   │   └── exec_agent.py       #   执行验证专家 (新增)
│   ├── workflows/              # 🔄 LangGraph工作流 (新增)
│   │   ├── state.py            #   状态定义
│   │   ├── base_workflow.py    #   基础工作流
│   │   └── nodes/              #   工作流节点
│   ├── tools/                  # 🔧 工具层
│   │   ├── browser_tools.py    #   统一浏览器工具包 (现有)
│   │   ├── dom_tools_enhanced.py #   增强DOM工具 (现有)
│   │   ├── delegation_tools.py #   Agent委托工具 (现有)
│   │   └── templates/          #   代码模板库 (新增)
│   ├── core/                   # ⚙️ 核心层
│   │   ├── browser.py          #   浏览器封装 (现有)
│   │   ├── handoff_engine.py   #   Agent移交引擎 (现有)
│   │   ├── workflow_engine.py  #   工作流引擎 (新增)
│   │   └── config.py           #   配置管理 (现有)
│   └── dom/                    # 📄 DOM处理
│       └── extractor.py        #   DOM提取器 (现有)
├── examples/                   # 💡 示例代码
│   ├── v1_examples/            #   v1.0示例
│   └── v2_examples/            #   v2.0示例 (新增)
├── tests/                      # 🧪 测试代码
├── docs/                       # 📚 文档 (新增)
│   ├── migration_guide.md      #   迁移指南
│   └── workflow_examples.md    #   工作流示例
└── IMPLEMENTATION_ROADMAP.md   # 🚀 实施路线图
```

### 📚 核心文档

| 文档 | 描述 | 适用人群 |
|------|------|----------|
| [📖 文档中心](docs/README.md) | 所有文档的导航和索引 | 所有用户 |
| [🚀 快速上手](docs/user-guide/quick-start.md) | 5分钟快速体验指南 | 新用户 |
| [🎨 Streamlit UI 指南](docs/user-guide/streamlit-ui.md) | 图形化界面使用和开发指南 | 新用户、开发者 |
| [🏗️ 系统架构](docs/architecture/system-architecture.md) | 系统架构和技术实现 | 开发者、架构师 |
| [🎯 LangGraph分层监督架构](docs/architecture/langgraph-hierarchical.md) | 推荐的新架构方案 | 架构师、技术决策者 |
| [📋 项目路线图](docs/project/status-and-roadmap.md) | 项目状态和未来规划 | 项目管理者、开发者 |
| [🔧 开发指南](docs/development/setup.md) | 开发环境搭建和贡献说明 | 贡献者 |

## 🚀 升级路线图

### 📅 重构时间表
- **总工期**: 6周 (2025年8月1日 - 9月12日)
- **阶段1**: LangGraph架构重构 (Week 1-2)
- **阶段2**: 新Agent开发 (Week 2-3)
- **阶段3**: 代码生成与执行 (Week 3-4)
- **阶段4**: 系统集成与优化 (Week 4-5)
- **阶段5**: 测试与文档 (Week 5-6)

### 🎯 预期成果
完成后的系统将具备：
- **智能化**: 理解自然语言，自动生成代码
- **可靠性**: 完善的错误处理和恢复机制
- **高性能**: 并行执行，资源优化
- **易用性**: 简单的API，丰富的文档
- **可扩展**: 模块化设计，易于扩展

详细信息请参考 [实施路线图](IMPLEMENTATION_ROADMAP.md)。

## 🧪 测试和验证

```bash
# 快速验证安装
python -c "from iicrawlermcp.agents import build_agent; print('✅ 系统正常')"

# 运行完整测试套件
pytest tests/ --cov=src/iicrawlermcp

# 运行示例验证功能
python examples/basic/browser_basics.py
python examples/advanced/multi_agent_collaboration.py
```

## 🎯 使用场景

### 🌐 网页自动化
- 自动化表单填写和提交
- 批量数据采集和处理
- 网站功能测试和监控

### 🤖 智能爬虫
- 动态内容抓取
- 反爬虫策略应对
- 多页面协调操作

### 📊 数据提取
- 结构化数据提取
- 图片和文件下载
- 实时数据监控

## Development

### Code Formatting

Format code with Black:
```bash
black src/ tests/
```

### Linting

Run flake8 for linting:
```bash
flake8 src/ tests/
```

### Type Checking

Run mypy for type checking:
```bash
mypy src/
```

## � 故障排除

### 常见问题

| 问题 | 解决方案 |
|------|----------|
| 🚫 Playwright浏览器未安装 | `playwright install` |
| 🔑 OpenAI API密钥错误 | 检查 `.env` 文件中的 `OPENAI_API_KEY` |
| 📦 导入错误 | 确保从项目根目录运行 |
| 🤖 Agent创建失败 | 检查配置和依赖是否正确安装 |
| 📸 截图保存失败 | 确保 `screenshots/` 目录存在且有写权限 |

### 获取帮助

- 📖 查看 [ARCHITECTURE.md](ARCHITECTURE.md) 了解系统设计
- 📋 查看 [API_REFERENCE.md](API_REFERENCE.md) 了解详细API
- 💡 运行 [examples/](examples/) 中的示例代码
- 🧪 查看 [tests/](tests/) 了解最佳实践

## 🤝 贡献指南

我们欢迎所有形式的贡献！

### 贡献方式
1. � **报告Bug**: 创建Issue描述问题
2. ✨ **功能建议**: 提出新功能想法
3. � **改进文档**: 完善文档和示例
4. � **代码贡献**: 提交Pull Request

### 开发流程
```bash
# 1. Fork并克隆项目
git clone https://git.dev.sh.ctripcorp.com/octopus/icrawlermcp.git

# 2. 创建功能分支
git checkout -b feature/amazing-feature

# 3. 安装开发依赖
pip install -r requirements-dev.txt

# 4. 进行开发和测试
pytest tests/

# 5. 提交更改
git commit -m "Add amazing feature"
git push origin feature/amazing-feature

# 6. 创建Pull Request
```

详细开发指南请查看 [DEVELOPMENT.md](DEVELOPMENT.md)

## 📄 许可证

本项目采用 MIT 许可证 - 详见 [LICENSE](LICENSE) 文件

## 🌟 致谢

感谢以下开源项目的支持：
- [LangChain](https://github.com/langchain-ai/langchain) - Agent框架
- [Playwright](https://github.com/microsoft/playwright-python) - 浏览器自动化
- [OpenAI](https://openai.com/) - 大语言模型服务

---

**iICrawlerMCP** - 让网页自动化更智能、更简单、更可靠 🚀

*如果这个项目对你有帮助，请给我们一个 ⭐ Star！*
