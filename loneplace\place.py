#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Scrape Lonely Planet /places listing with:
  • custom start–end page range
  • auto resume from existing CSV

pip install requests beautifulsoup4 tqdm
"""

import csv, json, logging, random, sys, time
from concurrent.futures import Thr<PERSON><PERSON>oolExecutor, as_completed
from pathlib import Path
from typing import List, Dict, Optional, Set

import requests
from bs4 import BeautifulSoup
from requests.adapters import <PERSON><PERSON><PERSON><PERSON>pt<PERSON>, Retry
from tqdm import tqdm

# ─── CONFIG ────────────────────────────────────────────────────────────
BASE_URL      = "https://www.lonelyplanet.com/places"
PLACE_BASE    = "https://www.lonelyplanet.com"

START_PAGE    = 1          # ← 从第几页开始
END_PAGE      = None       # ← 到第几页结束；None = 全部
MAX_WORKERS   = 12
MAX_RETRIES   = 3
BACKOFF_BASE  = 2
CSV_PATH      = Path("besttime/lonelyplanet_places.csv")

HEADERS = {
    "User-Agent": (
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) "
        "AppleWebKit/537.36 (KHTML, like Gecko) "
        "Chrome/138.0.0.0 Safari/537.36"
    ),
    "Accept-Language": "en-US,en;q=0.9",
}
# ────────────────────────────────────────────────────────────────────────


def build_session() -> requests.Session:
    sess = requests.Session()
    sess.mount(
        "https://",
        HTTPAdapter(
            max_retries=Retry(
                total=MAX_RETRIES,
                backoff_factor=1,
                status_forcelist=[429, 500, 502, 503, 504],
                raise_on_status=False,
            )
        ),
    )
    sess.headers.update(HEADERS)
    return sess


# ─── PARSERS ────────────────────────────────────────────────────────────
def parse_from_script(soup: BeautifulSoup) -> Optional[List[Dict]]:
    tag = soup.find("script", id="__NEXT_DATA__")
    if not tag or not tag.string:
        return None
    try:
        data = json.loads(tag.string)
        return data["props"]["pageProps"]["places"]["items"]
    except Exception:
        return None


def parse_from_li(soup: BeautifulSoup) -> List[Dict]:
    items = []
    for li in soup.select("ul li"):
        a = li.select_one("a.card-link")
        if not a:
            continue
        slug = a.get("href", "")
        place_url = f"{PLACE_BASE}/{slug.lstrip('/')}"
        title = (a.contents[-1] or "").strip()
        region_div = a.find("div", class_="text-sm")
        region = region_div.get_text(strip=True) if region_div else ""
        img = li.find("img")
        img_url = img["src"] if img and img.has_attr("src") else ""
        items.append(
            {
                "title": title,
                "eyebrow": region,
                "slug": slug,
                "esid": "",
                "featuredImage": {
                    "url": img_url,
                    "alt": img["alt"] if img else "",
                    "caption": "",
                },
            }
        )
    return items
# ────────────────────────────────────────────────────────────────────────


def fetch_page(page: int, sess: requests.Session) -> List[Dict]:
    for attempt in range(1, MAX_RETRIES + 1):
        try:
            r = sess.get(
                BASE_URL,
                params={"type": "All", "sort": "DESC", "page": page},
                timeout=20,
            )
            r.raise_for_status()
            soup = BeautifulSoup(r.text, "html.parser")
            return parse_from_script(soup) or parse_from_li(soup)
        except Exception as exc:
            logging.warning(f"[Page {page}] attempt {attempt} failed: {exc}")
            if attempt == MAX_RETRIES:
                raise
            time.sleep(BACKOFF_BASE ** (attempt - 1) + random.random())


# ─── CSV UTILS ──────────────────────────────────────────────────────────
def init_csv():
    if CSV_PATH.exists():
        return
    with CSV_PATH.open("w", newline="", encoding="utf-8") as f:
        csv.writer(f).writerow(
            [
                "page",
                "idx_on_page",
                "title",
                "eyebrow",
                "place_url",
                "esid",
                "image_url",
                "image_alt",
                "image_caption",
            ]
        )


def read_done_pages() -> Set[int]:
    if not CSV_PATH.exists():
        return set()
    with CSV_PATH.open("r", newline="", encoding="utf-8") as f:
        reader = csv.reader(f)
        next(reader, None)  # skip header
        return {int(row[0]) for row in reader if row and row[0].isdigit()}


def append_items(page: int, items: List[Dict]):
    with CSV_PATH.open("a", newline="", encoding="utf-8") as f:
        w = csv.writer(f)
        for idx, it in enumerate(items, 1):
            slug = it.get("slug", "")
            place_url = f"{PLACE_BASE}/{slug.lstrip('/')}"
            img = it.get("featuredImage") or {}
            w.writerow(
                [
                    page,
                    idx,
                    it.get("title"),
                    it.get("eyebrow"),
                    place_url,
                    it.get("esid", ""),
                    img.get("url"),
                    img.get("alt"),
                    img.get("caption"),
                ]
            )


# ─── MAIN ───────────────────────────────────────────────────────────────
def main():
    logging.basicConfig(
        format="[%(levelname)s] %(message)s", level=logging.INFO, stream=sys.stderr
    )

    sess = build_session()
    init_csv()
    done_pages = read_done_pages()

    # 拿首页确定总页数
    soup = BeautifulSoup(
        sess.get(BASE_URL, params={"type": "All", "sort": "DESC", "page": 1}).text,
        "html.parser",
    )
    total_pages_site = (
        json.loads(soup.find("script", id="__NEXT_DATA__").string)["props"]["pageProps"][
            "places"
        ]["pages"]
        if soup.find("script", id="__NEXT_DATA__")
        else float("inf")
    )

    end_page = END_PAGE if END_PAGE is not None else total_pages_site
    end_page = min(end_page, total_pages_site)

    pages_to_fetch = [
        p
        for p in range(START_PAGE, end_page + 1)
        if p not in done_pages
    ]
    if not pages_to_fetch:
        logging.info("Everything already scraped, nothing to do.")
        return

    logging.info(
        f"Site pages: {total_pages_site} | Range requested: {START_PAGE}-{end_page} "
        f"| Already done: {len(done_pages)} | To fetch: {len(pages_to_fetch)}"
    )

    # 并发抓取
    with ThreadPoolExecutor(max_workers=MAX_WORKERS) as pool:
        futs = {pool.submit(fetch_page, p, sess): p for p in pages_to_fetch}
        for fut in tqdm(as_completed(futs), total=len(futs), desc="Pages scraped"):
            p = futs[fut]
            try:
                items = fut.result()
                if not items:
                    logging.warning(f"[Page {p}] empty result.")
                else:
                    append_items(p, items)
            except Exception as e:
                logging.error(f"[Page {p}] FAILED: {e}")

    logging.info(f"Done! CSV at {CSV_PATH.resolve()}")


if __name__ == "__main__":
    main()
