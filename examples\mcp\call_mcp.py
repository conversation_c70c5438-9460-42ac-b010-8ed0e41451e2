import asyncio
from fastmcp import Client   # pip install fastmcp

async def _baidu_screenshot_async() -> str:
    """
    调用本地 MCP 服务器的 intelligent_web_task，
    执行“访问百度并截图”任务，返回任务结果（字符串或 JSON）。
    """
    # Client 会根据 URL 自动推断传输方式；SSE 地址以 /sse 结尾即可
    # 如果你的服务器改成 Streamable-HTTP，则使用 http://127.0.0.1:8000/mcp
    async with Client("http://127.0.0.1:8000/sse") as client:
        result = await client.call_tool(
            "intelligent_web_task",
            {"task_description": "访问百度首页,找到所有热搜,给我完整的热搜列表,"}
        )
        # FastMCP >=2.5 returns CallToolResult 对象；使用 .content 获取原始返回
        return getattr(result, "content", str(result))

def run_baidu_screenshot() -> str:
    """
    同步封装，外部直接调用即可获得结果。
    """
    return asyncio.run(_baidu_screenshot_async())


# =========== 自测 ===========
if __name__ == "__main__":
    output = run_baidu_screenshot()
    print("[MCP 返回结果]\n", output)